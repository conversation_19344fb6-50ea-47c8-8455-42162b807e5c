﻿using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class ChangeFcStatusUpdateModeRequest : JplRequest
    {
        public class ChangeFcStatusUpdateModeData
        {
            [JsonPropertyName("StatusUpdateCode")]
            public int StatusUpdateCode { get; set; }
        }
    }

    public class ChangeFcStatusUpdateModeResponse : JplResponse
    {
        public class ChangeFcStatusUpdateModeResponseData
        {
            // Empty response
        }
    }
}
