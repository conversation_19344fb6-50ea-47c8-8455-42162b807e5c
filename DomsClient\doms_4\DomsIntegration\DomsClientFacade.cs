﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Communication;
using DomsIntegration.Authentication;
using DomsIntegration.Functions;
using DomsIntegration.Models;
using DomsIntegration.Utilities;

namespace DomsIntegration
{
    /// <summary>
    /// Main DOMs client that provides access to all functionality
    /// </summary>
    public class DomsClientFacade : IDisposable
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;
        private readonly IForecourtService _forecourtService;
        private readonly IDispenseService _dispenseService;
        private bool _disposed;

        /// <summary>
        /// Gets the authentication service
        /// </summary>
        public IDomsAuthenticationService Authentication => _authService;

        /// <summary>
        /// Gets the forecourt service
        /// </summary>
        public IForecourtService Forecourt => _forecourtService;

        /// <summary>
        /// Gets the dispense service
        /// </summary>
        public IDispenseService Dispense => _dispenseService;

        /// <summary>
        /// Gets the connection status
        /// </summary>
        public bool IsConnected => _client.IsConnected;

        /// <summary>
        /// Gets the authentication status
        /// </summary>
        public bool IsAuthenticated => _authService.IsAuthenticated;

        /// <summary>
        /// Event raised when connection status changes
        /// </summary>
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged
        {
            add => _client.ConnectionStatusChanged += value;
            remove => _client.ConnectionStatusChanged -= value;
        }

        /// <summary>
        /// Event raised when a message is received
        /// </summary>
        public event EventHandler<MessageReceivedEventArgs> MessageReceived
        {
            add => _client.MessageReceived += value;
            remove => _client.MessageReceived -= value;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsClientFacade"/> class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public DomsClientFacade(ILogger logger = null)
        {
            _client = new DomsClient(logger);
            _authService = new DomsAuthenticationService(_client);
            _forecourtService = new ForecourtService(_client, _authService);
            _dispenseService = new DispenseService(_client, _authService);
        }

        /// <summary>
        /// Connects to the DOMs controller
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        public async Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default)
        {
            await _client.ConnectAsync(endpoint, cancellationToken);
        }

        /// <summary>
        /// Connects and authenticates in one operation
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="clientId">Client identifier</param>
        /// <param name="clientVersion">Client version</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if both connection and authentication were successful</returns>
        public async Task<bool> ConnectAndAuthenticateAsync(
            DomsEndpoint endpoint, 
            string username, 
            string password, 
            string clientId = null, 
            string clientVersion = null,
            CancellationToken cancellationToken = default)
        {
            await ConnectAsync(endpoint, cancellationToken);
            return await _authService.AuthenticateAsync(username, password, clientId, clientVersion, cancellationToken);
        }

        /// <summary>
        /// Disconnects from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the disconnection operation</returns>
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (_authService.IsAuthenticated)
            {
                await _authService.LogoutAsync(cancellationToken);
            }
            
            await _client.DisconnectAsync(cancellationToken);
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait(5000);
                _client?.Dispose();
                _disposed = true;
            }
        }
    }
}
