﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Messages.Requests;

namespace DomsIntegration.Core.Messages.Responses
{
    /// <summary>
    /// FcLogon response message
    /// </summary>
    public class FcLogonResponse : JplResponse
    {
        public FcLogonResponse() : base("FcLogon_resp") { }

        public new FcLogonResponseData Data { get; set; }
    }

    /// <summary>
    /// FcLogon response data
    /// </summary>
    public class FcLogonResponseData
    {
        public string CountryCode { get; set; }
        public int FcHwType { get; set; }
        public string FcHwVersionNo { get; set; }
        public int FcSwType { get; set; }
        public string FcSwVersionNo { get; set; }
        public string FcSwDate { get; set; }
        public FcSwBlock[] FcSwBlocks { get; set; }
    }

    /// <summary>
    /// Software block information
    /// </summary>
    public class FcSwBlock
    {
        public string FcSwMainBlockId { get; set; }
        public string FcSwSubBlockId { get; set; }
        public string FcSwBlockReleaseNo { get; set; }
        public string FcSwBlockCheckCode { get; set; }
    }

    /// <summary>
    /// Extended FcLogon response message
    /// </summary>
    public class FcLogonExtendedResponse : JplResponse
    {
        public FcLogonExtendedResponse() : base("FcLogon_resp", "01H") { }

        public new FcLogonExtendedResponseData Data { get; set; }
    }

    /// <summary>
    /// Extended FcLogon response data
    /// </summary>
    public class FcLogonExtendedResponseData : FcLogonResponseData
    {
        public UnsolicitedMessage[] UnsolMessages { get; set; }
    }
}
