﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Models;

namespace DomsIntegration.Core.Messages.Requests
{
    /// <summary>
    /// FC Status request
    /// </summary>
    public class FcStatusRequest : JplRequest
    {
        public FcStatusRequest() : base("FcStatus_req")
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC date and time request
    /// </summary>
    public class ChangeFcDateTimeRequest : JplRequest
    {
        public ChangeFcDateTimeRequest() : base("change_FcDateAndTime_req") { }

        public new ChangeFcDateTimeRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC date and time request data
    /// </summary>
    public class ChangeFcDateTimeRequestData
    {
        public FcDateTime FcDateAndTime { get; set; }
    }

    /// <summary>
    /// FC date and time request
    /// </summary>
    public class FcDateTimeRequest : JplRequest
    {
        public FcDateTimeRequest() : base("FcDateAndTime_req")
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC operation mode request
    /// </summary>
    public class ChangeFcOperationModeRequest : JplRequest
    {
        public ChangeFcOperationModeRequest() : base("change_FcOperationModeNo_req") { }

        public new ChangeFcOperationModeRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC operation mode request data
    /// </summary>
    public class ChangeFcOperationModeRequestData
    {
        public int FcOperationModeNo { get; set; }
    }

    /// <summary>
    /// Change FC price set request
    /// </summary>
    public class ChangeFcPriceSetRequest : JplRequest
    {
        public ChangeFcPriceSetRequest() : base("change_FcPriceSet_req", "02H") { }

        public new ChangeFcPriceSetRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC price set request data
    /// </summary>
    public class ChangeFcPriceSetRequestData
    {
        public string FcPriceSetId { get; set; }
        public string[] FcPriceGroupId { get; set; }
        public string[] FcGradeId { get; set; }
        public string[][] FcPriceGroups { get; set; }
        public string PriceSetActivationDateAndTime { get; set; }
    }

    /// <summary>
    /// FC installation status request
    /// </summary>
    public class FcInstallStatusRequest : JplRequest
    {
        public FcInstallStatusRequest() : base("FcInstallStatus_req")
        {
            Data = new { };
        }
    }
}
