﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network
{
    public partial class PssClient : IDisposable
    {
        private async Task ReadMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                    {
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    for (int i = 0; i < bytesRead; i++)
                    {
                        var currentByte = buffer[i];

                        if (currentByte == ProtocolDelimiters.STX)
                        {
                            messageBuffer.Clear();
                        }
                        else if (currentByte == ProtocolDelimiters.ETX)
                        {
                            if (messageBuffer.Count > 0)
                            {
                                var messageJson = Encoding.UTF8.GetString(messageBuffer.ToArray());
                                await ProcessReceivedMessage(messageJson);
                                messageBuffer.Clear();
                            }
                        }
                        else
                        {
                            messageBuffer.Add(currentByte);
                        }
                    }
                }
            }
            catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
            {
                OnError(ex, "Error reading messages", true);
            }
        }

        private async Task ProcessReceivedMessage(string messageJson)
        {
            try
            {
                var messageDocument = JsonDocument.Parse(messageJson);
                var root = messageDocument.RootElement;

                var name = root.GetProperty("name").GetString();
                var subCode = root.GetProperty("subCode").GetString();
                JsonElement solicitedProp;
                var solicited = root.TryGetProperty("solicited", out solicitedProp) ? solicitedProp.GetBoolean() : false;
                JsonElement corrProp;
                var correlationId = root.TryGetProperty("correlationId", out corrProp) ? corrProp.GetString() : null;

                JplMessage message = null;
                switch (name)
                {
                    case MessageNames.FC_LOGON_RESP:
                        message = JsonSerializer.Deserialize<FcLogonResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.FC_STATUS_RESP:
                        message = JsonSerializer.Deserialize<FcStatusResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.FC_DATE_TIME_RESP:
                    case MessageNames.CHANGE_FC_DATE_TIME_RESP:
                        message = JsonSerializer.Deserialize<FcDateTimeResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.HEARTBEAT:
                        message = JsonSerializer.Deserialize<HeartbeatMessage>(messageJson, _jsonOptions);
                        break;
                }

                if (message != null)
                {
                    MessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                    { 
                        Message = message, 
                        RawMessage = messageJson 
                    });

                    if (!string.IsNullOrEmpty(correlationId))
                    {
                        TaskCompletionSource<JplResponse> tcs;
                        if (_pendingRequests.TryRemove(correlationId, out tcs))
                        {
                            if (message is JplResponse response)
                            {
                                tcs.SetResult(response);
                            }
                        }
                    }
                    else if (!solicited)
                    {
                        UnsolicitedMessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                        { 
                            Message = message, 
                            RawMessage = messageJson 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                OnError(ex, $"Error processing message: {messageJson}", false);
            }
        }

        private void OnHeartbeatTimer(object state)
        {
            try
            {
                if (!IsConnected)
                    return;

                var timeSinceLastMessage = DateTime.UtcNow - _lastMessageReceived;
                if (timeSinceLastMessage.TotalMilliseconds > (_config.HeartbeatIntervalMs * _config.MaxHeartbeatMissedCount))
                {
                    OnError(new TimeoutException("Heartbeat timeout"), "Connection lost - no heartbeat", true);
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        await SendHeartbeatAsync(_cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        OnError(ex, "Failed to send heartbeat", false);
                    }
                });
            }
            catch (Exception ex)
            {
                OnError(ex, "Error in heartbeat timer", false);
            }
        }

        private bool ValidateServerCertificate(object sender, X509Certificate certificate, 
            X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            if (!_config.ValidateServerCertificate)
                return true;

            return sslPolicyErrors == SslPolicyErrors.None;
        }

        private void OnError(Exception exception, string message, bool isFatal)
        {
            ErrorOccurred?.Invoke(this, new ErrorEventArgs
            {
                Exception = exception,
                Message = message,
                IsFatal = isFatal
            });

            if (isFatal)
            {
                State = ConnectionState.Error;
                Task.Run(async () => await DisconnectAsync());
            }
        }
    }
}
