using System;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class PricesForm : Form
    {
        public int PriceDecimalPointPos { get; set; }
        public Forecourt Forecourt { get; set; }
        public object FcPriceSetResponse { get; set; }
        public Forecourt.OperationCompletedDelegate PricesSetCallback { get; set; }

        public PricesForm()
        {
            InitializeComponent();
        }

        private void PricesForm_Load(object sender, EventArgs e)
        {
            // Load price data into form
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            // Save price changes
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
