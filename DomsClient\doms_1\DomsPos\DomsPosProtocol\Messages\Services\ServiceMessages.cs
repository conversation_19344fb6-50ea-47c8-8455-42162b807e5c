﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Service Message Classes
    public class FcServiceMsgRequest : JplRequest
    {
        public class FcServiceMsgData
        {
            // Empty for basic requests
        }
    }

    public class FcServiceMsgResponse : JplResponse
    {
        public class FcServiceMsgResponseData
        {
            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }

            [JsonPropertyName("FcServiceMsg")]
            public string FcServiceMsg { get; set; }
        }
    }

    public class ClearFcServiceMsgRequest : JplRequest
    {
        public class ClearFcServiceMsgData
        {
            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }
        }
    }

    public class ClearFcServiceMsgResponse : JplResponse
    {
        public class ClearFcServiceMsgResponseData
        {
            [JsonPropertyName("FcServiceLogStatus")]
            public BitFlags FcServiceLogStatus { get; set; }
        }
    }
}
