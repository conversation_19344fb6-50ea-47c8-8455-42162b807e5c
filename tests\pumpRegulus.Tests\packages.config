﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Castle.Core" version="5.1.1" targetFramework="net48" />
  <package id="DataAccess" version="5.0.0.3" targetFramework="net48" />
  <package id="GUI" version="15.0.0.8" targetFramework="net48" />
  <package id="Moq" version="4.20.2" targetFramework="net48" />
  <package id="NLog" version="5.2.3" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="Triquestra.Common.Forecourt" version="7.0.0.2" targetFramework="net48" />
  <package id="Triquestra.Common.ForeCourt.Comms" version="7.0.0.56" targetFramework="net48" />
  <package id="Triquestra.Common.ForeCourt.Messaging" version="7.0.0.57" targetFramework="net48" />
  <package id="Utils" version="5.0.0.3" targetFramework="net48" />
</packages>