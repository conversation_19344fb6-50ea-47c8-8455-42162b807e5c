<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="JPL_Demo_POS_CSharp.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <userSettings>
        <JPL_Demo_POS_CSharp.Properties.Settings>
            <setting name="PosId" serializeAs="String">
                <value>25</value>
            </setting>
            <setting name="IPAddress" serializeAs="String">
                <value />
            </setting>
            <setting name="VolumeDecimalPointPosition" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="PriceDecimalPointPosition" serializeAs="String">
                <value>3</value>
            </setting>
            <setting name="MoneyDecimalPointPosition" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="FcLogonString" serializeAs="String">
                <value>POS,RI,UNSO_TRBUFSTA_3,UNSO_INSTSTA_1,UNSO_FPSTA_3:MFDR=01</value>
            </setting>
            <setting name="ClearDeliveryReports" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="ShowHeartBeatMessages" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ShowUnsolicitedMessages" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="CurrentRequest" serializeAs="String">
                <value />
            </setting>
            <setting name="AutoLogon" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="PortNumber" serializeAs="String">
                <value>8888</value>
            </setting>
            <setting name="ClearBackOfficeRecords" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="AutoLockTransactions" serializeAs="String">
                <value>False</value>
            </setting>
        </JPL_Demo_POS_CSharp.Properties.Settings>
    </userSettings>
</configuration>
