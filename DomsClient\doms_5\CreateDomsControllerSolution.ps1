# Generate-DomsPosClientSolution.ps1
param(
    [string]$SolutionPath = ".\DomsPosClient",
    [string]$SolutionName = "DomsPosClient"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Doms POS Protocol Client Generator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Create solution structure
Write-Host "Creating solution structure..." -ForegroundColor Green

$directories = @(
    "$SolutionPath",
    "$SolutionPath\src",
    "$SolutionPath\src\$SolutionName.Core",
    "$SolutionPath\src\$SolutionName.Core\Communication",
    "$SolutionPath\src\$SolutionName.Core\Messages",
    "$SolutionPath\src\$SolutionName.Core\Messages\Requests",
    "$SolutionPath\src\$SolutionName.Core\Messages\Responses", 
    "$SolutionPath\src\$SolutionName.Core\Models",
    "$SolutionPath\src\$SolutionName.Core\Services",
    "$SolutionPath\src\$SolutionName.Core\Utilities",
    "$SolutionPath\src\$SolutionName.Core\Exceptions",
    "$SolutionPath\src\$SolutionName.Core\Properties",
    "$SolutionPath\tests",
    "$SolutionPath\tests\$SolutionName.Tests",
    "$SolutionPath\tests\$SolutionName.Tests\Properties",
    "$SolutionPath\examples",
    "$SolutionPath\docs"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
    Write-Host "  Created: $dir" -ForegroundColor Yellow
}

# Generate GUIDs for projects
$coreProjectGuid = [System.Guid]::NewGuid().ToString().ToUpper()
$testsProjectGuid = [System.Guid]::NewGuid().ToString().ToUpper()

Write-Host "Creating solution and project files..." -ForegroundColor Green

# Create solution file
$solutionContent = @"
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = ********
MinimumVisualStudioVersion = 10.0.0.0
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "$SolutionName.Core", "src\$SolutionName.Core\$SolutionName.Core.csproj", "{$coreProjectGuid}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "$SolutionName.Tests", "tests\$SolutionName.Tests\$SolutionName.Tests.csproj", "{$testsProjectGuid}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{$coreProjectGuid}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{$coreProjectGuid}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{$coreProjectGuid}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{$coreProjectGuid}.Release|Any CPU.Build.0 = Release|Any CPU
		{$testsProjectGuid}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{$testsProjectGuid}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{$testsProjectGuid}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{$testsProjectGuid}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
"@

$solutionContent | Out-File -FilePath "$SolutionPath\$SolutionName.sln" -Encoding UTF8

# Create core project file
$coreProjectContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '`$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '`$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{$coreProjectGuid}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>$SolutionName.Core</RootNamespace>
    <AssemblyName>$SolutionName.Core</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Communication\JplClient.cs" />
    <Compile Include="Exceptions\DomsExceptions.cs" />
    <Compile Include="Messages\JplMessage.cs" />
    <Compile Include="Messages\Requests\LogonRequests.cs" />
    <Compile Include="Messages\Responses\LogonResponses.cs" />
    <Compile Include="Messages\Requests\ForecourtRequests.cs" />
    <Compile Include="Messages\Responses\ForecourtResponses.cs" />
    <Compile Include="Messages\Requests\DispenseRequests.cs" />
    <Compile Include="Messages\Responses\DispenseResponses.cs" />
    <Compile Include="Models\DataTypes.cs" />
    <Compile Include="Models\Enumerations.cs" />
    <Compile Include="Services\LogonService.cs" />
    <Compile Include="Services\ForecourtControlService.cs" />
    <Compile Include="Services\DispenseControlService.cs" />
    <Compile Include="Utilities\ILogger.cs" />
    <Compile Include="Utilities\ConsoleLogger.cs" />
    <Compile Include="Utilities\MessageHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="`$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
"@

$coreProjectContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\$SolutionName.Core.csproj" -Encoding UTF8

# Create packages.config
$packagesConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
</packages>
"@

$packagesConfig | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\packages.config" -Encoding UTF8

# Create test project
$testProjectContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '`$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '`$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{$testsProjectGuid}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>$SolutionName.Tests</RootNamespace>
    <AssemblyName>$SolutionName.Tests</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions, Version=14.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MSTest.TestFramework.2.2.10\lib\net45\Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="JplClientTests.cs" />
    <Compile Include="LogonServiceTests.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\src\$SolutionName.Core\$SolutionName.Core.csproj">
      <Project>{$coreProjectGuid}</Project>
      <Name>$SolutionName.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="`$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
"@

$testProjectContent | Out-File -FilePath "$SolutionPath\tests\$SolutionName.Tests\$SolutionName.Tests.csproj" -Encoding UTF8

$testPackagesConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="MSTest.TestAdapter" version="2.2.10" targetFramework="net48" />
  <package id="MSTest.TestFramework" version="2.2.10" targetFramework="net48" />
</packages>
"@

$testPackagesConfig | Out-File -FilePath "$SolutionPath\tests\$SolutionName.Tests\packages.config" -Encoding UTF8

Write-Host "Creating source files..." -ForegroundColor Green

# AssemblyInfo.cs for Core
$assemblyInfoCore = @"
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("$SolutionName.Core")]
[assembly: AssemblyDescription("Doms POS Protocol Client Library")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("$SolutionName")]
[assembly: AssemblyCopyright("Copyright © $(Get-Date -Format yyyy)")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("$coreProjectGuid")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
"@

$assemblyInfoCore | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Properties\AssemblyInfo.cs" -Encoding UTF8

# AssemblyInfo.cs for Tests
$assemblyInfoTests = @"
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("$SolutionName.Tests")]
[assembly: AssemblyDescription("Tests for Doms POS Protocol Client Library")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("$SolutionName")]
[assembly: AssemblyCopyright("Copyright © $(Get-Date -Format yyyy)")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("$testsProjectGuid")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
"@

$assemblyInfoTests | Out-File -FilePath "$SolutionPath\tests\$SolutionName.Tests\Properties\AssemblyInfo.cs" -Encoding UTF8

# 1. JplClient.cs
$jplClientContent = @"
using System;
using System.IO;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Newtonsoft.Json;
using $SolutionName.Core.Exceptions;
using $SolutionName.Core.Messages;
using $SolutionName.Core.Utilities;

namespace $SolutionName.Core.Communication
{
    /// <summary>
    /// Main client for communicating with Doms PSS Forecourt Controller using JSON Presentation Layer (JPL)
    /// </summary>
    public class JplClient : IDisposable
    {
        private const byte STX = 0x02;
        private const byte ETX = 0x03;
        private const int DefaultPort = 8888;
        private const int DefaultSecurePort = 8889;
        private const int HeartbeatInterval = 20000; // 20 seconds
        private const int ReceiveTimeout = 30000; // 30 seconds

        private readonly ILogger _logger;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplMessage>> _pendingRequests;
        private TcpClient _tcpClient;
        private Stream _stream;
        private Timer _heartbeatTimer;
        private Timer _receiveTimeoutTimer;
        private DateTime _lastMessageReceived;
        private bool _disposed;
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// Event fired when an unsolicited message is received
        /// </summary>
        public event EventHandler<JplMessage> UnsolicitedMessageReceived;

        /// <summary>
        /// Event fired when connection state changes
        /// </summary>
        public event EventHandler<bool> ConnectionStateChanged;

        /// <summary>
        /// Gets whether the client is currently connected
        /// </summary>
        public bool IsConnected => _tcpClient?.Connected == true;

        /// <summary>
        /// Initializes a new instance of the JplClient
        /// </summary>
        /// <param name=""logger"">Logger instance</param>
        public JplClient(ILogger logger = null)
        {
            _logger = logger ?? new ConsoleLogger();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplMessage>>();
            _lastMessageReceived = DateTime.UtcNow;
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// Connects to the PSS controller
        /// </summary>
        /// <param name=""hostname"">Controller hostname or IP address</param>
        /// <param name=""port"">Port number (default: 8888 for unencrypted, 8889 for TLS)</param>
        /// <param name=""useTls"">Whether to use TLS encryption</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        public async Task ConnectAsync(string hostname, int? port = null, bool useTls = false, CancellationToken cancellationToken = default)
        {
            try
            {
                if (IsConnected)
                    throw new InvalidOperationException(""Client is already connected"");

                int actualPort = port ?? (useTls ? DefaultSecurePort : DefaultPort);
                
                _logger.LogInfo(`$""Connecting to {hostname}:{actualPort} (TLS: {useTls})"");

                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(hostname, actualPort);

                if (useTls)
                {
                    var sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                    await sslStream.AuthenticateAsClientAsync(hostname);
                    _stream = sslStream;
                }
                else
                {
                    _stream = _tcpClient.GetStream();
                }

                _logger.LogInfo(""Connected successfully"");
                
                // Start heartbeat and receive timeout timers
                StartTimers();
                
                // Start listening for messages
                _ = Task.Run(() => MessageReceiveLoop(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                ConnectionStateChanged?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Connection failed: {ex.Message}"");
                Cleanup();
                throw new DomsCommunicationException(`$""Failed to connect to {hostname}:{port}"", ex);
            }
        }

        /// <summary>
        /// Sends a message and waits for response
        /// </summary>
        /// <typeparam name=""TResponse"">Expected response type</typeparam>
        /// <param name=""request"">Request message</param>
        /// <param name=""timeoutMs"">Timeout in milliseconds</param>
        /// <param name=""correlationId"">Optional correlation ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Response message</returns>
        public async Task<TResponse> SendMessageAsync<TResponse>(JplMessage request, int timeoutMs = 30000, string correlationId = null, CancellationToken cancellationToken = default)
            where TResponse : class
        {
            if (!IsConnected)
                throw new InvalidOperationException(""Client is not connected"");

            try
            {
                // Generate correlation ID if not provided
                if (string.IsNullOrEmpty(correlationId))
                {
                    correlationId = Guid.NewGuid().ToString();
                }

                request.CorrelationId = correlationId;

                _logger.LogDebug(`$""Sending message: {request.Name}"");

                // Create task completion source for response
                var tcs = new TaskCompletionSource<JplMessage>();
                _pendingRequests[GetResponseMessageName(request.Name)] = tcs;

                try
                {
                    // Serialize and frame the message
                    string jsonData = JsonConvert.SerializeObject(request, Formatting.None);
                    byte[] messageBytes = FrameMessage(jsonData);

                    // Send the message
                    await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                    await _stream.FlushAsync(cancellationToken);

                    // Wait for response with timeout
                    using (var timeoutCts = new CancellationTokenSource(timeoutMs))
                    using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                    {
                        var response = await tcs.Task.ConfigureAwait(false);
                        return JsonConvert.DeserializeObject<TResponse>(JsonConvert.SerializeObject(response));
                    }
                }
                finally
                {
                    _pendingRequests.TryRemove(GetResponseMessageName(request.Name), out _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to send message: {ex.Message}"");
                throw new DomsCommunicationException(""Failed to send message"", ex);
            }
        }

        /// <summary>
        /// Sends a heartbeat message
        /// </summary>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Task representing the heartbeat operation</returns>
        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            try
            {
                var heartbeat = new JplMessage
                {
                    Name = ""heartbeat"",
                    SubCode = ""00H"",
                    Data = new { }
                };

                string jsonData = JsonConvert.SerializeObject(heartbeat, Formatting.None);
                byte[] messageBytes = FrameMessage(jsonData);

                await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);

                _logger.LogDebug(""Heartbeat sent"");
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to send heartbeat: {ex.Message}"");
            }
        }

        /// <summary>
        /// Disconnects from the PSS controller
        /// </summary>
        public void Disconnect()
        {
            _logger.LogInfo(""Disconnecting..."");
            _cancellationTokenSource?.Cancel();
            Cleanup();
            ConnectionStateChanged?.Invoke(this, false);
        }

        private static byte[] FrameMessage(string jsonData)
        {
            byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
            byte[] framedMessage = new byte[jsonBytes.Length + 2];
            framedMessage[0] = STX;
            Array.Copy(jsonBytes, 0, framedMessage, 1, jsonBytes.Length);
            framedMessage[framedMessage.Length - 1] = ETX;
            return framedMessage;
        }

        private async Task MessageReceiveLoop(CancellationToken cancellationToken)
        {
            byte[] buffer = new byte[4096];
            var messageBuffer = new MemoryStream();

            try
            {
                while (IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    int bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    
                    if (bytesRead == 0)
                    {
                        _logger.LogWarning(""Connection closed by remote host"");
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    // Process received bytes
                    for (int i = 0; i < bytesRead; i++)
                    {
                        byte b = buffer[i];

                        if (b == STX)
                        {
                            messageBuffer.SetLength(0);
                        }
                        else if (b == ETX)
                        {
                            // Complete message received
                            string jsonData = Encoding.UTF8.GetString(messageBuffer.ToArray());
                            await ProcessReceivedMessage(jsonData);
                            messageBuffer.SetLength(0);
                        }
                        else
                        {
                            messageBuffer.WriteByte(b);
                        }
                    }
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                _logger.LogError(`$""Error in message receive loop: {ex.Message}"");
            }
            finally
            {
                messageBuffer.Dispose();
            }
        }

        private async Task ProcessReceivedMessage(string jsonData)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<JplMessage>(jsonData);
                
                _logger.LogDebug(`$""Received message: {message.Name}"");

                // Check if this is a response to a pending request
                if (_pendingRequests.TryRemove(message.Name, out var tcs))
                {
                    tcs.SetResult(message);
                }
                // Check if this is an unsolicited message
                else if (message.Solicited == false)
                {
                    UnsolicitedMessageReceived?.Invoke(this, message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to process received message: {ex.Message}"");
            }
        }

        private string GetResponseMessageName(string requestName)
        {
            return requestName.Replace(""_req"", ""_resp"");
        }

        private void StartTimers()
        {
            // Heartbeat timer
            _heartbeatTimer = new Timer(async _ => await SendHeartbeatAsync(), null, HeartbeatInterval, HeartbeatInterval);
            
            // Receive timeout timer
            _receiveTimeoutTimer = new Timer(CheckReceiveTimeout, null, ReceiveTimeout, ReceiveTimeout);
        }

        private void CheckReceiveTimeout(object state)
        {
            if ((DateTime.UtcNow - _lastMessageReceived).TotalMilliseconds > ReceiveTimeout)
            {
                _logger.LogWarning(""Receive timeout - closing connection"");
                Disconnect();
            }
        }

        private static bool ValidateServerCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            // In production, implement proper certificate validation
            return true;
        }

        private void Cleanup()
        {
            _heartbeatTimer?.Dispose();
            _receiveTimeoutTimer?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Close();
            _tcpClient?.Dispose();
            
            _heartbeatTimer = null;
            _receiveTimeoutTimer = null;
            _stream = null;
            _tcpClient = null;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                Cleanup();
                _disposed = true;
            }
        }
    }
}
"@

$jplClientContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Communication\JplClient.cs" -Encoding UTF8

# 2. JplMessage.cs
$jplMessageContent = @"
using System;
using Newtonsoft.Json;

namespace $SolutionName.Core.Messages
{
    /// <summary>
    /// Base class for all JPL messages
    /// </summary>
    public class JplMessage
    {
        /// <summary>
        /// Name of the message
        /// </summary>
        [JsonProperty(""name"")]
        public string Name { get; set; }

        /// <summary>
        /// Sub-code identifying message variant
        /// </summary>
        [JsonProperty(""subCode"")]
        public string SubCode { get; set; }

        /// <summary>
        /// Data associated with the message
        /// </summary>
        [JsonProperty(""data"")]
        public object Data { get; set; }

        /// <summary>
        /// Indicates if message is solicited (response only)
        /// </summary>
        [JsonProperty(""solicited"", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Solicited { get; set; }

        /// <summary>
        /// Optional correlation ID for request/response matching
        /// </summary>
        [JsonProperty(""correlationId"", NullValueHandling = NullValueHandling.Ignore)]
        public string CorrelationId { get; set; }
    }

    /// <summary>
    /// Base class for request messages
    /// </summary>
    public abstract class JplRequest : JplMessage
    {
        protected JplRequest(string name, string subCode = ""00H"")
        {
            Name = name;
            SubCode = subCode;
        }
    }

    /// <summary>
    /// Base class for response messages
    /// </summary>
    public abstract class JplResponse : JplMessage
    {
        protected JplResponse(string name, string subCode = ""00H"")
        {
            Name = name;
            SubCode = subCode;
        }
    }
}
"@

$jplMessageContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\JplMessage.cs" -Encoding UTF8

# 3. DomsExceptions.cs
$exceptionsContent = @"
using System;

namespace $SolutionName.Core.Exceptions
{
    /// <summary>
    /// Base exception for all Doms POS client exceptions
    /// </summary>
    public class DomsPosException : Exception
    {
        public DomsPosException(string message) : base(message) { }
        public DomsPosException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown for communication-related errors
    /// </summary>
    public class DomsCommunicationException : DomsPosException
    {
        public DomsCommunicationException(string message) : base(message) { }
        public DomsCommunicationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown for protocol-related errors
    /// </summary>
    public class DomsProtocolException : DomsPosException
    {
        public DomsProtocolException(string message) : base(message) { }
        public DomsProtocolException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when a message is rejected by the controller
    /// </summary>
    public class DomsMessageRejectedException : DomsProtocolException
    {
        public string RejectCode { get; }
        public string RejectInfo { get; }

        public DomsMessageRejectedException(string rejectCode, string rejectInfo) 
            : base(`$""Message rejected - Code: {rejectCode}, Info: {rejectInfo}"")
        {
            RejectCode = rejectCode;
            RejectInfo = rejectInfo;
        }
    }
}
"@

$exceptionsContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Exceptions\DomsExceptions.cs" -Encoding UTF8

# 4. DataTypes.cs
$dataTypesContent = @"
using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace $SolutionName.Core.Models
{
    /// <summary>
    /// Represents a date and time in FC format (YYYYMMDDhhmmss)
    /// </summary>
    [JsonConverter(typeof(FcDateTimeConverter))]
    public struct FcDateTime
    {
        private readonly string _value;

        public FcDateTime(DateTime dateTime)
        {
            _value = dateTime.ToString(""yyyyMMddHHmmss"");
        }

        public FcDateTime(string value)
        {
            _value = value ?? throw new ArgumentNullException(nameof(value));
        }

        public DateTime ToDateTime()
        {
            if (string.IsNullOrEmpty(_value) || _value.Length != 14)
                throw new FormatException(""Invalid FcDateTime format"");

            return DateTime.ParseExact(_value, ""yyyyMMddHHmmss"", null);
        }

        public override string ToString() => _value;

        public static implicit operator string(FcDateTime fcDateTime) => fcDateTime._value;
        public static implicit operator FcDateTime(string value) => new FcDateTime(value);
        public static implicit operator FcDateTime(DateTime dateTime) => new FcDateTime(dateTime);
    }

    /// <summary>
    /// JSON converter for FcDateTime
    /// </summary>
    public class FcDateTimeConverter : JsonConverter<FcDateTime>
    {
        public override void WriteJson(JsonWriter writer, FcDateTime value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString());
        }

        public override FcDateTime ReadJson(JsonReader reader, Type objectType, FcDateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            return new FcDateTime(reader.Value?.ToString());
        }
    }

    /// <summary>
    /// Represents a forecourt money value
    /// </summary>
    public struct FcMoney
    {
        private readonly string _value;

        public FcMoney(decimal amount, int decimalPlaces = 2)
        {
            _value = ((long)(amount * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcMoney(string value)
        {
            _value = value ?? ""0"";
        }

        public decimal ToDecimal(int decimalPlaces = 2)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcMoney fcMoney) => fcMoney._value;
        public static implicit operator FcMoney(string value) => new FcMoney(value);
        public static implicit operator FcMoney(decimal amount) => new FcMoney(amount);
    }

    /// <summary>
    /// Represents a forecourt volume value
    /// </summary>
    public struct FcVolume
    {
        private readonly string _value;

        public FcVolume(decimal volume, int decimalPlaces = 2)
        {
            _value = ((long)(volume * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcVolume(string value)
        {
            _value = value ?? ""0"";
        }

        public decimal ToDecimal(int decimalPlaces = 2)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcVolume fcVolume) => fcVolume._value;
        public static implicit operator FcVolume(string value) => new FcVolume(value);
        public static implicit operator FcVolume(decimal volume) => new FcVolume(volume);
    }

    /// <summary>
    /// Represents a forecourt price value
    /// </summary>
    public struct FcPrice
    {
        private readonly string _value;

        public FcPrice(decimal price, int decimalPlaces = 4)
        {
            _value = ((long)(price * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcPrice(string value)
        {
            _value = value ?? ""0"";
        }

        public decimal ToDecimal(int decimalPlaces = 4)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcPrice fcPrice) => fcPrice._value;
        public static implicit operator FcPrice(string value) => new FcPrice(value);
        public static implicit operator FcPrice(decimal price) => new FcPrice(price);
    }

    /// <summary>
    /// Enumeration wrapper for JPL enum values
    /// </summary>
    /// <typeparam name=""T"">Enum type</typeparam>
    public class JplEnum<T> where T : struct, Enum
    {
        [JsonProperty(""enum"")]
        public Dictionary<string, string> EnumValues { get; set; }

        [JsonProperty(""value"")]
        public string Value { get; set; }

        public T GetEnumValue()
        {
            if (EnumValues != null)
            {
                var kvp = EnumValues.FirstOrDefault(e => e.Value == Value);
                if (Enum.TryParse<T>(kvp.Key, true, out T result))
                    return result;
            }
            return default(T);
        }
    }

    /// <summary>
    /// Represents bit flags in JPL messages
    /// </summary>
    public class JplBitFlags
    {
        [JsonProperty(""value"")]
        public int Value { get; set; }

        [JsonProperty(""bits"")]
        public Dictionary<string, int> Bits { get; set; }

        public bool IsBitSet(string bitName)
        {
            return Bits?.ContainsKey(bitName) == true && Bits[bitName] > 0;
        }
    }
}
"@

$dataTypesContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Models\DataTypes.cs" -Encoding UTF8

# 5. Enumerations.cs
$enumerationsContent = @"
using System;

namespace $SolutionName.Core.Models
{
    /// <summary>
    /// Fuelling point main states
    /// </summary>
    public enum FpMainState
    {
        Unconfigured,
        Closed,
        Idle,
        Error,
        Calling,
        PreAuthorized,
        Starting,
        Starting_paused,
        Starting_terminated,
        Fuelling,
        Fuelling_paused,
        Fuelling_terminated,
        Unavailable,
        Unavailable_and_calling
    }

    /// <summary>
    /// Tank gauge main states
    /// </summary>
    public enum TgMainState
    {
        Unconfigured,
        Operative,
        Alarm,
        Error
    }

    /// <summary>
    /// EPT main states
    /// </summary>
    public enum EptMainState
    {
        Unconfigured,
        Closed,
        Error,
        Idle,
        Busy
    }

    /// <summary>
    /// EPT validation states
    /// </summary>
    public enum EptValidationState
    {
        IDLE,
        BUSY,
        REQUEST,
        COMPLETED,
        CANCELLED
    }

    /// <summary>
    /// Preset types for fuelling points
    /// </summary>
    public enum PresetType
    {
        NoPreset,
        VolumePreset,
        MoneyPreset,
        FloorLimitPreset
    }

    /// <summary>
    /// Service mode categories
    /// </summary>
    public enum ServiceModeCategory
    {
        PostPayment = 1,
        PrePayment = 2,
        AttendantPayment = 3,
        NoPayment = 4,
        CardPayment = 5,
        BankNotePayment = 6
    }
}
"@

$enumerationsContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Models\Enumerations.cs" -Encoding UTF8

# 6. ILogger.cs
$iloggerContent = @"
using System;

namespace $SolutionName.Core.Utilities
{
    /// <summary>
    /// Interface for logging functionality
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogDebug(string message);

        /// <summary>
        /// Logs an information message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error message with exception details
        /// </summary>
        /// <param name=""message"">Message to log</param>
        /// <param name=""exception"">Exception to log</param>
        void LogError(string message, Exception exception);
    }
}
"@

$iloggerContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Utilities\ILogger.cs" -Encoding UTF8

# 7. ConsoleLogger.cs
$consoleLoggerContent = @"
using System;

namespace $SolutionName.Core.Utilities
{
    /// <summary>
    /// Console implementation of ILogger
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        private readonly ConsoleColor _originalColor;

        public ConsoleLogger()
        {
            _originalColor = Console.ForegroundColor;
        }

        public void LogDebug(string message)
        {
            WriteWithColor(`$""[DEBUG] {DateTime.Now:HH:mm:ss} {message}"", ConsoleColor.Gray);
        }

        public void LogInfo(string message)
        {
            WriteWithColor(`$""[INFO]  {DateTime.Now:HH:mm:ss} {message}"", ConsoleColor.White);
        }

        public void LogWarning(string message)
        {
            WriteWithColor(`$""[WARN]  {DateTime.Now:HH:mm:ss} {message}"", ConsoleColor.Yellow);
        }

        public void LogError(string message)
        {
            WriteWithColor(`$""[ERROR] {DateTime.Now:HH:mm:ss} {message}"", ConsoleColor.Red);
        }

        public void LogError(string message, Exception exception)
        {
            WriteWithColor(`$""[ERROR] {DateTime.Now:HH:mm:ss} {message} - {exception}"", ConsoleColor.Red);
        }

        private void WriteWithColor(string message, ConsoleColor color)
        {
            lock (this)
            {
                Console.ForegroundColor = color;
                Console.WriteLine(message);
                Console.ForegroundColor = _originalColor;
            }
        }
    }
}
"@

$consoleLoggerContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Utilities\ConsoleLogger.cs" -Encoding UTF8

# 8. MessageHelper.cs
$messageHelperContent = @"
using System;
using System.Text;

namespace $SolutionName.Core.Utilities
{
    /// <summary>
    /// Helper methods for message processing
    /// </summary>
    public static class MessageHelper
    {
        /// <summary>
        /// Converts a hex string to bytes
        /// </summary>
        /// <param name=""hex"">Hex string</param>
        /// <returns>Byte array</returns>
        public static byte[] HexStringToBytes(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return new byte[0];

            if (hex.EndsWith(""H"", StringComparison.OrdinalIgnoreCase))
                hex = hex.Substring(0, hex.Length - 1);

            var bytes = new byte[hex.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
            }
            return bytes;
        }

        /// <summary>
        /// Converts bytes to hex string
        /// </summary>
        /// <param name=""bytes"">Byte array</param>
        /// <param name=""addHSuffix"">Whether to add 'H' suffix</param>
        /// <returns>Hex string</returns>
        public static string BytesToHexString(byte[] bytes, bool addHSuffix = true)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            var sb = new StringBuilder();
            foreach (byte b in bytes)
            {
                sb.AppendFormat(""{0:X2}"", b);
            }

            return addHSuffix ? sb.ToString() + ""H"" : sb.ToString();
        }

        /// <summary>
        /// Validates a message name format
        /// </summary>
        /// <param name=""messageName"">Message name to validate</param>
        /// <returns>True if valid</returns>
        public static bool IsValidMessageName(string messageName)
        {
            return !string.IsNullOrEmpty(messageName) && 
                   (messageName.EndsWith(""_req"") || messageName.EndsWith(""_resp""));
        }

        /// <summary>
        /// Gets the response message name for a request
        /// </summary>
        /// <param name=""requestName"">Request message name</param>
        /// <returns>Response message name</returns>
        public static string GetResponseName(string requestName)
        {
            if (string.IsNullOrEmpty(requestName))
                throw new ArgumentNullException(nameof(requestName));

            if (requestName.EndsWith(""_req""))
                return requestName.Replace(""_req"", ""_resp"");

            throw new ArgumentException(""Invalid request message name"", nameof(requestName));
        }
    }
}
"@

$messageHelperContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Utilities\MessageHelper.cs" -Encoding UTF8

# 9. LogonRequests.cs
$logonRequestsContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Models;

namespace $SolutionName.Core.Messages.Requests
{
    /// <summary>
    /// FcLogon request message
    /// </summary>
    public class FcLogonRequest : JplRequest
    {
        public FcLogonRequest() : base(""FcLogon_req"") { }
        
        public new FcLogonRequestData Data { get; set; }
    }

    /// <summary>
    /// FcLogon request data
    /// </summary>
    public class FcLogonRequestData
    {
        public string FcAccessCode { get; set; }
        public string CountryCode { get; set; }
        public string PosVersionId { get; set; }
    }

    /// <summary>
    /// Extended FcLogon request message
    /// </summary>
    public class FcLogonExtendedRequest : JplRequest
    {
        public FcLogonExtendedRequest() : base(""FcLogon_req"", ""01H"") { }
        
        public new FcLogonExtendedRequestData Data { get; set; }
    }

    /// <summary>
    /// Extended FcLogon request data
    /// </summary>
    public class FcLogonExtendedRequestData : FcLogonRequestData
    {
        public FcLogonParameters FcLogonPars { get; set; }
    }

    /// <summary>
    /// Logon parameters
    /// </summary>
    public class FcLogonParameters
    {
        public UnsolicitedMessage[] UnsolMsgList { get; set; }
    }

    /// <summary>
    /// Unsolicited message configuration
    /// </summary>
    public class UnsolicitedMessage
    {
        public string ExtMsgCode { get; set; }
        public string MsgSubc { get; set; }
    }
}
"@

$logonRequestsContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Requests\LogonRequests.cs" -Encoding UTF8

# 10. LogonResponses.cs
$logonResponsesContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Messages.Requests;

namespace $SolutionName.Core.Messages.Responses
{
    /// <summary>
    /// FcLogon response message
    /// </summary>
    public class FcLogonResponse : JplResponse
    {
        public FcLogonResponse() : base(""FcLogon_resp"") { }
        
        public new FcLogonResponseData Data { get; set; }
    }

    /// <summary>
    /// FcLogon response data
    /// </summary>
    public class FcLogonResponseData
    {
        public string CountryCode { get; set; }
        public int FcHwType { get; set; }
        public string FcHwVersionNo { get; set; }
        public int FcSwType { get; set; }
        public string FcSwVersionNo { get; set; }
        public string FcSwDate { get; set; }
        public FcSwBlock[] FcSwBlocks { get; set; }
    }

    /// <summary>
    /// Software block information
    /// </summary>
    public class FcSwBlock
    {
        public string FcSwMainBlockId { get; set; }
        public string FcSwSubBlockId { get; set; }
        public string FcSwBlockReleaseNo { get; set; }
        public string FcSwBlockCheckCode { get; set; }
    }

    /// <summary>
    /// Extended FcLogon response message
    /// </summary>
    public class FcLogonExtendedResponse : JplResponse
    {
        public FcLogonExtendedResponse() : base(""FcLogon_resp"", ""01H"") { }
        
        public new FcLogonExtendedResponseData Data { get; set; }
    }

    /// <summary>
    /// Extended FcLogon response data
    /// </summary>
    public class FcLogonExtendedResponseData : FcLogonResponseData
    {
        public UnsolicitedMessage[] UnsolMessages { get; set; }
    }
}
"@

$logonResponsesContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Responses\LogonResponses.cs" -Encoding UTF8

# 11. ForecourtRequests.cs
$forecourtRequestsContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Models;

namespace $SolutionName.Core.Messages.Requests
{
    /// <summary>
    /// FC Status request
    /// </summary>
    public class FcStatusRequest : JplRequest
    {
        public FcStatusRequest() : base(""FcStatus_req"") 
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC date and time request
    /// </summary>
    public class ChangeFcDateTimeRequest : JplRequest
    {
        public ChangeFcDateTimeRequest() : base(""change_FcDateAndTime_req"") { }
        
        public new ChangeFcDateTimeRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC date and time request data
    /// </summary>
    public class ChangeFcDateTimeRequestData
    {
        public FcDateTime FcDateAndTime { get; set; }
    }

    /// <summary>
    /// FC date and time request
    /// </summary>
    public class FcDateTimeRequest : JplRequest
    {
        public FcDateTimeRequest() : base(""FcDateAndTime_req"") 
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC operation mode request
    /// </summary>
    public class ChangeFcOperationModeRequest : JplRequest
    {
        public ChangeFcOperationModeRequest() : base(""change_FcOperationModeNo_req"") { }
        
        public new ChangeFcOperationModeRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC operation mode request data
    /// </summary>
    public class ChangeFcOperationModeRequestData
    {
        public int FcOperationModeNo { get; set; }
    }

    /// <summary>
    /// Change FC price set request
    /// </summary>
    public class ChangeFcPriceSetRequest : JplRequest
    {
        public ChangeFcPriceSetRequest() : base(""change_FcPriceSet_req"", ""02H"") { }
        
        public new ChangeFcPriceSetRequestData Data { get; set; }
    }

    /// <summary>
    /// Change FC price set request data
    /// </summary>
    public class ChangeFcPriceSetRequestData
    {
        public string FcPriceSetId { get; set; }
        public string[] FcPriceGroupId { get; set; }
        public string[] FcGradeId { get; set; }
        public string[][] FcPriceGroups { get; set; }
        public string PriceSetActivationDateAndTime { get; set; }
    }

    /// <summary>
    /// FC installation status request
    /// </summary>
    public class FcInstallStatusRequest : JplRequest
    {
        public FcInstallStatusRequest() : base(""FcInstallStatus_req"") 
        {
            Data = new { };
        }
    }
}
"@

$forecourtRequestsContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Requests\ForecourtRequests.cs" -Encoding UTF8

# 12. ForecourtResponses.cs
$forecourtResponsesContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Models;

namespace $SolutionName.Core.Messages.Responses
{
    /// <summary>
    /// FC Status response
    /// </summary>
    public class FcStatusResponse : JplResponse
    {
        public FcStatusResponse() : base(""FcStatus_resp"") { }
        
        public new FcStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FC Status response data
    /// </summary>
    public class FcStatusResponseData
    {
        public FcStatus1Flags FcStatus1Flags { get; set; }
        public FcStatus2Flags FcStatus2Flags { get; set; }
        public string FcServiceMsgSeqNo { get; set; }
        public FcDateTime FcMasterResetDateAndTime { get; set; }
        public int FcMasterResetCode { get; set; }
        public FcDateTime FcResetDateAndTime { get; set; }
        public string FcResetCode { get; set; }
    }

    /// <summary>
    /// FC Status 1 flags
    /// </summary>
    public class FcStatus1Flags
    {
        public int Value { get; set; }
        public FcStatus1Bits Bits { get; set; }
    }

    /// <summary>
    /// FC Status 1 bits
    /// </summary>
    public class FcStatus1Bits
    {
        public int PumpTotalsReady { get; set; }
        public int InstallationDataReceived { get; set; }
        public int FallbackMode { get; set; }
        public int FallbackTotalsNonZero { get; set; }
        public int RamErrorDetectedInFc { get; set; }
        public int OpWithStoredTransDisabled { get; set; }
        public int OptSaleDisabled { get; set; }
        public int CurrencyCodeIsEuro { get; set; }
    }

    /// <summary>
    /// FC Status 2 flags
    /// </summary>
    public class FcStatus2Flags
    {
        public int Value { get; set; }
        public FcStatus2Bits Bits { get; set; }
    }

    /// <summary>
    /// FC Status 2 bits
    /// </summary>
    public class FcStatus2Bits
    {
        public int ServiceMsgReady { get; set; }
        public int UnsolicitedStatusUpdateOn { get; set; }
        public int HwSwIncompatibilityWithinFc { get; set; }
        public int RtcError { get; set; }
        public int NoAdditionalParametersAssignedToGrades { get; set; }
        public int BackOfficeRecordExists { get; set; }
    }

    /// <summary>
    /// Change FC date and time response
    /// </summary>
    public class ChangeFcDateTimeResponse : JplResponse
    {
        public ChangeFcDateTimeResponse() : base(""change_FcDateAndTime_resp"") 
        {
            Data = new { };
        }
    }

    /// <summary>
    /// FC date and time response
    /// </summary>
    public class FcDateTimeResponse : JplResponse
    {
        public FcDateTimeResponse() : base(""FcDateAndTime_resp"") { }
        
        public new FcDateTimeResponseData Data { get; set; }
    }

    /// <summary>
    /// FC date and time response data
    /// </summary>
    public class FcDateTimeResponseData
    {
        public FcDateTime FcDateAndTime { get; set; }
        public FcDateTime LastDateAndTimeSetting { get; set; }
    }

    /// <summary>
    /// Change FC operation mode response
    /// </summary>
    public class ChangeFcOperationModeResponse : JplResponse
    {
        public ChangeFcOperationModeResponse() : base(""change_FcOperationModeNo_resp"") 
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC price set response
    /// </summary>
    public class ChangeFcPriceSetResponse : JplResponse
    {
        public ChangeFcPriceSetResponse() : base(""change_FcPriceSet_resp"", ""02H"") { }
        
        public new ChangeFcPriceSetResponseData Data { get; set; }
    }

    /// <summary>
    /// Change FC price set response data
    /// </summary>
    public class ChangeFcPriceSetResponseData
    {
        public string FcPriceSetId { get; set; }
    }

    /// <summary>
    /// FC installation status response
    /// </summary>
    public class FcInstallStatusResponse : JplResponse
    {
        public FcInstallStatusResponse() : base(""FcInstallStatus_resp"") { }
        
        public new FcInstallStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FC installation status response data
    /// </summary>
    public class FcInstallStatusResponseData
    {
        public InstalledFcDeviceGroup[] InstalledFcDeviceGroups { get; set; }
    }

    /// <summary>
    /// Installed FC device group
    /// </summary>
    public class InstalledFcDeviceGroup
    {
        public string InstallMsgCode { get; set; }
        public string ExtendedInstallMsgCode { get; set; }
        public string[] FcDeviceId { get; set; }
    }
}
"@

$forecourtResponsesContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Responses\ForecourtResponses.cs" -Encoding UTF8

# 13. DispenseRequests.cs
$dispenseRequestsContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Models;

namespace $SolutionName.Core.Messages.Requests
{
    /// <summary>
    /// FP Status request
    /// </summary>
    public class FpStatusRequest : JplRequest
    {
        public FpStatusRequest() : base(""FpStatus_req"") { }
        
        public new FpStatusRequestData Data { get; set; }
    }

    /// <summary>
    /// FP Status request data
    /// </summary>
    public class FpStatusRequestData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Open FP request
    /// </summary>
    public class OpenFpRequest : JplRequest
    {
        public OpenFpRequest() : base(""open_Fp_req"") { }
        
        public new OpenFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Open FP request data
    /// </summary>
    public class OpenFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
        public int FpOperationModeNo { get; set; }
    }

    /// <summary>
    /// Close FP request
    /// </summary>
    public class CloseFpRequest : JplRequest
    {
        public CloseFpRequest() : base(""close_Fp_req"") { }
        
        public new CloseFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Close FP request data
    /// </summary>
    public class CloseFpRequestData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Authorize FP request
    /// </summary>
    public class AuthorizeFpRequest : JplRequest
    {
        public AuthorizeFpRequest() : base(""authorize_Fp_req"") { }
        
        public new AuthorizeFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP request data
    /// </summary>
    public class AuthorizeFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// Authorize FP with preset request
    /// </summary>
    public class AuthorizeFpPresetRequest : JplRequest
    {
        public AuthorizeFpPresetRequest() : base(""authorize_Fp_req"", ""01H"") { }
        
        public new AuthorizeFpPresetRequestData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP with preset request data
    /// </summary>
    public class AuthorizeFpPresetRequestData : AuthorizeFpRequestData
    {
        public string PresetType { get; set; }
        public string VoidPresetLimit { get; set; }
        public string VolumePresetLimit { get; set; }
        public string MoneyPresetLimit { get; set; }
        public string FloorPresetLimit { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization request
    /// </summary>
    public class CancelFpAuthRequest : JplRequest
    {
        public CancelFpAuthRequest() : base(""cancel_FpAuth_req"") { }
        
        public new CancelFpAuthRequestData Data { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization request data
    /// </summary>
    public class CancelFpAuthRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// Emergency stop FP request
    /// </summary>
    public class EstopFpRequest : JplRequest
    {
        public EstopFpRequest() : base(""estop_Fp_req"") { }
        
        public new EstopFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Emergency stop FP request data
    /// </summary>
    public class EstopFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// FP supervised transaction request
    /// </summary>
    public class FpSupTransRequest : JplRequest
    {
        public FpSupTransRequest() : base(""FpSupTrans_req"") { }
        
        public new FpSupTransRequestData Data { get; set; }
    }

    /// <summary>
    /// FP supervised transaction request data
    /// </summary>
    public class FpSupTransRequestData
    {
        public string FpId { get; set; }
        public string TransSeqNo { get; set; }
        public string PosId { get; set; }
        public string[] TransParId { get; set; }
    }
}
"@

$dispenseRequestsContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Requests\DispenseRequests.cs" -Encoding UTF8

# 14. DispenseResponses.cs
$dispenseResponsesContent = @"
using $SolutionName.Core.Messages;
using $SolutionName.Core.Models;

namespace $SolutionName.Core.Messages.Responses
{
    /// <summary>
    /// FP Status response
    /// </summary>
    public class FpStatusResponse : JplResponse
    {
        public FpStatusResponse() : base(""FpStatus_resp"") { }
        
        public new FpStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FP Status response data
    /// </summary>
    public class FpStatusResponseData
    {
        public string FpId { get; set; }
        public string SmId { get; set; }
        public JplEnum<FpMainState> FpMainState { get; set; }
        public JplBitFlags FpSubStates { get; set; }
        public string FpLockId { get; set; }
    }

    /// <summary>
    /// Open FP response
    /// </summary>
    public class OpenFpResponse : JplResponse
    {
        public OpenFpResponse() : base(""open_Fp_resp"") { }
        
        public new OpenFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Open FP response data
    /// </summary>
    public class OpenFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Close FP response
    /// </summary>
    public class CloseFpResponse : JplResponse
    {
        public CloseFpResponse() : base(""close_Fp_resp"") { }
        
        public new CloseFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Close FP response data
    /// </summary>
    public class CloseFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Authorize FP response
    /// </summary>
    public class AuthorizeFpResponse : JplResponse
    {
        public AuthorizeFpResponse() : base(""authorize_Fp_resp"") { }
        
        public new AuthorizeFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP response data
    /// </summary>
    public class AuthorizeFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization response
    /// </summary>
    public class CancelFpAuthResponse : JplResponse
    {
        public CancelFpAuthResponse() : base(""cancel_FpAuth_resp"") { }
        
        public new CancelFpAuthResponseData Data { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization response data
    /// </summary>
    public class CancelFpAuthResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Emergency stop FP response
    /// </summary>
    public class EstopFpResponse : JplResponse
    {
        public EstopFpResponse() : base(""estop_Fp_resp"") { }
        
        public new EstopFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Emergency stop FP response data
    /// </summary>
    public class EstopFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// FP supervised transaction response
    /// </summary>
    public class FpSupTransResponse : JplResponse
    {
        public FpSupTransResponse() : base(""FpSupTrans_resp"") { }
        
        public new FpSupTransResponseData Data { get; set; }
    }

    /// <summary>
    /// FP supervised transaction response data
    /// </summary>
    public class FpSupTransResponseData
    {
        public string FpId { get; set; }
        public string TransSeqNo { get; set; }
        public TransactionParameters TransPars { get; set; }
    }

    /// <summary>
    /// Transaction parameters
    /// </summary>
    public class TransactionParameters
    {
        public string FcShiftNo { get; set; }
        public string ReceiptNo { get; set; }
        public string AuthId { get; set; }
        public string SmId { get; set; }
        public string FmId { get; set; }
        public string FpId { get; set; }
        public string FcPriceGroupId { get; set; }
        public string FcPriceSetId { get; set; }
        public string CurrencyCode { get; set; }
        public string FcGradeId { get; set; }
        public string Price { get; set; }
        public string Vol { get; set; }
        public string Money { get; set; }
        public FcDateTime StartDate { get; set; }
        public string StartTime { get; set; }
        public FcDateTime FinishDate { get; set; }
        public string FinishTime { get; set; }
        public string Price_e { get; set; }
        public string Vol_e { get; set; }
        public string Money_e { get; set; }
    }
}
"@

$dispenseResponsesContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Messages\Responses\DispenseResponses.cs" -Encoding UTF8

# 15. LogonService.cs
$logonServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Messages.Requests;
using $SolutionName.Core.Messages.Responses;
using $SolutionName.Core.Utilities;

namespace $SolutionName.Core.Services
{
    /// <summary>
    /// Service for handling forecourt controller logon operations
    /// </summary>
    public class LogonService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the LogonService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public LogonService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Performs logon to the forecourt controller
        /// </summary>
        /// <param name=""accessCode"">Access code (default: ""POS"")</param>
        /// <param name=""countryCode"">Country code</param>
        /// <param name=""posVersionId"">POS version identifier</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Logon response</returns>
        public async Task<FcLogonResponse> LogonAsync(
            string accessCode = ""POS"", 
            string countryCode = ""0000"", 
            string posVersionId = ""DomsPosClient.1.0"", 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(""Performing logon to forecourt controller"");

                var request = new FcLogonRequest
                {
                    Data = new FcLogonRequestData
                    {
                        FcAccessCode = accessCode,
                        CountryCode = countryCode,
                        PosVersionId = posVersionId
                    }
                };

                var response = await _client.SendMessageAsync<FcLogonResponse>(request, cancellationToken: cancellationToken);

                if (response != null)
                {
                    _logger.LogInfo(`$""Logon successful - Controller: HW{response.Data.FcHwType} SW{response.Data.FcSwType}"");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Logon failed: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Performs extended logon with unsolicited message configuration
        /// </summary>
        /// <param name=""accessCode"">Access code</param>
        /// <param name=""countryCode"">Country code</param>
        /// <param name=""posVersionId"">POS version identifier</param>
        /// <param name=""unsolicitedMessages"">List of unsolicited messages to enable</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Extended logon response</returns>
        public async Task<FcLogonExtendedResponse> LogonExtendedAsync(
            string accessCode = ""POS"",
            string countryCode = ""0000"", 
            string posVersionId = ""DomsPosClient.1.0"",
            UnsolicitedMessage[] unsolicitedMessages = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(""Performing extended logon to forecourt controller"");

                var request = new FcLogonExtendedRequest
                {
                    Data = new FcLogonExtendedRequestData
                    {
                        FcAccessCode = accessCode,
                        CountryCode = countryCode,
                        PosVersionId = posVersionId,
                        FcLogonPars = new FcLogonParameters
                        {
                            UnsolMsgList = unsolicitedMessages ?? new UnsolicitedMessage[0]
                        }
                    }
                };

                var response = await _client.SendMessageAsync<FcLogonExtendedResponse>(request, cancellationToken: cancellationToken);

                if (response != null)
                {
                    _logger.LogInfo(`$""Extended logon successful - {response.Data.UnsolMessages?.Length ?? 0} unsolicited messages configured"");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Extended logon failed: {ex.Message}"");
                throw;
            }
        }
    }
}
"@

$logonServiceContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Services\LogonService.cs" -Encoding UTF8

# 16. ForecourtControlService.cs
$forecourtControlServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Messages.Requests;
using $SolutionName.Core.Messages.Responses;
using $SolutionName.Core.Models;
using $SolutionName.Core.Utilities;

namespace $SolutionName.Core.Services
{
    /// <summary>
    /// Service for general forecourt controller functions
    /// </summary>
    public class ForecourtControlService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the ForecourtControlService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public ForecourtControlService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Gets the current forecourt controller status
        /// </summary>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>FC status response</returns>
        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(""Requesting forecourt controller status"");
                var request = new FcStatusRequest();
                return await _client.SendMessageAsync<FcStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to get FC status: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Sets the forecourt date and time
        /// </summary>
        /// <param name=""dateTime"">Date and time to set</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Setting forecourt date/time to {dateTime}"");

                var request = new ChangeFcDateTimeRequest
                {
                    Data = new ChangeFcDateTimeRequestData
                    {
                        FcDateAndTime = new FcDateTime(dateTime)
                    }
                };

                return await _client.SendMessageAsync<ChangeFcDateTimeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to set FC date/time: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Gets the current forecourt date and time
        /// </summary>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Date and time response</returns>
        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(""Requesting forecourt date/time"");
                var request = new FcDateTimeRequest();
                return await _client.SendMessageAsync<FcDateTimeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to get FC date/time: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Changes the forecourt operation mode
        /// </summary>
        /// <param name=""operationModeNo"">Operation mode number</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcOperationModeResponse> ChangeOperationModeAsync(int operationModeNo, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Changing forecourt operation mode to {operationModeNo}"");

                var request = new ChangeFcOperationModeRequest
                {
                    Data = new ChangeFcOperationModeRequestData
                    {
                        FcOperationModeNo = operationModeNo
                    }
                };

                return await _client.SendMessageAsync<ChangeFcOperationModeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to change FC operation mode: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Sets the price set for the forecourt
        /// </summary>
        /// <param name=""priceSetId"">Price set ID</param>
        /// <param name=""priceGroupIds"">Price group IDs</param>
        /// <param name=""gradeIds"">Grade IDs</param>
        /// <param name=""priceGroups"">Price groups data</param>
        /// <param name=""activationDateTime"">Optional activation date/time</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcPriceSetResponse> SetPriceSetAsync(
            string priceSetId,
            string[] priceGroupIds,
            string[] gradeIds, 
            string[][] priceGroups,
            DateTime? activationDateTime = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Setting price set {priceSetId}"");

                var request = new ChangeFcPriceSetRequest
                {
                    Data = new ChangeFcPriceSetRequestData
                    {
                        FcPriceSetId = priceSetId,
                        FcPriceGroupId = priceGroupIds,
                        FcGradeId = gradeIds,
                        FcPriceGroups = priceGroups,
                        PriceSetActivationDateAndTime = activationDateTime?.ToString(""yyyyMMddHHmmss"") ?? ""00000000000000""
                    }
                };

                return await _client.SendMessageAsync<ChangeFcPriceSetResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to set price set: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Gets the installation status of the forecourt
        /// </summary>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Installation status response</returns>
        public async Task<FcInstallStatusResponse> GetInstallationStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(""Requesting forecourt installation status"");
                var request = new FcInstallStatusRequest();
                return await _client.SendMessageAsync<FcInstallStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to get installation status: {ex.Message}"");
                throw;
            }
        }
    }
}
"@

$forecourtControlServiceContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Services\ForecourtControlService.cs" -Encoding UTF8

# 17. DispenseControlService.cs
$dispenseControlServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Messages.Requests;
using $SolutionName.Core.Messages.Responses;
using $SolutionName.Core.Models;
using $SolutionName.Core.Utilities;

namespace $SolutionName.Core.Services
{
    /// <summary>
    /// Service for dispense control functions
    /// </summary>
    public class DispenseControlService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the DispenseControlService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public DispenseControlService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Gets the status of a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>FP status response</returns>
        public async Task<FpStatusResponse> GetFuellingPointStatusAsync(string fpId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(`$""Requesting status for FP {fpId}"");

                var request = new FpStatusRequest
                {
                    Data = new FpStatusRequestData
                    {
                        FpId = fpId
                    }
                };

                return await _client.SendMessageAsync<FpStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to get FP {fpId} status: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Opens a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""operationModeNo"">Operation mode number</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Open FP response</returns>
        public async Task<OpenFpResponse> OpenFuellingPointAsync(string fpId, string posId, int operationModeNo = 1, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Opening FP {fpId} with POS {posId}, mode {operationModeNo}"");

                var request = new OpenFpRequest
                {
                    Data = new OpenFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId,
                        FpOperationModeNo = operationModeNo
                    }
                };

                return await _client.SendMessageAsync<OpenFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to open FP {fpId}: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Closes a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Close FP response</returns>
        public async Task<CloseFpResponse> CloseFuellingPointAsync(string fpId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Closing FP {fpId}"");

                var request = new CloseFpRequest
                {
                    Data = new CloseFpRequestData
                    {
                        FpId = fpId
                    }
                };

                return await _client.SendMessageAsync<CloseFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to close FP {fpId}: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Authorizes a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Authorize FP response</returns>
        public async Task<AuthorizeFpResponse> AuthorizeFuellingPointAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Authorizing FP {fpId} with POS {posId}"");

                var request = new AuthorizeFpRequest
                {
                    Data = new AuthorizeFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<AuthorizeFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to authorize FP {fpId}: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Authorizes a fuelling point with preset limits
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""presetType"">Preset type</param>
        /// <param name=""volumeLimit"">Volume limit (if applicable)</param>
        /// <param name=""moneyLimit"">Money limit (if applicable)</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Authorize FP response</returns>
        public async Task<AuthorizeFpResponse> AuthorizeFuellingPointWithPresetAsync(
            string fpId, 
            string posId, 
            PresetType presetType,
            decimal? volumeLimit = null,
            decimal? moneyLimit = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Authorizing FP {fpId} with preset {presetType}"");

                var request = new AuthorizeFpPresetRequest
                {
                    Data = new AuthorizeFpPresetRequestData
                    {
                        FpId = fpId,
                        PosId = posId,
                        PresetType = ((int)presetType).ToString(""00H""),
                        VoidPresetLimit = ""0"",
                        VolumePresetLimit = volumeLimit?.ToString() ?? ""0"",
                        MoneyPresetLimit = moneyLimit?.ToString() ?? ""0"",
                        FloorPresetLimit = ""0""
                    }
                };

                return await _client.SendMessageAsync<AuthorizeFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to authorize FP {fpId} with preset: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Cancels authorization for a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Cancel authorization response</returns>
        public async Task<CancelFpAuthResponse> CancelFuellingPointAuthorizationAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo(`$""Cancelling authorization for FP {fpId}"");

                var request = new CancelFpAuthRequest
                {
                    Data = new CancelFpAuthRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<CancelFpAuthResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to cancel authorization for FP {fpId}: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Emergency stops a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Emergency stop response</returns>
        public async Task<EstopFpResponse> EmergencyStopFuellingPointAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogWarning(`$""Emergency stopping FP {fpId}"");

                var request = new EstopFpRequest
                {
                    Data = new EstopFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<EstopFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to emergency stop FP {fpId}: {ex.Message}"");
                throw;
            }
        }

        /// <summary>
        /// Gets a supervised transaction from a fuelling point
        /// </summary>
        /// <param name=""fpId"">Fuelling point ID</param>
        /// <param name=""transSeqNo"">Transaction sequence number</param>
        /// <param name=""posId"">POS ID</param>
        /// <param name=""transParIds"">Transaction parameter IDs to retrieve</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Supervised transaction response</returns>
        public async Task<FpSupTransResponse> GetSupervisedTransactionAsync(
            string fpId, 
            string transSeqNo, 
            string posId,
            string[] transParIds = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug(`$""Getting supervised transaction {transSeqNo} from FP {fpId}"");

                var request = new FpSupTransRequest
                {
                    Data = new FpSupTransRequestData
                    {
                        FpId = fpId,
                        TransSeqNo = transSeqNo,
                        PosId = posId,
                        TransParId = transParIds ?? new string[] { ""51"", ""52"", ""53"", ""54"" } // Basic transaction data
                    }
                };

                return await _client.SendMessageAsync<FpSupTransResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$""Failed to get supervised transaction from FP {fpId}: {ex.Message}"");
                throw;
            }
        }
    }
}
"@

$dispenseControlServiceContent | Out-File -FilePath "$SolutionPath\src\$SolutionName.Core\Services\DispenseControlService.cs" -Encoding UTF8

# 18. Test files
$jplClientTestsContent = @"
using Microsoft.VisualStudio.TestTools.UnitTesting;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Utilities;

namespace $SolutionName.Tests
{
    [TestClass]
    public class JplClientTests
    {
        [TestMethod]
        public void JplClient_Constructor_ShouldInitializeWithDefaultLogger()
        {
            // Arrange & Act
            var client = new JplClient();

            // Assert
            Assert.IsNotNull(client);
            Assert.IsFalse(client.IsConnected);
        }

        [TestMethod]
        public void JplClient_Constructor_ShouldInitializeWithCustomLogger()
        {
            // Arrange
            var logger = new ConsoleLogger();

            // Act
            var client = new JplClient(logger);

            // Assert
            Assert.IsNotNull(client);
            Assert.IsFalse(client.IsConnected);
        }

        [TestMethod]
        public void JplClient_Dispose_ShouldCleanupResources()
        {
            // Arrange
            var client = new JplClient();

            // Act & Assert - Should not throw
            client.Dispose();
        }
    }
}
"@

$jplClientTestsContent | Out-File -FilePath "$SolutionPath\tests\$SolutionName.Tests\JplClientTests.cs" -Encoding UTF8

$logonServiceTestsContent = @"
using Microsoft.VisualStudio.TestTools.UnitTesting;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Services;
using $SolutionName.Core.Utilities;
using System;

namespace $SolutionName.Tests
{
    [TestClass]
    public class LogonServiceTests
    {
        [TestMethod]
        public void LogonService_Constructor_ShouldThrowExceptionForNullClient()
        {
            // Arrange, Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => new LogonService(null));
        }

        [TestMethod]
        public void LogonService_Constructor_ShouldInitializeWithValidClient()
        {
            // Arrange
            var client = new JplClient();

            // Act
            var service = new LogonService(client);

            // Assert
            Assert.IsNotNull(service);
        }

        [TestMethod]
        public void LogonService_Constructor_ShouldInitializeWithCustomLogger()
        {
            // Arrange
            var client = new JplClient();
            var logger = new ConsoleLogger();

            // Act
            var service = new LogonService(client, logger);

            // Assert
            Assert.IsNotNull(service);
        }
    }
}
"@

$logonServiceTestsContent | Out-File -FilePath "$SolutionPath\tests\$SolutionName.Tests\LogonServiceTests.cs" -Encoding UTF8

# 19. Example usage
$exampleUsageContent = @"
using System;
using System.Threading.Tasks;
using $SolutionName.Core.Communication;
using $SolutionName.Core.Services;
using $SolutionName.Core.Utilities;
using $SolutionName.Core.Messages.Requests;

namespace $SolutionName.Examples
{
    /// <summary>
    /// Example usage of the Doms POS Client library
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            // Create logger
            var logger = new ConsoleLogger();
            logger.LogInfo(""Starting Doms POS Client Example"");

            // Create client
            using (var client = new JplClient(logger))
            {
                try
                {
                    // Subscribe to unsolicited messages
                    client.UnsolicitedMessageReceived += (sender, message) =>
                    {
                        logger.LogInfo(`$""Unsolicited message received: {message.Name}"");
                    };

                    // Connect to controller
                    logger.LogInfo(""Connecting to PSS controller..."");
                    await client.ConnectAsync(""192.168.1.100"", useTls: false);
                    
                    // Create services
                    var logonService = new LogonService(client, logger);
                    var forecourtService = new ForecourtControlService(client, logger);
                    var dispenseService = new DispenseControlService(client, logger);
                    
                    // Perform logon
                    logger.LogInfo(""Performing logon..."");
                    var logonResponse = await logonService.LogonAsync(""POS"", ""0045"", ""ExamplePOS.1.0"");
                    logger.LogInfo(`$""Logged on to controller version {logonResponse.Data.FcSwVersionNo}"");
                    
                    // Get controller status
                    logger.LogInfo(""Getting controller status..."");
                    var status = await forecourtService.GetStatusAsync();
                    logger.LogInfo(`$""Controller status - PumpTotalsReady: {status.Data.FcStatus1Flags.Bits.PumpTotalsReady}"");
                    
                    // Set date/time
                    logger.LogInfo(""Synchronizing date/time..."");
                    await forecourtService.SetDateTimeAsync(DateTime.Now);
                    logger.LogInfo(""Date/time synchronized"");
                    
                    // Get installation status
                    logger.LogInfo(""Getting installation status..."");
                    var installStatus = await forecourtService.GetInstallationStatusAsync();
                    logger.LogInfo(`$""Installed device groups: {installStatus.Data.InstalledFcDeviceGroups?.Length ?? 0}"");
                    
                    // Example fuelling point operations
                    if (installStatus.Data.InstalledFcDeviceGroups?.Length > 0)
                    {
                        // Find fuelling points
                        foreach (var deviceGroup in installStatus.Data.InstalledFcDeviceGroups)
                        {
                            if (deviceGroup.InstallMsgCode == ""10H"" || deviceGroup.ExtendedInstallMsgCode == ""0010H"") // Fuelling points
                            {
                                foreach (var fpId in deviceGroup.FcDeviceId)
                                {
                                    logger.LogInfo(`$""Working with FP {fpId}"");
                                    
                                    // Get FP status
                                    var fpStatus = await dispenseService.GetFuellingPointStatusAsync(fpId);
                                    logger.LogInfo(`$""FP {fpId} status: {fpStatus.Data.FpMainState?.Value}"");
                                    
                                    // Open FP (if not already open)
                                    if (fpStatus.Data.FpMainState?.Value != ""02H"") // Not idle
                                    {
                                        await dispenseService.OpenFuellingPointAsync(fpId, ""01"");
                                        logger.LogInfo(`$""Opened FP {fpId}"");
                                    }
                                    
                                    // Authorize FP
                                    await dispenseService.AuthorizeFuellingPointAsync(fpId, ""01"");
                                    logger.LogInfo(`$""Authorized FP {fpId}"");
                                    
                                    // Wait a bit
                                    await Task.Delay(2000);
                                    
                                    // Cancel authorization
                                    await dispenseService.CancelFuellingPointAuthorizationAsync(fpId, ""01"");
                                    logger.LogInfo(`$""Cancelled authorization for FP {fpId}"");
                                    
                                    break; // Just demo with first FP
                                }
                                break;
                            }
                        }
                    }
                    
                    // Keep connection alive for a while to receive unsolicited messages
                    logger.LogInfo(""Listening for unsolicited messages for 30 seconds..."");
                    await Task.Delay(30000);
                }
                catch (Exception ex)
                {
                    logger.LogError(`$""Application error: {ex.Message}"", ex);
                }
                finally
                {
                    logger.LogInfo(""Disconnecting..."");
                    client.Disconnect();
                }
            }

            logger.LogInfo(""Example completed"");
            Console.WriteLine(""Press any key to exit..."");
            Console.ReadKey();
        }
    }
}
"@

$exampleUsageContent | Out-File -FilePath "$SolutionPath\examples\ExampleUsage.cs" -Encoding UTF8

