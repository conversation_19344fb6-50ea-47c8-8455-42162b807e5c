﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Connection Status Messages
    public class PosConnectionStatusRequest : JplRequest
    {
        public class PosConnectionStatusData
        {
            // Empty for basic requests
        }
    }

    public class PosConnectionStatusResponse : JplResponse
    {
        public class PosConnectionStatusResponseData
        {
            [JsonPropertyName("Connections")]
            public List<Connection> Connections { get; set; }
        }

        public class Connection
        {
            [JsonPropertyName("PosDeviceType")]
            public EnumValue<string> PosDeviceType { get; set; }

            [JsonPropertyName("ConnType")]
            public EnumValue<int> ConnType { get; set; }

            [JsonPropertyName("ConnAddress")]
            public int ConnAddress { get; set; }

            [JsonPropertyName("ServerPortNo")]
            public int ServerPortNo { get; set; }

            [JsonPropertyName("ConnStatus")]
            public BitFlags ConnStatus { get; set; }
        }
    }

    // Peripheral Status Messages
    public class PssPeripheralsStatusRequest : JplRequest
    {
        public class PssPeripheralsStatusData
        {
            // Empty for basic requests
        }
    }

    public class PssPeripheralsStatusResponse : JplResponse
    {
        public class PssPeripheralsStatusResponseData
        {
            [JsonPropertyName("Peripherals")]
            public List<Peripheral> Peripherals { get; set; }
        }

        public class Peripheral
        {
            [JsonPropertyName("PeripheralType")]
            public EnumValue<string> PeripheralType { get; set; }

            [JsonPropertyName("ConnType")]
            public EnumValue<int> ConnType { get; set; }

            [JsonPropertyName("ConnAddress")]
            public int ConnAddress { get; set; }

            [JsonPropertyName("ServerPortNo")]
            public int ServerPortNo { get; set; }

            [JsonPropertyName("PeripheralStatus")]
            public BitFlags PeripheralStatus { get; set; }
        }
    }
}
