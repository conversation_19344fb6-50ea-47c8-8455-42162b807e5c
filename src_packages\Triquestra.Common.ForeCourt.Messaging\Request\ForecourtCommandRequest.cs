﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class ForecourtCommandRequest : ForecourtControlMessage
    {
        

        public ForecourtCommandRequest(int sequenceNo, int sourceId, int targetId,  ForecourtCommandMessageTypes commandType) :
            base(sequenceNo, sourceId, targetId, ForecourtMessageClasses.COMMAND_REQUEST)
        {
            CommandType = commandType;
        }

        ForecourtCommandMessageTypes CommandType { get; set; }

        public override XDocument Serialise()
        {
            var xdoc= base.Serialise();
            if (xdoc.Root == null) return xdoc;
            xdoc.Root.Add(new XElement("CommandReq", new XAttribute("Code", CommandType)));
            return xdoc;
        }

        public static ForecourtCommandRequest Create(ForecourtCommandMessageTypes commandMessageType)
        {
            return Create(1, 0, commandMessageType);
        }

        public static ForecourtCommandRequest Create(int sourceId, int targetId, ForecourtCommandMessageTypes commandMessageType)
        {
            return new ForecourtCommandRequest(1, sourceId, targetId, commandMessageType);
        }
    }
}
