﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventNozzleStateChangeMessage:ForecourtEventMessage
    {
        public int NozzleId { get; set; }
        public NozzleStates NozzleState { get; set; }
        public int DispenserId { get { return SourceId; } set { SourceId = value; } }
        public EventNozzleStateChangeMessage(int sequenceNo, int sourceId, int targetId, int nozzleId, NozzleStates nozzleState) : 
            base(sequenceNo, sourceId, targetId, EventMessageTypes.NOZZLE_STATE_CHANGE)
        {
            NozzleId = nozzleId;
            NozzleState = nozzleState;
        }

        public static EventNozzleStateChangeMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var nozzleStateChangeNode = eventNode.Element("NozzleStateChange");
            if (nozzleStateChangeNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <NozzleStateChange> node");
            }
            var nozzleId = int.Parse(nozzleStateChangeNode.Attribute("NozzleID").Value);
            var ns =
                (NozzleStates)
                    Enum.Parse(typeof(NozzleStates), nozzleStateChangeNode.Attribute("State").Value);
            return new EventNozzleStateChangeMessage(seqNo, sourceId, targetId, nozzleId, ns);
        }
    }
}
