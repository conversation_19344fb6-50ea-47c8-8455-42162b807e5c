﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Globalization;

namespace Triquestra.Common.PumpEsm.Base
{
    public class ConfigGetItemDataResult : PumpEsmActionResult
    {
        public ConfigGetItemDataResult(int pumpHandle, string upc, decimal qty, decimal subTotal, decimal price)
            : base()
        {
            Result = true;

            PumpHandle = pumpHandle;
            UPC = upc;
            Quantity = qty;
            SubTotal = subTotal;
            Price = price;
        }

        public ConfigGetItemDataResult(int pumpHandle, string upc, decimal qty, decimal subTotal, decimal price,
            string pumpId, int transHandle)
            : this(pumpHandle, upc, qty, subTotal, price)
        {
            PumpId = pumpId;
            TransHandle = transHandle;
        }

        public override string ResultText
        {
            get
            {
                //sample line: 0,Z91,5.550,10.462,1.88,11,22,33,44,55,66,77,88,99,101,102
                string[] list = new string[14];
                list[0] = PumpHandle.ToString(); //0
                list[1] = UPC; //Z91
                list[2] = Quantity.ToString(CultureInfo.InvariantCulture); //5.550
                list[3] = SubTotal.ToString(CultureInfo.InvariantCulture); //10.462
                list[4] = Price.ToString(CultureInfo.InvariantCulture); //1.88
                list[5] = FieldText1; //11
                list[6] = PumpId; //22
                list[7] = TransHandle.ToString();//33
                list[8] = Cost.HasValue ? Cost.Value.ToString(CultureInfo.InvariantCulture) : string.Empty; // 44
                list[9] = FieldText2; // 55
                list[10] = FieldText3; // 66
                list[11] = ESMNote; // 77
                list[12] = FieldText4;
                list[13] = FieldText5;
                return string.Join(",", list);
            }
            set
            {
                throw new NotImplementedException();
            }
        }

        public int PumpHandle { get; set; }

        public decimal Quantity { get; set; }

        public decimal SubTotal { get; set; }

        public decimal Price { get; set; }
        
        public string PumpId { get; set; }

        public int TransHandle { get; set; }

        public string UPC { get; set; }

        public string FieldText1 { get; set; }

        public string FieldText2 { get; set; }

        public string FieldText3 { get; set; }

        public string FieldText4 { get; set; }

        public string FieldText5 { get; set; }

        public string ESMNote { get; set; }

        public decimal? Cost { get; set; }
    }
}
