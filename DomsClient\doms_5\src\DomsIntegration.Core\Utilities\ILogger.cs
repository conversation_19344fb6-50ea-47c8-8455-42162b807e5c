﻿using System;

namespace DomsIntegration.Core.Utilities
{
    /// <summary>
    /// Interface for logging functionality
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogDebug(string message);

        /// <summary>
        /// Logs an information message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name=""message"">Message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error message with exception details
        /// </summary>
        /// <param name=""message"">Message to log</param>
        /// <param name=""exception"">Exception to log</param>
        void LogError(string message, Exception exception);
    }
}
