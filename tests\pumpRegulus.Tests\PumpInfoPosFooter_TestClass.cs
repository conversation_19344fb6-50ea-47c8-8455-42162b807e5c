﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Triquestra.Common.PumpEsm;
using Triquestra.Common.PumpEsm.RegulusInterface;
using Moq;
using Triquestra.Common.PumpEsm.Types;
using Triquestra.Common.PumpEsm.Controls;
using Triquestra.Common.PumpEsm.Helpers;
using System.Collections.Generic;
using System.Windows.Forms;
using WinFormUi.Controls;
using System.Linq;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.Database;
using Triquestra.Common.PumpEsm.DataAccess;
using System.Reflection;
using Triquestra.Common.PumpEsm.Messaging.Response;
using Triquestra.Common.PumpEsm.Messaging.Event;
using System.Threading;
using Triquestra.Common.GUI;

namespace pumpRegulus.Tests
{
    [TestClass]
    public class PumpInfoPosFooter_TestClass
    {

        PumpInfoPosFooter _form;

        Mock<IRegulus> _regulus;

        Mock<IPumpDataProvider> _pumpDataProvider;

        Mock<IPumpController> _pumpController;

        Mock<IDigifortHelper> _digifortHelper;

        <PERSON><PERSON><IGuiHelper> _guiHelper;

        int _dispenserId = 102;

        int _workstationId = 103;

        int _blend1Id = 104;

        int _blend2Id = 105;

        int _blend3Id = 106;

        string _blend1Name = "A-91";

        string _blend2Name = "PREM";

        string _blend3Name = "DISEL";

        string _upc1 = "91";

        string _upc2 = "98";

        string _upc3 = "DSL";

        DispenserUi _dispenser;

        string _upcSent;

        List<int> _denominations = new List<int>() { 10, 20, 30, 50, 100 };

        IForecourtBlend _firstBlend;

        #region Init/Cleanup

        [TestInitialize]

        public void PumpInfoPosFooter_TestClass_Init()
        {
            _guiHelper = new Mock<IGuiHelper>();

            _pumpDataProvider = new Mock<IPumpDataProvider>();

            _pumpDataProvider.SetupGet(t => t.WorkstationId)
                .Returns(_workstationId);

            _pumpDataProvider.SetupGet(t => t.Denominations)
                .Returns(_denominations);

            _pumpController = new Mock<IPumpController>();

            _regulus = new Mock<IRegulus>();

            _regulus.SetupGet(t => t.PumpController)
                .Returns(_pumpController.Object);

            _digifortHelper = new Mock<IDigifortHelper>();

            _form = new PumpInfoPosFooter(_pumpDataProvider.Object, _regulus.Object, _digifortHelper.Object, _guiHelper.Object, 1000);

            _form.PanelDispensers = new System.Windows.Forms.FlowLayoutPanel();

            _form.SendScanCode = (upc) => _upcSent = upc;

            _dispenser = new DispenserUi(_guiHelper.Object);

            _dispenser.DispenserId = _dispenserId;

            _firstBlend = new ForecourtBlend(_blend1Id, _blend1Name);

            _dispenser.Blends = new List<IForecourtBlend>
                {
                    _firstBlend,
                    new ForecourtBlend(_blend2Id, _blend2Name),
                    new ForecourtBlend(_blend3Id, _blend3Name)
                };

            _dispenser.Connect(_form);

            _form.PanelDispensers.Controls.Add(_dispenser);

        }

        #endregion

        #region Ctors

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Ctor_PumpProviderAndScreenWidth()
        {
            int screenWidth = 100;

            string title = "Digi";

            #region setup pump data provider

            _pumpDataProvider.SetupGet<HashSet<int>>(t => t.RetailPumps)
                .Returns(new HashSet<int>() { 1 });

            _pumpDataProvider.SetupGet(t => t.DigifortTitle)
                .Returns(title);

            _pumpDataProvider.SetupGet(t=>t.TcpAddress)
                .Returns( "*************");

            _pumpDataProvider.SetupGet(t=>t.TcpPort).Returns( 6050 );

            _pumpDataProvider.SetupGet(t=>t.UdpAddress).Returns( "**********" );

            _pumpDataProvider.SetupGet(t=>t.UdpPort).Returns( 8051 );

            #endregion

            _form = new PumpInfoPosFooter(_pumpDataProvider.Object, screenWidth) ;

            Assert.IsNotNull(_form._regulus, "Regulus not assigned");

            Assert.IsNotNull(_form._digifort, "Digiforce not assigned");

            Assert.AreEqual( _pumpDataProvider.Object, _form._pumpDataProvider, "Pump data provider not assigned");

            Assert.IsNotNull(_form._guiHelper, "GuiHelper should be created");

        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Ctor_PumpProviderRegusDigiHelperAndScreenWidth()
        {
            int screenWidth = 100;

            _form = new PumpInfoPosFooter(_pumpDataProvider.Object, _regulus.Object, _digifortHelper.Object, _guiHelper.Object, screenWidth);

            Assert.AreEqual(_pumpDataProvider.Object, _form._pumpDataProvider, "Pump provider not set");

            Assert.IsNotNull(_form._regulus, "Regulus not assigned");

            Assert.IsNotNull(_form._digifort, "Digiforce not assigned");

            Assert.AreEqual(_pumpDataProvider.Object, _form._pumpDataProvider, "Pump data provider not assigned");

            Assert.AreEqual(screenWidth, _form.Width, "Screen width should be set");
        }

        #endregion

        #region TapPump

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopUp_BtnPrepayUp_DispenserStateByDefault()
        {
            _form.BtnStop.Down = false;

            _form.BtnPlay.Down = false;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.CALLING;

            _form.TapPump(_dispenserId);

            _pumpController.Verify( t=> t.Authorize(_dispenserId), Times.Once(), "Authorize should be called for the pump controller");

        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopUp_BtnPrepayUp_DispenserStateIsSuspended()
        {
            _form.BtnStop.Down = false;

            _form.BtnPlay.Down = true;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.SUSPENDED;

            _form.TapPump(_dispenserId);

            _pumpController.Verify(t => t.Resume(_dispenserId), Times.Once(), "Resume should be called for pump controller");

            _pumpDataProvider.Verify(t => t.ReportPumpSuspendAction(false, _dispenserId), Times.Once(), "ReportPumpSuspendAction should be called for pump data");

            _digifortHelper.Verify(t => t.NotifySuspend(false, _dispenserId), Times.Once(), "Digifort should send notification");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopUp_BtnPrepayUp_DispenserStateIsCalling()
        {
            _form.BtnStop.Down = false;

            _form.BtnPlay.Down = false;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.CALLING;

            _form.TapPump(_dispenserId);

            _pumpController.Verify(t=>t.Authorize(_dispenserId), Times.Once(), "Authorize should be called for pump contoller");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopDown_BtnPrepayUp_DispenserStateIsStopped()
        {
            _form.BtnStop.Down = true;

            _form.BtnPlay.Down = false;

            _form.TapPump(_dispenserId);

            Assert.IsFalse(_form.BtnStop.Down, "Button Down expected as FALSE");

            _pumpController.Verify(t => t.Suspend(_dispenserId), Times.Once(), "Suspend should be called for the pump controller");

            _pumpDataProvider.Verify(t => t.ReportPumpSuspendAction(true, _dispenserId), Times.Once(), "ReportPumpSuspendAction should be called for pump data provider");

            _digifortHelper.Verify(t => t.NotifySuspend(true, _dispenserId), Times.Once(), "Digifort helper should send notification");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopDown_BtnPrepayUp_DispenserStateIsSuspended()
        {
            _form.BtnStop.Down = true;

            _form.BtnPlay.Down = false;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.SUSPENDED;

            _form.TapPump(_dispenserId);

            Assert.IsFalse(_form.BtnStop.Down, "Button Down expected as FALSE");

            _pumpController.Verify(t => t.Suspend(_dispenserId), Times.Never(), "Suspend should NOT be called for the pump controller");

            _pumpDataProvider.Verify(t => t.ReportPumpSuspendAction(true, _dispenserId), Times.Never(), "ReportPumpSuspendAction should NOT be called for pump data provider");

            _digifortHelper.Verify(t => t.NotifySuspend(true, _dispenserId), Times.Never(), "Digifort helper should NOT send notification");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopUp_BtnPrepayDown_DispenserStateIsSuspended()
        {
            _form.BtnStop.Down = false;

            _form.BtnPlay.Down = true;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.SUSPENDED;

            _form.TapPump(_dispenserId);

            Assert.IsFalse(_form.BtnPlay.Down, "ButPlay Down expected as FALSE");

            _pumpController.Verify( t=> t.Resume(_dispenserId), Times.Once(), "Resume should be called for PumpController");

            _pumpDataProvider.Verify(t=>t.ReportPumpSuspendAction(false, _dispenserId), Times.Once(), "ReportPumpSuspendAction should be called");

            _digifortHelper.Verify( t=> t.NotifySuspend(false, _dispenserId), Times.Once(), "DigifortHelper should send notification with DispenserID");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapPump_BtnStopUp_BtnPrepayDown_DispenserStateIsStopped()
        {
            _form.BtnStop.Down = false;

            _form.BtnPlay.Down = true;

            _dispenser.DispenserState = Triquestra.Common.PumpEsm.Messaging.DispenserStates.STOPPED;

            _form.TapPump(_dispenserId);

            Assert.IsFalse(_form.BtnPlay.Down, "ButPlay Down expected as FALSE");

            _pumpController.Verify(t => t.Resume(_dispenserId), Times.Never(), "Resume should NOT be called for PumpController");

            _pumpDataProvider.Verify(t => t.ReportPumpSuspendAction(false, _dispenserId), Times.Never(), "ReportPumpSuspendAction should NOT be called");

            _digifortHelper.Verify(t => t.NotifySuspend(false, _dispenserId), Times.Never(), "DigifortHelper should NOT send notification");
        }

        #endregion

        #region TapForPrepay

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapForPrepay_NoPrepayForm()
        {
            var actual = _form.TapForPrepay(_dispenser);

            Assert.IsTrue(actual, "We expect new Prepay form created");

            Assert.IsNotNull(_form.PrepayForm, "Prepay form should be assigned");

            Assert.AreEqual(_dispenserId, _form.PrepayForm.DispenserId, "DispenserID should be assigned to Prepay form");

            Assert.IsTrue(Application.OpenForms.OfType<PrepayForm>().Any(), "Prepay form should be opened");

            _form.PrepayForm.Close();
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_TapForPrepay_PrepayFormAlreadyAssigned()
        {
            _form.PrepayForm = new PrepayForm();

            var actual = _form.TapForPrepay(_dispenser);

            Assert.IsFalse(actual, "We DON'T expect new Prepay form created");

            //_guiHelper
        }

        #endregion

        #region ApplyPrepay

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_ApplyPrepay()
        {
            int blendId = 2;

            string blendName = "A-91";

            string fuelUPC = "A91";

            int nozzleId = 3;

            #region setup

            Prepay prepayData = new Prepay()
            {
                Amount = 10,
                Blend = new ForecourtBlend( blendId, blendName),
                DispenserId = _dispenserId
            };


            Items item = new Items()
            {
                Description = "Fuel",
                UPC = fuelUPC,
                Price1 = 1.55m
            };

            _pumpDataProvider.Setup(t => t.GetItemByBlendId(blendId))
                .Returns(item);

            List<Fuel_Pump> pumps = new List<Fuel_Pump>()
            {
                new Fuel_Pump() { ID = _dispenserId, H1 = nozzleId  },
                new Fuel_Pump() { ID = _dispenserId + 1 }
            };

            _pumpDataProvider.SetupGet(t => t.Dispensers)
                .Returns(pumps);

            List<Fuel_Hose> hoses = new List<Fuel_Hose>()
            {
                new Fuel_Hose() { ID = nozzleId, PumpID = _dispenserId, UPC = fuelUPC  },
                new Fuel_Hose() { PumpID = _dispenserId, UPC = fuelUPC+"1" }
            };

            _pumpDataProvider.SetupGet(t => t.Nozzles)
                .Returns(hoses);

            #endregion

            _form.ApplyPrepay(prepayData);

            _pumpController.Verify(t=> t.Reserve( _workstationId, prepayData.DispenserId, prepayData.Amount, prepayData.Blend)
                , Times.Once(), "Reserve should be called for pump controller" );

            FieldInfo fi = typeof(PumpInfoPosFooter).GetField("_reservedDelivery", BindingFlags.NonPublic | BindingFlags.Instance);

            var toCheck = fi.GetValue(_form);

            Assert.IsNotNull(toCheck, "Delivery should be set");

            Assert.AreEqual(item.Price1, (toCheck as Delivery).Price, "Invalid price");
        }

        #endregion

        #region SendDeliveryAsync

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_SendDeliveryAsync_NoPendingDelivery()
        {
            Mock<IDelivery> delivery = new Mock<IDelivery>();

            delivery.SetupGet(d => d.DispenserId)
                .Returns(_dispenserId);

            _form.SendDeliveryAsync(delivery.Object);

            FieldInfo fi = typeof(PumpInfoPosFooter).GetField("_pendingDelivery", BindingFlags.NonPublic | BindingFlags.Instance);

            var toCheck = fi.GetValue(_form);

            Assert.IsNotNull(toCheck, "PendingDelivery should be set");

            _pumpController.Verify(t => t.LockDelivery(delivery.Object.DispenserId, delivery.Object.DeliveryId), Times.Once(), "LockDelivery should be called for pumpController");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_SendDeliveryAsync_PendingDeliveryAlreadySet()
        {
            Mock<IDelivery> toCreatePending = new Mock<IDelivery>();

            Mock<IDelivery> delivery = new Mock<IDelivery>();

            delivery.SetupGet(d => d.DispenserId)
                .Returns(_dispenserId);

            FieldInfo fi = typeof(PumpInfoPosFooter).GetField("_pendingDelivery", BindingFlags.NonPublic | BindingFlags.Instance);

            PendingDelivery pendingDelivery = new PendingDelivery(toCreatePending.Object);

            fi.SetValue(_form, pendingDelivery);

            _form.SendDeliveryAsync(delivery.Object);

            _pumpController.Verify(t => t.LockDelivery(delivery.Object.DispenserId, delivery.Object.DeliveryId), Times.Never(), "LockDelivery should NOT be called for pumpController");
        }

        #endregion

        #region ReserveReceived

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_ReserveReceived()
        {
            int seqNo = 10;

            int sourceId = 11;

            int targetId = 12;

            int blendId = 13;

            string upc = "aa";

            ForecourtCommandResults commandResult = new ForecourtCommandResults();

            ReserveResponse reserveResponse = new ReserveResponse(seqNo, sourceId, targetId, commandResult);

            FieldInfo fi = typeof(PumpInfoPosFooter).GetField("_reservedDelivery", BindingFlags.NonPublic | BindingFlags.Instance);

            Delivery delivery = new Delivery()
            {
                BlendId = blendId
            };

            fi.SetValue(_form, delivery);

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(delivery.BlendId))
                .Returns(upc);


            _form.PumpController_ReserveReceived(reserveResponse, null );

            _form.ProcessAsyncQueue();

            Assert.AreEqual(_upcSent, upc, "Upc should be sent");
        }

        #endregion

        #region DeliveryLockReceived

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_DeliveryLockReceived()
        {
            int seqNo = 10;

            int sourceId = 11;

            int blendId = 13;

            int deliveryId = 14;

            string upc = "aa";

            ForecourtCommandResults commandResult = new ForecourtCommandResults();

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(blendId))
                .Returns( upc );

            DeliveryLockResponse deliveryLockResponse = new DeliveryLockResponse(seqNo, sourceId, _dispenserId, commandResult, deliveryId);

            Delivery delivery = new Delivery()
            {
                BlendId = blendId,
                DeliveryId = deliveryId,
                DispenserId = _dispenserId
            };

            _form._pendingDelivery = new PendingDelivery(delivery);

            _form.PumpController_DeliveryLockReceived(deliveryLockResponse, null);

            _form.ProcessAsyncQueue();

            Assert.AreEqual(_upcSent, upc, "Upc should be sent");

            Assert.IsNotNull(_form._activeDelivery, "ActiveDelivery should be set");
        }

        #endregion

        #region Delivery withoup prepay - whole cycle from filling the fuel to adding items into the sale

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_DeliveryFrom3NozzlesOnTheSamePump()
        {
            int seqNo = 10;

            int targetId = 11;

            int deliveryId = 14;

            int delivery2Id = 15;

            int delivery3Id = 16;

            #region mocking

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(_blend1Id))
                .Returns(_upc1);

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(_blend2Id))
                .Returns(_upc2);

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(_blend3Id))
                .Returns(_upc3);

            #endregion

            #region Process first delivery

            Delivery delivery = new Delivery()
            {
                BlendId = _blend1Id,
                DeliveryId = deliveryId,
                DispenserId = _dispenserId
            };

            EventDeliveryStartedMessage deliveryStartedMessage = new EventDeliveryStartedMessage(seqNo, _dispenserId, targetId, delivery);

            //_form._pendingDelivery = new PendingDelivery(delivery);

            _form.PumpController_DeliveryStarted(deliveryStartedMessage, null);

            _form.ProcessAsyncQueue();

            EventDeliveryStateChangeMessage deliveryStateChangedMessage = 
                new EventDeliveryStateChangeMessage(seqNo, _dispenserId, targetId, deliveryId, DeliveryStates.COMPLETED);

            _form.PumpController_DeliveryStateChanged(deliveryStateChangedMessage, null);

            _form.ProcessAsyncQueue();

            Assert.IsNull(_form._activeDelivery, "ActiveDelivery should NOT be set");

            Assert.AreEqual(1, _dispenser.DeliveryList.Count(), "1 delivery expected");

            Assert.IsFalse(_dispenser.PnlStack.Visible, "Delivery STACK info should NOT be visible");

            #endregion

            #region Process second delivery

            Delivery secondDelivery = new Delivery()
            {
                BlendId = _blend2Id,
                DeliveryId = delivery2Id,
                DispenserId = _dispenserId
            };

            EventDeliveryStartedMessage secondDeliveryStartedMessage = 
                new EventDeliveryStartedMessage(seqNo, _dispenserId, targetId, secondDelivery);

            _form.PumpController_DeliveryStarted(secondDeliveryStartedMessage, null);

            _form.ProcessAsyncQueue();

            EventDeliveryStateChangeMessage delivery2StateChangedMessage =
                new EventDeliveryStateChangeMessage(seqNo, _dispenserId, targetId, delivery2Id, DeliveryStates.COMPLETED);

            _form.PumpController_DeliveryStateChanged(delivery2StateChangedMessage, null);

            _form.ProcessAsyncQueue();

            Assert.AreEqual(2, _dispenser.DeliveryList.Count(), "2 deliveries expected");

            Assert.IsTrue(_dispenser.PnlStack.Visible, "Delivery info should be visible");

            #endregion

            #region Process third delivery

            Delivery thirdDelivery = new Delivery()
            {
                BlendId = _blend3Id,
                DeliveryId = delivery3Id,
                DispenserId = _dispenserId,
                State = DeliveryStates.COMPLETED
            };

            EventDeliveryStartedMessage thirdDeliveryStartedMessage =
                new EventDeliveryStartedMessage(seqNo, _dispenserId, targetId, thirdDelivery);

            _form.PumpController_DeliveryStarted(thirdDeliveryStartedMessage, null);

            _form.ProcessAsyncQueue();

            EventDeliveryStateChangeMessage delivery3StateChangedMessage =
                new EventDeliveryStateChangeMessage(seqNo, _dispenserId, targetId, delivery3Id, DeliveryStates.COMPLETED);

            _form.PumpController_DeliveryStateChanged(delivery3StateChangedMessage, null);

            _form.ProcessAsyncQueue();

            Assert.AreEqual(3, _dispenser.DeliveryList.Count(), "3 deliveries expected");

            Assert.IsTrue(_dispenser.PnlStack.Visible, "Delivery info should be visible");

            #endregion

            //
            // add into sale
            //

            #region Last delivery

            // click on pump - last delivery should be processed

            _dispenser.PnlLight_Click(null, null);

            _pumpController.Verify(t => t.LockDelivery(_dispenserId, delivery3Id), Times.Once(), "Lock command should be sent to the pump dispenser for the last delivery");

            DeliveryLockResponse delivery3LockedResponse = new DeliveryLockResponse(seqNo, _dispenserId, _dispenserId,
                ForecourtCommandResults.SUCCESS, delivery3Id);

            _form.PumpController_DeliveryLockReceived(delivery3LockedResponse, null);

            _form.ProcessAsyncQueue();

            Assert.IsNotNull(_form._activeDelivery, "ActiveDelivery must be assigned");

            Assert.IsNull(_form._pendingDelivery, "Pending delivery must be set to null");

            Assert.AreEqual(_upcSent, _upc3, "Send should be called for _upc3");

            #endregion

            #region first delivery

            // click on stack button - first item in stack should be set to the sale

            _dispenser.PnlStack_Click(null, null);

            _pumpController.Verify(t => t.LockDelivery(_dispenserId, deliveryId), Times.Once(), "Lock command should be sent to the pump dispenser for first delivery");

            DeliveryLockResponse deliveryLockedResponse = new DeliveryLockResponse(seqNo, _dispenserId, _dispenserId,
                ForecourtCommandResults.SUCCESS, deliveryId);

            _form.PumpController_DeliveryLockReceived(deliveryLockedResponse, null);

            _form.ProcessAsyncQueue();

            Assert.IsNotNull(_form._activeDelivery, "ActiveDelivery must be assigned");

            Assert.IsNull(_form._pendingDelivery, "Pending delivery must be set to null");

            Assert.AreEqual(_upcSent, _upc1, "Send should be called for _upc1");

            #endregion

            #region Second delivery

            // todo According to Andrei - external setup should not sent more than 2 deliveries from the pump. So this code is commented.
            
            // second click on stack button should send second item in stack to the sale

            //_dispenser.PnlStack_Click(null, null);

            //_pumpController.Verify(t => t.LockDelivery(_dispenserId, delivery2Id), Times.Once(), "Lock command should be sent to the pump dispenser for second delivery");

            //DeliveryLockResponse delivery2LockedResponse = new DeliveryLockResponse(seqNo, _dispenserId, _dispenserId,
            //    ForecourtCommandResults.SUCCESS, delivery2Id);

            //_form.PumpController_DeliveryLockReceived(delivery2LockedResponse, null);

            //_form.ProcessAsyncQueue();

            #endregion
        }

        #endregion


        #region Delivery with prepay

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Prepay_SalesNotAllowed()
        {
            _form.AllowSales = false;

            _form.BtnPrepay_Click(null, null);

            Assert.IsFalse(_form.BtnPrepay.Down, "Prepay button should be UP as AllowSales is false");
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Prepay_PumpIsNotInIdleState()
        {
            _form.BtnPrepay_Click(null, null);

            _form.AllowSales = true;

            Assert.IsTrue(_form.BtnPrepay.Down, "Prepay button should be DOWN as AllowSales is true");

            _dispenser.PnlLight_Click(null, null);

            _guiHelper.Verify(t => t.ShowMessage(DispenserUi.MessagePumpCannotBeReserved, MessageBoxIcon.Error),
                Times.Once(), "Popup message expected: " + DispenserUi.MessagePumpCannotBeReserved);
        }

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Prepay_PumpIsInIdleState()
        {
            _form.BtnPrepay_Click(null, null);

            _form.AllowSales = true;

            _dispenser.DispenserState = DispenserStates.IDLE;

            int nozzleId = 222;

            Items itemForBlend1 = new Items() { UPC = _upc1, Price1 = 1.5m };

            #region mocking

            _pumpDataProvider.Setup(t => t.GetItemByBlendId(_blend1Id))
                .Returns(itemForBlend1);

            List<Fuel_Pump> pumps = new List<Fuel_Pump>()
            {
                new Fuel_Pump() { ID = _dispenserId, H1 = nozzleId  },
                new Fuel_Pump() { ID = _dispenserId + 1, H1 = nozzleId+10 }
            };

            _pumpDataProvider.SetupGet(t => t.Dispensers)
                .Returns(pumps);

            List<Fuel_Hose> hoses = new List<Fuel_Hose>()
            {
                new Fuel_Hose() { ID = nozzleId, PumpID = _dispenserId, UPC = _upc1  },
                new Fuel_Hose() { ID = nozzleId +1, PumpID = _dispenserId, UPC = _upc2 }
            };

            _pumpDataProvider.SetupGet(t => t.Nozzles)
                .Returns(hoses);

            #endregion

            Assert.IsTrue(_form.BtnPrepay.Down, "Prepay button should be DOWN as AllowSales is true");

            _dispenser.PnlLight_Click(null, null);

            Assert.IsTrue(Application.OpenForms.OfType<PrepayForm>().Any(), "Prepay form should be opened");

            OnScreenStateButton blendToSelect = _form.PrepayForm.pnlBlends.Controls.OfType<OnScreenStateButton>()
                .FirstOrDefault(t => t.DisplayValue == _blend1Name);

            _form.PrepayForm.OnChooseBlend(blendToSelect, null);

            OnScreenStateButton denominationToSelect = _form.PrepayForm.PnlValues.Controls.OfType<OnScreenStateButton>()
                .FirstOrDefault(t => t.DisplayValue == "$"+_denominations[0].ToString());

            _form.PrepayForm.btnValue_Click(denominationToSelect, null);

            Assert.IsNull(_form.PrepayForm, "Prepay form should be closed");

            //_pumpController.Verify( t=>t.Reserve(_workstationId, _dispenserId, (decimal)_denominations[0], _firstBlend),
            //    Times.Once(), "Reserve NOT sent or sent to the wrong NOZZLE");
        }

        #endregion

        #region Performance

        [TestMethod]
        public void PumpInfoPosFooter_TestClass_Performance_Test01()
        {
            int pumpCnt = 5;
            int cnt = 2;

            _pumpDataProvider.Setup(t => t.GetUPCforBlendId(_blend1Id))
                .Returns(_upc1);

            for(int p=0;p<pumpCnt;p++)
            {
                for(int m=0;m<cnt;m++)
                {
                    var delivery = new Mock<IDelivery>();

                    delivery.Setup(t => t.BlendId).Returns(_blend1Id);

                    delivery.Setup(t => t.NozzleId).Returns(m);

                    _form.SendDelivery(delivery.Object);
                }
            }
            
        }

        #endregion
    }
}
