﻿using System;

namespace DomsIntegration.Utilities
{
    /// <summary>
    /// Interface for logging operations
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogDebug(string message);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error message with exception
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Exception details</param>
        void LogError(string message, Exception exception);
    }

    /// <summary>
    /// Null logger implementation that does nothing
    /// </summary>
    public class NullLogger : ILogger
    {
        /// <inheritdoc />
        public void LogInfo(string message) { }

        /// <inheritdoc />
        public void LogDebug(string message) { }

        /// <inheritdoc />
        public void LogWarning(string message) { }

        /// <inheritdoc />
        public void LogError(string message) { }

        /// <inheritdoc />
        public void LogError(string message, Exception exception) { }
    }

    /// <summary>
    /// Console logger implementation
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        /// <inheritdoc />
        public void LogInfo(string message)
        {
            Console.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogDebug(string message)
        {
            Console.WriteLine($"[DEBUG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogWarning(string message)
        {
            Console.WriteLine($"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogError(string message)
        {
            Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogError(string message, Exception exception)
        {
            Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            Console.WriteLine($"Exception: {exception}");
        }
    }
}
