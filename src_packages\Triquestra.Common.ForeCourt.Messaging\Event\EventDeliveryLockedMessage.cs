﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryLockedMessage : ForecourtEventMessage
    {
        /// <summary>
        /// The delivery ID 
        /// </summary>
        public int DeliveryId { get; set; }
        /// <summary>
        /// The workstation owning the transaction
        /// </summary>
        public int WorkstationId { get; set; }

        public EventDeliveryLockedMessage(int sequenceNo, int sourceId, int targetId, int deliveryId, int ownerWorkstationId) : 
            base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_LOCKED)
        {
            DeliveryId = deliveryId;
            WorkstationId = ownerWorkstationId;
        }

        public static EventDeliveryLockedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryLocked = eventNode.Element("DeliveryLocked");
            if (deliveryLocked == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryLocked> node");
            }
            var deliveryId = int.Parse(deliveryLocked.Attribute("DeliveryID").Value);
            var lockedWs = int.Parse(deliveryLocked.Attribute("Owner").Value);
            return new EventDeliveryLockedMessage(seqNo, sourceId, targetId, deliveryId, lockedWs);
        }        
    }
}
