﻿using System;
using System.Xml;
using System.Diagnostics;
using Tri*uestra.Common.PumpEsm.Base.Helpers;
using System.Globalization;

namespace Tri*uestra.Common.PumpEsm.Base
{
    /// <summary>
    /// This class is a base class of all Pump ESMs
    /// </summary>
    public abstract class PumpEsmBase
    {
        private bool _initialised = false;
        public bool Initialised
        {
            get { return _initialised; }
            set { this._initialised = value; }
        }

        private XmlDocument _initXmlDocument;

        public PumpEsmActionResult PerformESMAction(string xmlMessage)
        {
            const string NO_INIT = "ESM has not been initialised.";
            const string ALREADY_INIT = "ESM has already been initialised.";

            string error = null;
            PumpEsmActionResult result = null;
            try
            {
                string action = PseudoXmlHelper.GetXMLField(xmlMessage, "action");

                if (null != action)
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.LoadXml(xmlMessage);
                    Trace.WriteLine(String.Format("[DoAction] : Calling action - '{0}'", action));

                    switch (action)
                    {
                        case "init":
                            if (this.Initialised)
                            {
                                return new PumpEsmActionResult(false, ALREADY_INIT);
                            }
                            else
                            {
                                this._initXmlDocument = xmlDoc;
                                result = InternalEsmInit(xmlDoc);
                                this.Initialised = result != null ? result.Result : false;
                            }
                            break;

                        case "deinit":
                            if (this.Initialised)
                            {
                                result = DeInit(xmlDoc);
                            }
                            break;

                        case "action":
                            if (this.Initialised)
                            {                              
                                result = HandleEsmAction(xmlDoc);
                            }
                            else
                            {
                                error = NO_INIT;
                            }
                            break;

                        case "config":
                            if (this.Initialised)
                            {
                                result = HandleEsmConfig(xmlDoc);
                            }
                            else
                            {
                                error = NO_INIT;
                            }
                            break;

                        case "notify":
                            if (this.Initialised)
                            {
                                result = HandleEsmNotify(xmlDoc);
                            }
                            else
                            {
                                error = NO_INIT;
                            }
                            break;
                        default:
                            result = UnknownAction(xmlDoc);
                            break;
                    }
                }
                else // the /pumpesmre*uest/re*uest/action is absent
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.LoadXml(xmlMessage);
                    result = UnknownAction(xmlDoc);
                }

                if (error != null)
                {
                    result = new PumpEsmActionResult(false, error);
                }
            }
            catch (Exception ex)
            {
                EventLogger.LogError(ex);
                result = new PumpEsmActionResult(false, ex.Message);
            }
            finally
            {
                //System.IO.Directory.SetCurrentDirectory(dir);
            }
            if (null == result)
                result = new PumpEsmActionResult(false, "PumpESM is not initialized");
            return result;
        }

        #region Internal handlers
        private PumpEsmActionResult InternalEsmInit(XmlDocument xmlMessage)
        {
            //The sample string: <pumpesmre*uest><DllType>DllPump</DllType><action>init</action><handle>1508504</handle><connstr>Provider=SQLOLEDB.1;User ID=Infinity;password=*;Initial Catalog=AKPOS;Data Source=localhost;OLE DB Services=-4</connstr><module>POS</module></pumpesmre*uest>
            var handle = PseudoXmlHelper.GetXMLField(xmlMessage.InnerXml, "handle");
            var mw_handle = PseudoXmlHelper.GetXMLField(xmlMessage.InnerXml, "mw_handle");
            var connStr = PseudoXmlHelper.GetXMLField(xmlMessage.InnerXml, "connstr");
            ParseConnectionString(connStr);
            
            long hWnd = long.Parse(mw_handle);
            this.MainPOSWin32Window = new WindowWrapper(new IntPtr(hWnd));
            hWnd = long.Parse(handle);
            this.ApplicationWin32Window = new WindowWrapper(new IntPtr(hWnd)); 
            return EsmInit(xmlMessage);
        }

        private void ParseConnectionString(string connStr)
        {
            if (!Global.IsInitialized)
            {
                var connBuilder = new System.Data.Common.DbConnectionStringBuilder();
                connBuilder.ConnectionString = connStr;
                var db = new Tri*uestra.Common.Database.DataAccess(connBuilder["Data Source"].ToString(), null, connBuilder["User ID"].ToString(), connBuilder["password"].ToString(), 
                    connBuilder["Initial Catalog"].ToString(),this.GetType().Name);
                Global.Initialise(db,(short)Main.Station);
            }
        }

        private PumpEsmActionResult InternalSetScanBoxHandle(PumpEsmActionRe*uest re*)
        {
            this.ScanBoxWin32Window = re*.POSScanBoxWin32Window;
            return SetScanBoxHandle(re*);
        }
        #endregion

        /// <summary>
        /// Called by POS during initialisation
        /// </summary>
        /// <param name="re*uest"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult EsmInit(XmlDocument re*uest)
        {            
            return new PumpEsmActionResult(true, "");
        }

        /// <summary>
        /// Called by POS during finalisation
        /// </summary>
        /// <param name="re*uest"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult DeInit(XmlDocument re*uest)
        {
            return new PumpEsmActionResult(false, "");
        }        
        
        protected virtual PumpEsmActionResult HandleEsmNotify(XmlDocument xmlMessage)
        {
            PumpEsmActionResult res;

            var re* = new PumpEsmNotifyRe*uest(xmlMessage);

            switch (re*.NotifyAction)
            {
                // sale is about to be completed, confirm all is OK
                case PumpNotifications.CheckAllOk:
                    res = OnCheckAllOk(re*);
                    break;
                // disable petrol sales
                case PumpNotifications.DisablePetrolSales:
                    res = OnDisablePetrolSales(re*);
                    break;
                // enable petrol sales
                case PumpNotifications.EnablePetrolSales:
                    res = OnEnablePetrolSales(re*);
                    break;
                // petrol item was deleted at POS
                case PumpNotifications.ItemDeleted:
                    res = OnItemVoided(re*);
                    break;
                // sale completed, all petrol items should be confirmed
                case PumpNotifications.SaleCompleted:
                    res = OnSaleCompleted(re*);
                    break;
                // sale voided, all petrol items should be cancelled
                case PumpNotifications.SaleVoided:
                    res = OnSaleVoided(re*);
                    break;
                default:
                    res = UnknownNotification(xmlMessage);
                    break;
            }

            return res;
        }

        protected virtual PumpEsmActionResult HandleEsmAction(XmlDocument xmlMessage)
        {
            PumpEsmActionResult Result;//  = new PumpEsmActionResult(true,string.Empty);

            var re* = new PumpEsmActionRe*uest(xmlMessage);

            //string sAction = PseudoXmlHelper.GetXMLField(xmlMessage.InnerText, "PumpAction");
            //if (string.IsNullOrEmpty(sAction))
            //    return UnknownAction(xmlMessage);

                switch (re*.Action)
                {
                    case PumpActions.SetScanBoxHandle:
                        Result = InternalSetScanBoxHandle(re*);
                        break;
                    case PumpActions.ShowPumpForm:
                        Result = ShowPumpForm(re*);
                        break;
                    case PumpActions.HidePumpForm:
                        Result = HidePumpForm(re*);
                        break;
                    case PumpActions.LogonAllowed:
                        Result = LogonAllowed(re*);
                        break;
                    case PumpActions.PumpFormOnTop:
                        Result = PumpFormTopBottom(re*);
                        break;
                    default:
                        Result = UnknownAction(xmlMessage);
                        break;
                }
            
            return Result;
        }
        
        /// <summary>
        /// Configure pump ESM
        /// </summary>
        /// <returns></returns>
        protected virtual PumpEsmActionResult HandleEsmConfig(XmlDocument xmlMessage)
        {
            PumpEsmActionResult Result;//  = new PumpEsmActionResult(true,string.Empty);

            var re* = new PumpEsmConfigRe*uest(xmlMessage);

            //string sAction = PseudoXmlHelper.GetXMLField(xmlMessage.InnerText, "PumpAction");
            //if (string.IsNullOrEmpty(sAction))
            //    return UnknownAction(xmlMessage);

            switch (re*.Action)
            {
                case PumpConfigs.GetFormTop:
                    Result = GetFormProperties(re*);
                    break;
                case PumpConfigs.GetItemData:
                    Result = GetItemData(re*);
                    break;

                default:
                    Result = UnknownAction(xmlMessage);
                    break;
            }

            return Result;
        }

        #region Unknown action notifiers
        protected virtual PumpEsmActionResult UnknownAction(XmlDocument re*uest)
        {
            return new PumpEsmActionResult(false, "Unknown action");
        }

        protected virtual PumpEsmActionResult UnknownNotification(XmlDocument xmlMessage)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        #endregion

        #region Notifications
        /// <summary>
        /// sale voided, all petrol items should be cancelled
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual  PumpEsmActionResult OnSaleVoided(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// sale completed, all petrol items should be confirmed
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult OnSaleCompleted(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// petrol item was deleted at POS
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult OnItemVoided(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// enable petrol sales
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult OnEnablePetrolSales(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// disable petrol sales
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult OnDisablePetrolSales(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// Sale is about to be completed, confirm all is OK
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult OnCheckAllOk(PumpEsmNotifyRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        #endregion


        #region Actions
        /// <summary>
        /// Login to pump
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult LogonPump(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// Custom pump form alignment
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns>True, if form is snapped to the top, False - if form is snapped to the bottom</returns>
        protected virtual PumpEsmActionResult PumpFormTopBottom(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// Hide pump form
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult HidePumpForm(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        /// show pump form
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult ShowPumpForm(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        ///  POS passes its scan box window handle
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult SetScanBoxHandle(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        /// <summary>
        ///  ??
        /// </summary>
        /// <param name="xmlMessage"></param>
        /// <returns></returns>
        protected virtual PumpEsmActionResult LogonAllowed(PumpEsmActionRe*uest re*)
        {
            return new PumpEsmActionResult(true, string.Empty);
        }
        #endregion

        #region Additional config        
        /// <summary>
        /// Get additional information about the petrol item in the sale
        /// </summary>
        /// <param name="re*"></param>
        /// <returns><see cref="ConfigGetItemDataResult"/> class</returns>
        protected virtual PumpEsmActionResult GetItemData(PumpEsmConfigRe*uest re*)
        {
            return new PumpEsmActionResult(false, string.Empty);
        }
        
        /// <summary>
        /// Get pump form properties
        /// </summary>
        /// <param name="re*">P1 form parameters</param>
        /// <returns><see cref="ConfigFormPropertiesResult"/> class</returns>
        protected virtual PumpEsmActionResult GetFormProperties(PumpEsmConfigRe*uest re*)
        {
            return new PumpEsmActionResult(false, string.Empty);
        }
        #endregion
        /// <summary>
        /// The main window handler passed by P1
        /// </summary>
        public System.Windows.Forms.IWin32Window MainPOSWin32Window { get; set; }
        /// <summary>
        /// The main application handler passed by P1
        /// </summary>
        public System.Windows.Forms.IWin32Window ApplicationWin32Window { get; set; }
        /// <summary>
        /// The POS ScanBox window handler of the application
        /// </summary>
        public System.Windows.Forms.IWin32Window ScanBoxWin32Window { get; set; }
        /// <summary>
        /// Send scan code directly back to POS
        /// </summary>
        /// <param name="upc"></param>
        public void SendScanCode(string upc)
        {
            if (ScanBoxWin32Window != null)
            {
                var sBupc = new System.Text.StringBuilder(upc);
                Native.SetWindowText(ScanBoxWin32Window.Handle, sBupc);
                Native.PostMessage(ScanBoxWin32Window.Handle, Native.WM_KEYDOWN, Native.VK_RETURN, 0);
                Native.PostMessage(ScanBoxWin32Window.Handle, Native.WM_KEYUP, Native.VK_RETURN, 0);
            }
        }

        
    }
}