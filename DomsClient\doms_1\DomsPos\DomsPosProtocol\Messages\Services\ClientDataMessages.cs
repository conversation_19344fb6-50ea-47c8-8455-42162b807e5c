﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Clear Installation Data
    public class ClearInstallDataRequest : JplRequest
    {
        public class ClearInstallDataRequestData
        {
            [JsonPropertyName("ExtendedInstallMsgCode")]
            public string ExtendedInstallMsgCode { get; set; }

            [JsonPropertyName("FcDeviceId")]
            public string FcDeviceId { get; set; }
        }
    }

    public class ClearInstallDataResponse : JplResponse
    {
        public class ClearInstallDataResponseData
        {
            // Empty response
        }
    }

    // Client Data Messages
    public class ClientDataRequest : JplRequest
    {
        public class ClientDataRequestData
        {
            [JsonPropertyName("PosId")]
            public string PosId { get; set; }

            [JsonPropertyName("ClientDataOffset")]
            public int ClientDataOffset { get; set; }

            [JsonPropertyName("ClientDataLen")]
            public int ClientDataLen { get; set; }
        }
    }

    public class ClientDataResponse : JplResponse
    {
        public class ClientDataResponseData
        {
            [JsonPropertyName("ClientData")]
            public List<string> ClientData { get; set; }
        }
    }

    public class StoreClientDataRequest : JplRequest
    {
        public class StoreClientDataRequestData
        {
            [JsonPropertyName("PosId")]
            public string PosId { get; set; }

            [JsonPropertyName("ClientDataOffset")]
            public int ClientDataOffset { get; set; }

            [JsonPropertyName("ClientData")]
            public List<string> ClientData { get; set; }
        }
    }

    public class StoreClientDataResponse : JplResponse
    {
        public class StoreClientDataResponseData
        {
            // Empty response
        }
    }
}
