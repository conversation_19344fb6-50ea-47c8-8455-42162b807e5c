﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.ForecourtController
{
    // Date and Time Messages
    public class FcDateTimeRequest : JplRequest
    {
        public class FcDateTimeData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }
        }
    }

    public class FcDateTimeResponse : JplResponse
    {
        public class FcDateTimeResponseData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }

            [JsonPropertyName("LastDateAndTimeSetting")]
            public string LastDateAndTimeSetting { get; set; }
        }
    }

    // Installation Status Messages
    public class FcInstallStatusRequest : JplRequest
    {
        public class FcInstallStatusData
        {
            // Empty for basic requests
        }
    }

    public class FcInstallStatusResponse : JplResponse
    {
        public class FcInstallStatusResponseData
        {
            [JsonPropertyName("InstalledFcDeviceGroups")]
            public List<InstalledDeviceGroup> InstalledFcDeviceGroups { get; set; }

            [JsonPropertyName("InstalledFcEquipmentTypes")]
            public List<InstalledEquipmentType> InstalledFcEquipmentTypes { get; set; }
        }

        public class InstalledDeviceGroup
        {
            [JsonPropertyName("InstallMsgCode")]
            public string InstallMsgCode { get; set; }

            [JsonPropertyName("ExtendedInstallMsgCode")]
            public string ExtendedInstallMsgCode { get; set; }

            [JsonPropertyName("FcDeviceId")]
            public List<string> FcDeviceId { get; set; }
        }

        public class InstalledEquipmentType
        {
            [JsonPropertyName("FcEquipmentType")]
            public EnumValue<string> FcEquipmentType { get; set; }

            [JsonPropertyName("FcEquipmentId")]
            public List<string> FcEquipmentId { get; set; }
        }
    }

    // Price Set Messages
    public class FcPriceSetStatusRequest : JplRequest
    {
        public class FcPriceSetStatusData
        {
            // Empty for basic requests
        }
    }

    public class FcPriceSetStatusResponse : JplResponse
    {
        public class FcPriceSetStatusResponseData
        {
            [JsonPropertyName("FcPriceSetId")]
            public string FcPriceSetId { get; set; }

            [JsonPropertyName("FcPriceSetDateAndTime")]
            public string FcPriceSetDateAndTime { get; set; }

            [JsonPropertyName("FcPendingPriceSet")]
            public List<PendingPriceSet> FcPendingPriceSet { get; set; }
        }

        public class PendingPriceSet
        {
            [JsonPropertyName("FcPriceSetId")]
            public string FcPriceSetId { get; set; }

            [JsonPropertyName("PriceSetActivationDateAndTime")]
            public string PriceSetActivationDateAndTime { get; set; }
        }
    }
}
