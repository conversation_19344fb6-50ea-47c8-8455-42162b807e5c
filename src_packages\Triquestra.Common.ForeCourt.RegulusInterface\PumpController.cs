﻿using System;
using System.Collections.Generic;
using System.Linq;
using Triquestra.Common.PumpEsm.Comms;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.PumpEsm.Messaging.Event;
using Triquestra.Common.PumpEsm.Messaging.Request;
using Triquestra.Common.PumpEsm.Messaging.Response;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public class PumpController:ForecourtConnection, IPumpController
    {
        private int _SeqNo = 0;
        private readonly List<IDispenserSettings> _dispensers;
        private readonly List<int> _dispenserList;
        private readonly int _workstationId;

        private int SeqNo
        {
            get
            {
                if (this._SeqNo == int.MaxValue)
                    this._SeqNo = 0;
                return this._SeqNo++;
            }
        }

        /// <summary>
        /// Skip all initialisation
        /// !!!! Please be aware that some controller calls implicitly relying on the initial configuration will not work if a bootstrap has been skipped
        /// <remarks>
        /// A usual workflow for the class is to issue lots of async commands during startup. This flag allows us to skip it and/or initiate it manually using
        /// SendCommand(ForecourtCommandMessageTypes.CONTROLLER_CONFIG);
        /// </remarks>
        /// </summary>
        public bool SkipBootstrap { get; set; }

        public PumpController(string tcpHost, int tcpPort, string udpHost, int udpPort, int workstationId)
            : base(tcpHost, tcpPort, udpHost, udpPort, workstationId)
        {
            SkipBootstrap = false;
            _workstationId = workstationId;
            _dispensers = new List<IDispenserSettings>();
            _dispenserList = new List<int>();
            
        }
        [Obsolete("The pooling is implemented internally. No need to call this method.", false)]
        public static PumpController Attach(string tcpHost, int tcpPort, string udpHost, int udpPort, int workstationId)
        {
            var res = new PumpController(tcpHost, tcpPort, udpHost, udpPort, workstationId);
            res.Connect();
            return res;
        }

        public override void OnConnected(EventArgs e)
        {
            base.OnConnected(e);
            if (!SkipBootstrap)
            {
                System.Threading.Thread.Sleep(500);
                var initThread = new System.Threading.Thread(this.Initialise);
                initThread.Start();
                SkipBootstrap = true;
            }
        }

        public override void OnMessageReceived(List<IForecourtMessage> messages, EventArgs e)
        {
            base.OnMessageReceived(messages, e);
            if (messages == null || !messages.Any())
            {
                Log("OnMessageReceived sender is null");
                return;
            }

            foreach (var sender in messages)
            {
                try
                {
                    switch (sender.MessageClass)
                    {
                        case ForecourtMessageClasses.COMMAND_RESPONSE:

                            switch (((CommandResponse)sender).MessageType)
                            {
                                case ForecourtCommandMessageTypes.SYSTEM_TIME:

                                    var regulusTime = (sender as ResponseTimeCommand).Timestamp;
                                    //CheckRegulusTime(regulusTime);
                                    Log("regulusTime is " + regulusTime);
                                    SendCommand(ForecourtCommandMessageTypes.CONTROLLER_CONFIG);
                                    break;
                                case ForecourtCommandMessageTypes.CONTROLLER_CONFIG:
                                    Version = (sender as ResponseControllerConfig).Version;
                                    Log("Version is " + Version);
                                    SendCommand(ForecourtCommandMessageTypes.DISPENSER_LIST);
                                    break;
                                case ForecourtCommandMessageTypes.DISPENSER_LIST:
                                    Log("ResponseDispenserList");

                                    OnDispenserListReceived(sender as ResponseDispenserList);

                                    _dispenserList.Clear();
                                    //_dispenserList.AddRange((sender as ResponseDispenserList).DispenserList);
                                    //from Baran:                                    
                                    _dispenserList.AddRange((sender as ResponseDispenserList).DispenserList.OrderBy(d => d));
                                    if (_dispenserList.Count == 0) return;

#if false 
                                    //b23044: query all dispensers instead of a loop
                                    SendCommand(_workstationId, ForecourtCommandMessageTypes.DISPENSER_CONFIG);
#else
                                    // Baran recommended to leave it as a loop with increased delay
                                    foreach (var i in _dispenserList)
                                    {
                                        //!!!System.Threading.Thread.Sleep(100);
                                        SendCommand(_workstationId, i, ForecourtCommandMessageTypes.DISPENSER_CONFIG);
                                    }
#endif
                                    break;

                                case ForecourtCommandMessageTypes.DISPENSER_CONFIG:
                                    var disp = sender as ResponseDispenserSettings;

                                    Log("ResponseDispenserSettings");
#if false
                                    //b23044: query all dispensers instead of a loop
                                    disp.DispenserConfig.ForEach(s=>this.UpdateDispenser(s));
                                    SendCommand(_workstationId, ForecourtCommandMessageTypes.DISPENSER_DATA);
#else
                                    // Baran recommended to leave it as a loop with increased delay
                                    UpdateDispenser(disp.DispenserConfig[0]);
                                    var di = disp.DispenserConfig[0].DispenserId;
                                    if (_dispenserList.Contains(di))
                                    {
                                        //!!!System.Threading.Thread.Sleep(100);
                                        SendCommand(_workstationId, di, ForecourtCommandMessageTypes.DISPENSER_DATA);
                                    }
#endif
                                    break;
                                case ForecourtCommandMessageTypes.DELIVERY_DATA:

                                    OnDeliveryDataReceived(sender as DeliveryDataResponse);
                                    break;

                                case ForecourtCommandMessageTypes.DISPENSER_DATA:

                                    OnDispenserDataReceived(sender as DispenserDataResponse);
                                    break;

                                case ForecourtCommandMessageTypes.DELIVERY_LOCK:

                                    OnDeliveryLockReceived(sender as DeliveryLockResponse);
                                    break;
                                case ForecourtCommandMessageTypes.RESERVE:

                                    OnReserveDispenserReceived(sender as ReserveResponse);
                                    break;
                            }
                            break;

                        case ForecourtMessageClasses.CONFIG_RESPONSE:
                            if (sender is ResponseProfilesConfig)
                            {
                                OnProfilesReceived(sender as ResponseProfilesConfig);
                            }
                            break;

                        case ForecourtMessageClasses.EVENT:

                            switch (((ForecourtEventMessage)sender).EventType)
                            {
                                case EventMessageTypes.NOZZLE_STATE_CHANGE:

                                    OnNozzleStateChanged(sender as EventNozzleStateChangeMessage);
                                    break;
                                case EventMessageTypes.DISPENSER_STATE_CHANGE:

                                    OnDispenserStateChanged(sender as EventDispenserStateChangeMessage);
                                    break;
                                case EventMessageTypes.DISPENSER_MODE_CHANGE:
                                    OnDispenserModeChanged(sender as EventDispenserModeChangedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_PROGRESS:

                                    OnDeliveryInProgress(sender as EventDeliveryProgressMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_STATE_CHANGE:

                                    OnDeliveryStateChanged(sender as EventDeliveryStateChangeMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_LOCKED:

                                    OnDeliveryLocked(sender as EventDeliveryLockedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_COMPLETE:
                                    OnDeliveryCompleted(sender as EventDeliveryCompletedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_UNLOCKED:
                                    OnDeliveryUnlocked(sender as EventDeliveryUnlockedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_STARTED:
                                    OnDeliveryStarted(sender as EventDeliveryStartedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_DELETED:

                                    OnDeliveryDeleted(sender as EventDeliveryDeletedMessage);
                                    break;
                                case EventMessageTypes.DELIVERY_CLEAR:

                                    OnDeliveryCleared(sender as EventDeliveryClearedMessage);
                                    break;
                                case EventMessageTypes.ELECTRONIC_TOTALS:
                                    OnElectronicTotalsReceived(sender as EventPumpMeterMessage);
                                    break;
                                case EventMessageTypes.HEARTBEAT:
                                    OnHeartbeat(sender as EventHeartbeatMessage);
                                    break;
                            }

                            break;

                        default:
                            break;
                    }
                }
                catch(Exception ex)
                {
                    LogException("OnMessageReceived",ex);
                }
            }
        }



        #region Protected Methods       
        protected virtual void OnDeliveryStateChanged(EventDeliveryStateChangeMessage sender)
        {
            var handler = DeliveryStateChanged;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDispenserDataReceived(DispenserDataResponse sender)
        {
            var handler = DispenserDataReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnReserveDispenserReceived(ReserveResponse sender)
        {
            var handler = ReserveReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }


        protected virtual void OnDeliveryLockReceived(DeliveryLockResponse sender)
        {
            var handler = DeliveryLockReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDispensersUpdated()
        {
            var handler = DispensersUpdated;
            if (handler != null) handler(this, EventArgs.Empty);
        }

        protected virtual void OnDispenserListReceived(ResponseDispenserList sender)
        {
            var handler = DispenserListReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryStarted(EventDeliveryStartedMessage sender)
        {
            var handler = DeliveryStarted;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryCompleted(EventDeliveryCompletedMessage sender)
        {
            var handler = DeliveryCompleted;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryDataReceived(DeliveryDataResponse sender)
        {
            var handler = DeliveryDataReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryDeleted(EventDeliveryDeletedMessage sender)
        {
            var handler = DeliveryDeleted;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryCleared(EventDeliveryClearedMessage sender)
        {
            var handler = DeliveryCleared;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnElectronicTotalsReceived(EventPumpMeterMessage sender)
        {
            var handler = ElectronicTotalsReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryInProgress(EventDeliveryProgressMessage sender)
        {
            var handler = DeliveryInProgress;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDispenserStateChanged(EventDispenserStateChangeMessage sender)
        {
            var handler = DispenserStateChanged;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDispenserModeChanged(EventDispenserModeChangedMessage sender)
        {
            var handler = DispenserModeChanged;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnNozzleStateChanged(EventNozzleStateChangeMessage sender)
        {
            var handler = NozzleStateChanged;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryLocked(EventDeliveryLockedMessage sender)
        {
            var handler = DeliveryLocked;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnDeliveryUnlocked(EventDeliveryUnlockedMessage sender)
        {
            var handler = DeliveryUnlocked;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnProfilesReceived(ResponseProfilesConfig sender)
        {
            var handler = ProfilesReceived;
            if (handler != null) handler(sender, EventArgs.Empty);
        }

        protected virtual void OnHeartbeat(EventHeartbeatMessage sender)
        {
            var handler = Heartbeat;
            if (handler != null) handler(this, EventArgs.Empty);
        }

        //protected virtual void OnForecourtConnected(EventArgs e)
        //{
        //    var handler = ForecourtConnected;
        //    if (handler != null) handler(this, e);
        //}
        #endregion
        private object _syncRoot = new object();
        private void UpdateDispenser(IDispenserSettings dispenser)
        {
            var dispenserId = dispenser.DispenserId;
            int cnt;
            if (!_dispenserList.Contains(dispenserId))
                return;

            lock (_syncRoot)
            {
                var disp = _dispensers.Find(d => d.DispenserId == dispenserId);
                if (disp == null)
                {
                    _dispensers.Add(dispenser);
                }
                else
                {
                    _dispensers.Remove(disp);
                    _dispensers.Add(dispenser);
                }
                cnt = _dispensers.Count;
            }
            //Log(@"c:\temp\comm\log.txt",string.Format("taken {0} of {1}",_dispensers.Count ,_dispenserList.Count));
            if (cnt == _dispenserList.Count)
            {
                Log("OnDispensersUpdated");
                // we have all pumps, fire the event
                OnDispensersUpdated();
            }
        }

        private void Initialise()
        {
            Log("Initialise()");
            var timecommand = new ForecourtCommandRequest(1, 1, 0, ForecourtCommandMessageTypes.SYSTEM_TIME);
            SendData(timecommand.ToString());
        }

        public IEnumerable<IDispenserSettings> Dispensers
        {
            get
            {
                IEnumerable<IDispenserSettings> res;
                lock (_syncRoot)
                {
                    res = _dispensers.ToList();
                }
                return res;
            }
        }

        public int WorkstationId { get { return _workstationId; } }

        public string Version { get; private set; }

        #region Commands

        public void StartPumpController()
        {
            SendSystemRequest(ForecourtCommandMessageTypes.APPLICATION_START, "PumpControl");
        }

        public void StopPumpController()
        {
            SendSystemRequest(ForecourtCommandMessageTypes.APPLICATION_STOP, "PumpControl");
        }

        public void Reserve(int sourceId, int targetId, decimal limit, IForecourtBlend blend)
        {
            var command = new ReserveRequest(this.SeqNo, sourceId, targetId, limit, blend);
            SendCommand(command);
        }

        public void ClearDispenserException(int targetId)
        {
            throw new NotImplementedException();
        }

        public void StopDriver(int targetId)
        {
            SendCommand(_workstationId, targetId, ForecourtCommandMessageTypes.DRIVER_STOP);
        }

        public void StartDriver(int targetId)
        {
            SendCommand(_workstationId, targetId, ForecourtCommandMessageTypes.DRIVER_START);
        }

        public void ShutdownDriver(int targetId)
        {
            SendCommand(_workstationId, targetId, ForecourtCommandMessageTypes.DRIVER_SHUTDOWN);
        }

        public void Authorize(int targetId)
        {
            var command = new AuthorizeRequest(this.SeqNo, _workstationId, targetId, AuthModes.POSTPAY, LimitTypes.NONE, 0, null);
            SendCommand(command);
        }

        public void Authorize(int targetId, decimal limit, IForecourtBlend blend)
        {
            var command = new AuthorizeRequest(this.SeqNo, _workstationId, targetId, AuthModes.PREPAY, LimitTypes.VALUE, limit, blend);
            SendCommand(command);
        }

        public void Abort(int targetId)
        {
            SendCommand(_workstationId,targetId,ForecourtCommandMessageTypes.ABORT);
        }

        public void Abort(int sourceId, int targetId)
        {
            SendCommand(sourceId, targetId, ForecourtCommandMessageTypes.ABORT);
        }

        public void LockDelivery(int targetId, int deliveryId)
        {
            SendCommand(new LockDeliveryRequest(this.SeqNo, _workstationId,targetId,deliveryId));
        }

        public void UnlockDelivery(int targetId, int deliveryId)
        {
            SendCommand(new UnlockDeliveryRequest(this.SeqNo, _workstationId, targetId, deliveryId));
        }

        public void UnlockDelivery(int sourceId, int targetId, int deliveryId)
        {
            SendCommand(new UnlockDeliveryRequest(this.SeqNo, sourceId, targetId, deliveryId));
        }

        public void ClearDelivery(int targetId, int deliveryId)
        {
            SendCommand(new ClearDeliveryRequest(this.SeqNo, _workstationId, targetId, deliveryId));
        }

        public void TerminateController()
        {
            SendCommand(ForecourtCommandMessageTypes.CONTROLLER_TERMINATE);
        }

        public void SuspendAll()
        {
            SendCommand(_workstationId, ForecourtCommandMessageTypes.DISPENSER_SUSPEND);
        }

        public void Suspend(int targetId)
        {
            SendCommand(_workstationId, targetId, ForecourtCommandMessageTypes.DISPENSER_SUSPEND);
        }

        public void ResumeAll()
        {
            SendCommand(ForecourtCommandMessageTypes.DISPENSER_RESUME);
        }

        public void Resume(int targetId)
        {
            SendCommand(_workstationId, targetId, ForecourtCommandMessageTypes.DISPENSER_RESUME);
        }

        public void ResetDispenser(int targetId)
        {
            throw new NotImplementedException();
        }

        public void ResetAllDispensers()
        {
            SendCommand(ForecourtCommandMessageTypes.DISPENSER_RESET);
        }

        public void GetElectronicTotals(int targetId)
        {
            SendCommand(_workstationId,targetId, ForecourtCommandMessageTypes.GET_ELEC_TOTS);
        }

        public void GetProfiles()
        {
            SendCommand(new ForecourtConfigRequest(ForecourtConfigurationTypes.GET_AUTHMODE_PROFILES));
        }

        public void SetProfile(int profileIndex)
        {
            SendCommand(new ForecourtSetProfileRequest(profileIndex));
        }
        [Obsolete("Do not use this method. Use SystemManager.ChangeFuelPrice instead.", true)]
        public void ChangeFuelPrice(int blendId, int priceId, decimal amount)
        {
            SendCommand(new ForecourtPriceChangeRequest(blendId, priceId, amount));
        }

        public void GetDeliveryData(int targetId, int deliveryId)
        {
            var command = new DeliveryDataRequest(this.SeqNo, _workstationId, targetId, deliveryId);
            SendCommand(command);
        }

        public void GetDispenserData(int dispenserId)
        {
            SendCommand(_workstationId, dispenserId, ForecourtCommandMessageTypes.DISPENSER_DATA);
        }

        #endregion

        #region Responses

        public event EventHandler DeliveryDataReceived;

        public event EventHandler DeliveryLockReceived;

        public event EventHandler DispenserDataReceived;

        public event EventHandler ProfilesReceived;

        public event EventHandler DispenserListReceived;
        /// <summary>
        /// Signalled when a Reserve is executed
        /// </summary>
        public event EventHandler ReserveReceived;

        #endregion


        #region Events
        public event EventHandler Heartbeat;

        public event EventHandler ElectronicTotalsReceived;

        public event EventHandler DispenserClearRequired;

        public event EventHandler DeliveryStarted;        

        public event EventHandler DeliveryInProgress;

        public event EventHandler DeliveryCompleted;

        public event EventHandler DeliveryStateChanged;

        public event EventHandler DeliveryTimeout;

        public event EventHandler DeliveryCleared;

        public event EventHandler DeliveryLocked;

        public event EventHandler DeliveryUnlocked;

        public event EventHandler DeliveryDeleted;

        public event EventHandler DeliveryStacked;

        public event EventHandler DispenserModeChanged;

        public event EventHandler DispenserStateChanged;
        
        public event EventHandler DispenserConfigChanged;

        public event EventHandler NozzleStateChanged;
       
        public event EventHandler DispensersUpdated;

        #endregion
    }
}
