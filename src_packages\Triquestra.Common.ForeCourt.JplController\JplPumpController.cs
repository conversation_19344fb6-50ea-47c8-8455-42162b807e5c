using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Threading.Tasks;
using NLog;
using Triquestra.Common.PumpEsm.Comms;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    /// <summary>
    /// JPL-based implementation of IPumpController using JplClient for communication
    /// </summary>
    public class JplPumpController : IPumpController
    {
        #region Private Fields

        private static readonly Logger _log = LogManager.GetCurrentClassLogger();
        private readonly SimpleJplClient _jplClient;
        private readonly string _hostname;
        private readonly int _port;
        private readonly int _workstationId;
        private readonly List<IDispenserSettings> _dispensers;
        private bool _isConnected;

        #endregion

        #region Constructor

        public JplPumpController(string hostname, int port, int workstationId)
        {
            _hostname = hostname;
            _port = port;
            _workstationId = workstationId;
            _dispensers = new List<IDispenserSettings>();

            // Create simple JPL client
            _jplClient = new SimpleJplClient();

            // Subscribe to JPL client events
            _jplClient.ConnectionStateChanged += OnConnectionStateChanged;
            _jplClient.UnsolicitedMessageReceived += OnUnsolicitedMessageReceived;
        }

        #endregion

        #region IPumpController Properties

        public IEnumerable<IDispenserSettings> Dispensers => _dispensers;

        public int WorkstationId => _workstationId;

        public string Version => "1.0.0-JPL";

        #endregion

        #region IForecourtConnection Implementation

        public bool IsConnected => _isConnected;

        public void Connect()
        {
            try
            {
                _log.Info($"Connecting to JPL controller at {_hostname}:{_port}");

                // Connect asynchronously but wait for completion
                Task.Run(async () =>
                {
                    await _jplClient.ConnectAsync(_hostname, _port);
                    await InitializeDispensers();
                }).Wait();

                _log.Info("JPL controller connected successfully");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Failed to connect to JPL controller");
                throw;
            }
        }

        public void Disconnect()
        {
            try
            {
                _log.Info("Disconnecting from JPL controller");
                _jplClient.Disconnect();
                _isConnected = false;
                _log.Info("JPL controller disconnected");
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error during JPL controller disconnect");
            }
        }

        #region IForecourtConnection Additional Methods

        public Socket TcpSocket => null; // JPL doesn't expose raw socket

        public void SendCommand(ForecourtCommandMessageTypes commandType)
        {
            _log.Info($"SendCommand: {commandType}");
            // Convert to JPL command and send
        }

        public void SendCommand(int sourceId, ForecourtCommandMessageTypes commandType)
        {
            _log.Info($"SendCommand: {sourceId}, {commandType}");
            // Convert to JPL command and send
        }

        public void SendCommand(int sourceId, int targetId, ForecourtCommandMessageTypes commandType)
        {
            _log.Info($"SendCommand: {sourceId}, {targetId}, {commandType}");
            // Convert to JPL command and send
        }

        public void SendCommand(IForecourtMessage command)
        {
            _log.Info($"SendCommand: {command}");
            // Convert to JPL command and send
        }

        public void SendSystemRequest(ForecourtCommandMessageTypes commandType, string name)
        {
            _log.Info($"SendSystemRequest: {commandType}, {name}");
            // Convert to JPL system request and send
        }

        public event EventHandler MessageReceived;
        public event EventHandler Connected;

        #endregion

        #region IPumpController Methods - Core Operations

        public void Reserve(int sourceId, int targetId, decimal limit, IForecourtBlend blend)
        {
            _log.Info($"Reserving dispenser {targetId} for workstation {sourceId}, limit: {limit}, blend: {blend?.BlendName}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_reserve",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            workstation_id = sourceId.ToString(),
                            limit_amount = limit.ToString("F2"),
                            grade = blend?.BlendName ?? "Regular"
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleReserveResponse(sourceId, targetId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error reserving dispenser {targetId}");
                }
            });
        }

        public void Authorize(int targetId)
        {
            _log.Info($"Authorizing dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_authorize",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            limit_type = "NONE"
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleAuthorizeResponse(targetId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error authorizing dispenser {targetId}");
                }
            });
        }

        public void Authorize(int targetId, decimal limit, IForecourtBlend blend)
        {
            _log.Info($"Authorizing dispenser {targetId} with limit {limit} for blend {blend?.BlendName}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_authorize",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            limit_type = "VALUE",
                            limit_amount = limit.ToString("F2"),
                            grade = blend?.BlendName ?? "Regular"
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleAuthorizeResponse(targetId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error authorizing dispenser {targetId}");
                }
            });
        }

        public void Abort(int targetId)
        {
            _log.Info($"Aborting dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_abort",
                        Data = new
                        {
                            fp_id = targetId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleAbortResponse(targetId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error aborting dispenser {targetId}");
                }
            });
        }

        public void Abort(int sourceId, int targetId)
        {
            _log.Info($"Aborting dispenser {targetId} from workstation {sourceId}");
            Abort(targetId); // For now, treat the same as single-parameter abort
        }

        public void LockDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Locking delivery {deliveryId} on dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_lock_delivery",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            delivery_id = deliveryId.ToString(),
                            workstation_id = _workstationId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleLockDeliveryResponse(targetId, deliveryId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error locking delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void UnlockDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Unlocking delivery {deliveryId} on dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_unlock_delivery",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            delivery_id = deliveryId.ToString(),
                            workstation_id = _workstationId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleUnlockDeliveryResponse(targetId, deliveryId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error unlocking delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void UnlockDelivery(int sourceId, int targetId, int deliveryId)
        {
            _log.Info($"Unlocking delivery {deliveryId} on dispenser {targetId} from workstation {sourceId}");
            UnlockDelivery(targetId, deliveryId); // For now, treat the same as two-parameter version
        }

        public void ClearDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Clearing delivery {deliveryId} on dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_clear",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            delivery_id = deliveryId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleClearDeliveryResponse(targetId, deliveryId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error clearing delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        #endregion

        #region IPumpController Methods - Additional Operations

        public void SuspendAll()
        {
            _log.Info("Suspending all dispensers");
            // Implementation for suspending all dispensers
        }

        public void Suspend(int targetId)
        {
            _log.Info($"Suspending dispenser {targetId}");
            // Implementation for suspending specific dispenser
        }

        public void ResumeAll()
        {
            _log.Info("Resuming all dispensers");
            // Implementation for resuming all dispensers
        }

        public void Resume(int targetId)
        {
            _log.Info($"Resuming dispenser {targetId}");
            // Implementation for resuming specific dispenser
        }

        public void ResetDispenser(int targetId)
        {
            _log.Info($"Resetting dispenser {targetId}");
            // Implementation for resetting dispenser
        }

        public void ClearDispenserException(int targetId)
        {
            _log.Info($"Clearing exception on dispenser {targetId}");
            // Implementation for clearing dispenser exception
        }

        public void StopDriver(int targetId)
        {
            _log.Info($"Stopping driver for dispenser {targetId}");
            // Implementation for stopping driver
        }

        public void StartDriver(int targetId)
        {
            _log.Info($"Starting driver for dispenser {targetId}");
            // Implementation for starting driver
        }

        public void ShutdownDriver(int targetId)
        {
            _log.Info($"Shutting down driver for dispenser {targetId}");
            // Implementation for shutting down driver
        }

        public void TerminateController()
        {
            _log.Info("Terminating controller");
            Disconnect();
        }

        public void ResetAllDispensers()
        {
            _log.Info("Resetting all dispensers");
            // Implementation for resetting all dispensers
        }

        public void GetElectronicTotals(int targetId)
        {
            _log.Info($"Getting electronic totals for dispenser {targetId}");
            // Implementation for getting electronic totals
        }

        public void GetProfiles()
        {
            _log.Info("Getting profiles");
            // Implementation for getting profiles
        }

        public void SetProfile(int profileIndex)
        {
            _log.Info($"Setting profile {profileIndex}");
            // Implementation for setting profile
        }

        public void ChangeFuelPrice(int blendId, int priceId, decimal amount)
        {
            _log.Info($"Changing fuel price for blend {blendId}, price {priceId} to {amount}");
            // Implementation for changing fuel price
        }

        public void GetDeliveryData(int targetId, int deliveryId)
        {
            _log.Info($"Getting delivery data for dispenser {targetId}, delivery {deliveryId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_delivery_data",
                        Data = new
                        {
                            fp_id = targetId.ToString(),
                            delivery_id = deliveryId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleDeliveryDataResponse(targetId, deliveryId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error getting delivery data for dispenser {targetId}, delivery {deliveryId}");
                }
            });
        }

        public void GetDispenserData(int targetId)
        {
            _log.Info($"Getting dispenser data for dispenser {targetId}");

            Task.Run(async () =>
            {
                try
                {
                    var request = new JplMessage
                    {
                        Name = "fp_status",
                        Data = new
                        {
                            fp_id = targetId.ToString()
                        }
                    };

                    var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                    HandleDispenserDataResponse(targetId, response);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error getting dispenser data for dispenser {targetId}");
                }
            });
        }

        #endregion

        #region Private Helper Methods

        private async Task InitializeDispensers()
        {
            try
            {
                _log.Info("Initializing dispensers from JPL controller");

                var request = new JplMessage
                {
                    Name = "fp_list",
                    Data = new { }
                };

                var response = await _jplClient.SendMessageAsync<JplMessage>(request);
                HandleDispenserListResponse(response);
            }
            catch (Exception ex)
            {
                _log.Error(ex, "Error initializing dispensers");
            }
        }

        private void OnConnectionStateChanged(object sender, bool isConnected)
        {
            _isConnected = isConnected;
            _log.Info($"JPL connection state changed: {isConnected}");
        }

        private void OnUnsolicitedMessageReceived(object sender, JplMessage message)
        {
            _log.Debug($"Received unsolicited message: {message.Name}");

            try
            {
                // Handle different types of unsolicited messages
                switch (message.Name?.ToLower())
                {
                    case "fp_status_update":
                        HandleDispenserStatusUpdate(message);
                        break;
                    case "fp_delivery_started":
                        HandleDeliveryStartedMessage(message);
                        break;
                    case "fp_delivery_progress":
                        HandleDeliveryProgressMessage(message);
                        break;
                    case "fp_delivery_complete":
                        HandleDeliveryCompleteMessage(message);
                        break;
                    case "fp_nozzle_state_change":
                        HandleNozzleStateChangeMessage(message);
                        break;
                    case "heartbeat":
                        HandleHeartbeatMessage(message);
                        break;
                    default:
                        _log.Debug($"Unhandled unsolicited message: {message.Name}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error handling unsolicited message: {message.Name}");
            }
        }

        #endregion

        #region Response Handlers

        private void HandleReserveResponse(int sourceId, int targetId, JplMessage response)
        {
            _log.Debug($"Reserve response for dispenser {targetId}: {response}");
            ReserveReceived?.Invoke(this, EventArgs.Empty);
        }

        private void HandleAuthorizeResponse(int targetId, JplMessage response)
        {
            _log.Debug($"Authorize response for dispenser {targetId}: {response}");
            // Handle authorize response
        }

        private void HandleAbortResponse(int targetId, JplMessage response)
        {
            _log.Debug($"Abort response for dispenser {targetId}: {response}");
            // Handle abort response
        }

        private void HandleLockDeliveryResponse(int targetId, int deliveryId, JplMessage response)
        {
            _log.Debug($"Lock delivery response for dispenser {targetId}, delivery {deliveryId}: {response}");
            DeliveryLockReceived?.Invoke(this, EventArgs.Empty);
        }

        private void HandleUnlockDeliveryResponse(int targetId, int deliveryId, JplMessage response)
        {
            _log.Debug($"Unlock delivery response for dispenser {targetId}, delivery {deliveryId}: {response}");
            // Handle unlock delivery response
        }

        private void HandleClearDeliveryResponse(int targetId, int deliveryId, JplMessage response)
        {
            _log.Debug($"Clear delivery response for dispenser {targetId}, delivery {deliveryId}: {response}");
            // Handle clear delivery response
        }

        private void HandleDeliveryDataResponse(int targetId, int deliveryId, JplMessage response)
        {
            _log.Debug($"Delivery data response for dispenser {targetId}, delivery {deliveryId}: {response}");
            DeliveryDataReceived?.Invoke(this, EventArgs.Empty);
        }

        private void HandleDispenserDataResponse(int targetId, JplMessage response)
        {
            _log.Debug($"Dispenser data response for dispenser {targetId}: {response}");
            DispenserDataReceived?.Invoke(this, EventArgs.Empty);
        }

        private void HandleDispenserListResponse(JplMessage response)
        {
            _log.Debug($"Dispenser list response: {response}");

            // Parse dispenser list and populate _dispensers collection
            // This is a simplified implementation
            _dispensers.Clear();

            DispenserListReceived?.Invoke(this, EventArgs.Empty);
            DispensersUpdated?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Unsolicited Message Handlers

        private void HandleDispenserStatusUpdate(JplMessage message)
        {
            _log.Debug($"Dispenser status update: {message}");
            DispenserStateChanged?.Invoke(this, EventArgs.Empty);
        }

        private void HandleDeliveryStartedMessage(JplMessage message)
        {
            _log.Debug($"Delivery started: {message}");
            DeliveryStarted?.Invoke(this, EventArgs.Empty);
        }

        private void HandleDeliveryProgressMessage(JplMessage message)
        {
            _log.Debug($"Delivery progress: {message}");
            DeliveryInProgress?.Invoke(this, EventArgs.Empty);
        }

        private void HandleDeliveryCompleteMessage(JplMessage message)
        {
            _log.Debug($"Delivery complete: {message}");
            DeliveryCompleted?.Invoke(this, EventArgs.Empty);
        }

        private void HandleNozzleStateChangeMessage(JplMessage message)
        {
            _log.Debug($"Nozzle state change: {message}");
            NozzleStateChanged?.Invoke(this, EventArgs.Empty);
        }

        private void HandleHeartbeatMessage(JplMessage message)
        {
            _log.Debug($"Heartbeat received: {message}");
            Heartbeat?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        #region Events

        public event EventHandler Heartbeat;
        public event EventHandler DeliveryStarted;
        public event EventHandler DeliveryInProgress;
        public event EventHandler DeliveryCompleted;
        public event EventHandler DeliveryStateChanged;
        public event EventHandler DeliveryTimeout;
        public event EventHandler DeliveryCleared;
        public event EventHandler DeliveryLocked;
        public event EventHandler DeliveryUnlocked;
        public event EventHandler DeliveryDeleted;
        public event EventHandler DeliveryStacked;
        public event EventHandler DispenserModeChanged;
        public event EventHandler DispenserStateChanged;
        public event EventHandler DispenserClearRequired;
        public event EventHandler DispenserConfigChanged;
        public event EventHandler NozzleStateChanged;
        public event EventHandler ElectronicTotalsReceived;
        public event EventHandler DispensersUpdated;
        public event EventHandler ProfilesReceived;
        public event EventHandler DispenserDataReceived;
        public event EventHandler DeliveryDataReceived;
        public event EventHandler DeliveryLockReceived;
        public event EventHandler ReserveReceived;
        public event EventHandler DispenserListReceived;

        #endregion
    }
}
