﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Triquestra.Common.Database;
using Triquestra.Common.PumpEsm.DataAccess;
using Triquestra.Common.PumpEsm.Types;

namespace Triquestra.Common.PumpEsm
{
    public partial class PumpInfoPosFooter : Form
    {
        private List<HoseView> _nozzleInfo;
        class PumpView
        {
            private Fuel_Pump _pump;
            internal PumpView(Fuel_Pump p)
            {
                _pump = p;
            }

            public string Description { get { return _pump.ID + " " + _pump.Description; } }
            public Fuel_Pump Table { get { return _pump; } }
        }

        class HoseView
        {
            private Fuel_Hose _hose;
            private Items _item;
            internal HoseView(Fuel_Hose h,Items i)
            {
                _hose = h;
                _item = i;
            }

            public string Description { get { return _item.Description; } }
            public Fuel_Hose Table1 { get { return _hose; } }
            public Items Table2 { get { return _item; } }
        }
        public PumpInfoPosFooter()
        {
            InitializeComponent();
        }
        internal Action<string> SendScanCode { get; set; }
        internal Types.PumpDataProvider DataProvider { get; set; }

        public Types.PetrolSaleItem ActiveDelivery { get; set; }

        internal void AddDeliveryToList()
        {
            //
        }

        internal void ProcessDelivery(int p1, bool p2)
        {
            ActiveDelivery = null;
        }

        internal void ProcessDeliveries(int p1, bool p2)
        {
            ActiveDelivery = null;
        }

        public bool AllowSales { get; set; }

        internal bool ValidateSale(int p)
        {
            return true;
        }

        internal void InitControls()
        {
            var pumps = DataProvider.Dispensers.Where(w=>w.CategoryType=="1").ToList();
            var hoses = DataProvider.Nozzles;
            var items = DataProvider.Items;
            _nozzleInfo = (from p in pumps
                               join h in hoses on p.ID equals h.PumpID
                               join i in items on h.UPC equals i.UPC
                               select new HoseView(h, i)
                                   ).ToList();

            cbxPUmps.DataSource = pumps.Select(s => new PumpView(s)).ToList();
            if (pumps.Count > 0)
                cbxPUmps.SelectedIndex = 0;
        }

        private void cbxPUmps_SelectedIndexChanged(object sender, EventArgs e)
        {
            var pump = (PumpView)cbxPUmps.SelectedItem;
            List<HoseView> hoses;
            if (pump != null)
            {
                hoses = _nozzleInfo.Where(w => w.Table1.PumpID == pump.Table.ID).ToList();
            }
            else
            {
                hoses = new List<HoseView>();
            }
            cbxNozzles.DataSource = hoses;
            if (hoses.Count > 0)
                cbxNozzles.SelectedIndex = 0;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            var pump = (PumpView)cbxPUmps.SelectedItem;
            var hose = (HoseView)cbxNozzles.SelectedItem;
            Messaging.AuthModes authModes;
            if (rbPostPay.Checked)
                authModes = Messaging.AuthModes.POSTPAY;
            else
                authModes = Messaging.AuthModes.PREPAY;

            if (pump==null)
            {
                GuiHelper.ShowMessage("Please select pump",MessageBoxIcon.Error);
                return;
            }

            if (hose == null)
            {
                GuiHelper.ShowMessage("Please select hose", MessageBoxIcon.Error);
                return;
            }

            var price = hose.Table2.Price1;
            var value = Decimal.Parse(tbxValue.Text);
            if (value<=0m)
            {
                GuiHelper.ShowMessage("Invalid value", MessageBoxIcon.Error);
                return;
            }
            int deliveryId;
            if (rbPrepay.Checked)
                deliveryId =0;
            else
                deliveryId = DateTime.Now.Hour*3600+DateTime.Now.Minute*60+DateTime.Now.Second;
            var hoseId = hose.Table1.ID;

            decimal limit = 0m;

            if (rbRefund.Checked)
                limit = 2m * value;

            var nozzleIdx = ((hoseId == pump.Table.H1) ? 1
                    : (hoseId == pump.Table.H2) ? 2
                        : (hoseId == pump.Table.H3) ? 3
                            : (hoseId == pump.Table.H4) ? 4
                                : 0);

            var delivery = new PetrolSaleItem()
            {
                SaleID = 0,
                DeliveryId = deliveryId,
                DispenserId = pump.Table.ID,
                NozzleId = nozzleIdx,
                BlendId = int.Parse(hose.Table2.SKU),// the Item.SKU must be an integer value
                Mode = authModes,
                Limit = limit,
                Volume = Math.Round(value / price, 4),
                Amount = value,
                Price = price
            };

            ActiveDelivery = delivery;
            SendScanCode(hose.Table2.UPC);
        }
    }
}
