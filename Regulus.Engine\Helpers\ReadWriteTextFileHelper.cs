﻿using System.Collections.Generic;
using System.IO;
using Triquestra.Common.PumpEsm.Types;

namespace Triquestra.Common.PumpEsm.Helpers
{
    public static class ReadWriteTextFileHelper
    {
        public static void WriteToFile(string filePath, List<PetrolSaleItem> itemsInDelivery)
        {
            using (Stream stream = File.Open(filePath, FileMode.Create))
            {
                var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                formatter.Serialize(stream, itemsInDelivery);
            }
        }

        public static List<PetrolSaleItem> ReadFromFile(string filePath)
        {
            if (File.Exists(filePath))
                using (Stream stream = File.Open(filePath, FileMode.Open))
                {
                    if (stream.Length > 0)
                    {
                        var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                        return (List<PetrolSaleItem>)formatter.Deserialize(stream);
                    }
                    else
                        return null;
                }
            else
                return null;
        }
    }
}
