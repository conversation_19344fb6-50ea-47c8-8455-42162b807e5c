using System;
using System.Collections.Generic;

namespace JPL_Demo_POS_CSharp
{
    // Additional message types that extend DomsPosClasses.cs

    #region FpSupTransBufStatus

    public class FpSupTransBufStatusRespType : DomsPosResponseType
    {
        public class FpSupTransBufStatusRespDataType
        {
            public class TransType
            {
                public class TransInfoFlagsType
                {
                    public int StoredTrans { get; set; }
                    public int ErrorTrans { get; set; }
                    public int TransGreaterThanMinLimit { get; set; }
                    public int PrepayModeUsed { get; set; }
                    public int MoneyDueIsNegative { get; set; }
                }

                public string TransSeqNo { get; set; }
                public string SmId { get; set; }
                public string TransLockId { get; set; }
                public BitFlagType<TransInfoFlagsType> TransInfoFlags { get; set; }
                public string MoneyDue_e { get; set; }
                public string Vol_e { get; set; }
                public string FcGradeId { get; set; }
            }

            public string FpId { get; set; }
            public List<TransType> TransInSupBuffer { get; set; }
        }

        public FpSupTransBufStatusRespDataType data { get; set; }
    }

    #endregion

    #region FpSupTrans

    public class FpSupTransReqType : DomsPosBaseType
    {
        public class TransParIdType
        {
            public static string FcShiftNo = "30";
            public static string ReceiptNo = "31";
            public static string AuthId = "41";
            public static string SmId = "42";
            public static string FmId = "43";
            public static string FpId = "44";
            public static string FcPriceGroupId = "45";
            public static string FcPriceSetId = "46";
            public static string StartLimit = "47";
            public static string StartLimit_e = "48";
            public static string CurrencyCode = "49";
            public static string FcGradeId = "51";
            public static string Price = "52";
            public static string Vol = "53";
            public static string Money = "54";
            public static string SecurityTelegram = "55";
            public static string Price_e = "56";
            public static string Vol_e = "57";
            public static string Money_e = "58";
        }

        public class FpSupTransReqDataType
        {
            public string FpId { get; set; }
            public int TransSeqNo { get; set; }
            public string PosId { get; set; }
            public List<string> TransParId { get; set; }
        }

        public FpSupTransReqDataType data { get; set; }

        public FpSupTransReqType(int fpId, int transSeqNo, int posId, List<string> transParIdList = null) : base("FpSupTrans_req", "00H")
        {
            data = new FpSupTransReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.TransSeqNo = transSeqNo;
            this.data.PosId = posId.ToString();

            if (transParIdList == null)
            {
                this.data.TransParId = new List<string>();
            }
            else
            {
                this.data.TransParId = transParIdList;
            }
        }
    }

    public class FpSupTransRespType : DomsPosResponseType
    {
        public class FpSupTransRespDataType
        {
            public class TransParsType
            {
                public string Money_e { get; set; }
                public string Vol_e { get; set; }
            }

            public string FpId { get; set; }
            public string TransSeqNo { get; set; }
            public TransParsType TransPars { get; set; }
        }

        public FpSupTransRespDataType data { get; set; }
    }

    #endregion

    #region unlock_FpSupTrans

    public class FpUnLockSupTransReqType : DomsPosBaseType
    {
        public class FpUnLockSupTransReqDataType
        {
            public string FpId { get; set; }
            public string TransSeqNo { get; set; }
            public string PosId { get; set; }
        }

        public FpUnLockSupTransReqDataType data { get; set; }

        public FpUnLockSupTransReqType(int fpId, int transSeqNo, int posId) : base("unlock_FpSupTrans_req", "00H")
        {
            this.data = new FpUnLockSupTransReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.TransSeqNo = transSeqNo.ToString();
            this.data.PosId = posId.ToString();
        }
    }

    #endregion

    #region clear_FpSupTrans

    public class FpClrSupTransBufReqType : DomsPosBaseType
    {
        public class FpClrSupTransBufReqDataType
        {
            public string FpId { get; set; }
            public string PosId { get; set; }
            public string TransSeqNo { get; set; }
            public string Vol_e { get; set; }
            public string Money_e { get; set; }
            public object PaymentParameters { get; set; } = new object();
        }

        public FpClrSupTransBufReqDataType data { get; set; }

        public FpClrSupTransBufReqType(int fpId, int transSeqNo, string vol_e, string money_e, string posId) : base("clear_FpSupTrans_req", "04H")
        {
            this.data = new FpClrSupTransBufReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.PosId = posId.ToString();
            this.data.TransSeqNo = transSeqNo.ToString();
            this.data.Vol_e = vol_e;
            this.data.Money_e = money_e;
        }
    }

    #endregion

    #region FpFuellingData

    public class FpFuellingDataReqType : DomsPosBaseType
    {
        public class FpFuellingDataReqDataType
        {
            public string FpId { get; set; }
        }

        public FpFuellingDataReqDataType data { get; set; }

        public FpFuellingDataReqType(int fpId) : base("FpFuellingData_req", "00H")
        {
            this.data = new FpFuellingDataReqDataType();
            this.data.FpId = fpId.ToString();
        }
    }

    public class FpFuellingDataRespType : DomsPosResponseType
    {
        public class FpFuellingDataRespDataType
        {
            public string FpId { get; set; }
            public string Vol_e { get; set; }
            public string Money_e { get; set; }
        }

        public FpFuellingDataRespDataType data { get; set; }
    }

    #endregion

    #region FpGradeTotals

    public class FpGradeTotalsReqType : DomsPosBaseType
    {
        public class FpGradeTotalsReqDataType
        {
            public string FpId { get; set; }
        }

        public FpGradeTotalsReqDataType data { get; set; }

        public FpGradeTotalsReqType(int fpId) : base("FpGradeTotals_req", "00H")
        {
            this.data = new FpGradeTotalsReqDataType();
            this.data.FpId = fpId.ToString();
        }
    }

    public class FpGradeTotalsRespType : DomsPosResponseType
    {
        public class FpGradeTotalsRespDataType
        {
            public class FpGradeOptionsType
            {
                public string FcGradeId { get; set; }
                public string FpGradeOptionNo { get; set; }
                public string FpGradeVolTotal { get; set; }
            }

            public string FpId { get; set; }
            public string FpGrandVolTotal { get; set; }
            public string FpGrandMoneyTotal { get; set; }
            public List<FpGradeOptionsType> FpGradeOptions { get; set; }
        }

        public FpGradeTotalsRespDataType data { get; set; }
    }

    #endregion

    #region PumpGradeTotals

    public class PumpGradeTotalsReqType : DomsPosBaseType
    {
        public class PumpGradeTotalsReqDataType
        {
            public string FpId { get; set; }
        }

        public PumpGradeTotalsReqDataType data { get; set; }

        public PumpGradeTotalsReqType(int fpId) : base("PumpGradeTotals_req", "00H")
        {
            this.data = new PumpGradeTotalsReqDataType();
            this.data.FpId = fpId.ToString();
        }
    }

    public class PumpGradeTotalsRespType : DomsPosResponseType
    {
        public class PumpGradeTotalsRespDataType
        {
            public class FpGradeOptionsType
            {
                public string FcGradeId { get; set; }
                public string FpGradeOptionNo { get; set; }
                public string PuGradeVolTotal { get; set; }
            }

            public string FpId { get; set; }
            public string PuGrandVolTotal { get; set; }
            public string PuGrandMoneyTotal { get; set; }
            public List<FpGradeOptionsType> FpGradeOptions { get; set; }
        }

        public PumpGradeTotalsRespDataType data { get; set; }
    }

    #endregion
}
