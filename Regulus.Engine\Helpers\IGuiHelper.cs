﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace Triquestra.Common.PumpEsm.Helpers
{
    public interface IGuiHelper
    {
        void ShowMessage(string text, MessageBoxIcon icon = MessageBoxIcon.Information);

        DialogResult ShowMessage(string text, MessageBoxButtons buttons, MessageBoxIcon icon);

        void ShowError(string text, MessageBoxIcon messageBoxIcon);

        void SetWindowActive(IntPtr targetHwnd);

    }
}
