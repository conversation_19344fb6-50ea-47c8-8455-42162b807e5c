﻿using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcDateTimeRequest : JplRequest
    {
        [JsonPropertyName("data")]
        public new FcDateTimeData Data { get; set; }

        public class FcDateTimeData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }
        }
    }

    public class FcDateTimeResponse : JplResponse
    {
        [JsonPropertyName("data")]
        public new FcDateTimeResponseData Data { get; set; }

        public class FcDateTimeResponseData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }

            [JsonPropertyName("LastDateAndTimeSetting")]
            public string LastDateAndTimeSetting { get; set; }
        }
    }
}
