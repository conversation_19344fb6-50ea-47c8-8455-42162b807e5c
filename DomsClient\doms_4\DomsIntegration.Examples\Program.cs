﻿using System;
using System.Threading.Tasks;
using DomsIntegration;
using DomsIntegration.Models;
using DomsIntegration.Utilities;

namespace DomsIntegration.Examples
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var logger = new ConsoleLogger();
            
            using (var client = new DomsClientFacade(logger))
            {
                try
                {
                    // Configure endpoint
                    var endpoint = new DomsEndpoint
                    {
                        Host = "localhost",
                        Port = 8080,
                        UseSsl = false,
                        TimeoutMs = 30000
                    };

                    // Connect and authenticate
                    Console.WriteLine("Connecting to DOMs controller...");
                    var success = await client.ConnectAndAuthenticateAsync(
                        endpoint, 
                        "admin", 
                        "password", 
                        "ExampleClient", 
                        "1.0.0");

                    if (success)
                    {
                        Console.WriteLine("Successfully connected and authenticated!");

                        // Get forecourt status
                        Console.WriteLine("Getting forecourt status...");
                        var status = await client.Forecourt.GetForecourtStatusAsync();
                        Console.WriteLine($"Forecourt status: {status.Status}");
                        Console.WriteLine($"Number of dispensers: {status.Dispensers.Count}");

                        // Authorize dispense on dispenser 1, nozzle 1
                        if (status.Dispensers.Count > 0)
                        {
                            Console.WriteLine("Authorizing dispense...");
                            var authResponse = await client.Dispense.AuthorizeDispenseAsync(
                                1, 1, maxAmount: 50.00m);

                            if (authResponse.Success)
                            {
                                Console.WriteLine($"Dispense authorized: {authResponse.AuthorizationId}");
                                
                                // Wait a bit then stop
                                await Task.Delay(5000);
                                
                                Console.WriteLine("Stopping dispense...");
                                var stopResponse = await client.Dispense.StopDispenseAsync(
                                    1, authResponse.AuthorizationId);
                                    
                                if (stopResponse.Success)
                                {
                                    Console.WriteLine("Dispense stopped successfully");
                                    if (stopResponse.FinalTransaction != null)
                                    {
                                        Console.WriteLine($"Final amount: {stopResponse.FinalTransaction.Amount:C}");
                                        Console.WriteLine($"Final volume: {stopResponse.FinalTransaction.Volume:F2}");
                                    }
                                }
                            }
                            else
                            {
                                Console.WriteLine($"Failed to authorize dispense: {authResponse.ErrorMessage}");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed to authenticate");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                }
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
