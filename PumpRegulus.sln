﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35431.28
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Regulus.Engine", "Regulus.Engine\Regulus.Engine.csproj", "{46183E79-1842-4E38-B646-75ABA1DFF4CA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{2828FA67-4C7A-44C8-BB4D-C8D6DEAE7248}"
	ProjectSection(SolutionItems) = preProject
		build.cfg = build.cfg
		Regulus Forecourt Controller Interface Specification v1.23.docx = Regulus Forecourt Controller Interface Specification v1.23.docx
		Release.json = Release.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".nuget", ".nuget", "{01345AFA-BB83-4135-B393-87A69D263DD9}"
	ProjectSection(SolutionItems) = preProject
		.nuget\NuGet.Config = .nuget\NuGet.Config
		.nuget\NuGet.exe = .nuget\NuGet.exe
		.nuget\NuGet.targets = .nuget\NuGet.targets
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "FuelDataAccess", "FuelDataAccess\FuelDataAccess.csproj", "{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "udpPacketTest", "udpPackettest\udpPacketTest.csproj", "{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "pumpEmulator", "pumpEmulator\pumpEmulator.csproj", "{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{718F241A-E383-4E47-88C7-C63FA7637266}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "pumpRegulus.Tests", "tests\pumpRegulus.Tests\pumpRegulus.Tests.csproj", "{3977C634-B117-4509-A6DA-999498661896}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Triquestra.Common.PumpEsm.Messaging.Tests", "tests\Triquestra.Common.PumpEsm.Messaging.Tests\Triquestra.Common.PumpEsm.Messaging.Tests.csproj", "{633FFFFB-896B-434E-A660-81205424F943}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Regulus.Interface", "Regulus.Interface\Regulus.Interface.csproj", "{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "pumpRegulus", "pumpRegulus\pumpRegulus.csproj", "{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Triquestra.Common.ForeCourt.Comms", "src_packages\Triquestra.Common.ForeCourt.Comms\Triquestra.Common.ForeCourt.Comms.csproj", "{0513D6D9-FA22-4890-BC07-9F513E07FBB5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scr_packages", "scr_packages", "{16FDE1C3-4457-4126-8FE3-D96B2082C7F4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Triquestra.Common.ForeCourt.Messaging", "src_packages\Triquestra.Common.ForeCourt.Messaging\Triquestra.Common.ForeCourt.Messaging.csproj", "{8D6139A7-070A-4A46-81DA-BF3B21F82657}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Triquestra.Common.Forecourt", "src_packages\Triquestra.Common.ForeCourt.RegulusInterface\Triquestra.Common.Forecourt.csproj", "{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DomsClient", "DomsClient", "{2D5F073D-B2BB-4D6B-89D7-820260981CB6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DomsIntegration.Core", "DomsClient\doms_5\src\DomsIntegration.Core\DomsIntegration.Core.csproj", "{EBC397EF-9512-403C-824C-428AF3A3959E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Jason|Any CPU = Jason|Any CPU
		Jason|Mixed Platforms = Jason|Mixed Platforms
		Jason|x64 = Jason|x64
		Jason|x86 = Jason|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x64 = Release|x64
		Release|x86 = Release|x86
		UnitTest|Any CPU = UnitTest|Any CPU
		UnitTest|Mixed Platforms = UnitTest|Mixed Platforms
		UnitTest|x64 = UnitTest|x64
		UnitTest|x86 = UnitTest|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Jason|Any CPU.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Jason|Mixed Platforms.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Jason|x64.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Jason|x86.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|x64.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.Release|x86.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.UnitTest|Any CPU.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.UnitTest|Mixed Platforms.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.UnitTest|x64.ActiveCfg = Release|Any CPU
		{46183E79-1842-4E38-B646-75ABA1DFF4CA}.UnitTest|x86.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|x64.ActiveCfg = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Debug|x86.ActiveCfg = Debug|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Jason|Any CPU.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Jason|Mixed Platforms.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Jason|x64.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Jason|x86.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|x64.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.Release|x86.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.UnitTest|Any CPU.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.UnitTest|Mixed Platforms.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.UnitTest|x64.ActiveCfg = Release|Any CPU
		{AF35B5EA-1DAB-47BA-BD7F-A2D79FA4A089}.UnitTest|x86.ActiveCfg = Release|Any CPU
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|Any CPU.ActiveCfg = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|x64.ActiveCfg = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|x86.ActiveCfg = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Debug|x86.Build.0 = Debug|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Jason|Any CPU.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Jason|Mixed Platforms.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Jason|x64.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Jason|x86.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|Any CPU.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|Mixed Platforms.Build.0 = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|x64.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|x86.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.Release|x86.Build.0 = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.UnitTest|Any CPU.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.UnitTest|Mixed Platforms.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.UnitTest|x64.ActiveCfg = Release|x86
		{6F83B61E-5552-41A9-A502-A13BB8A9BDA8}.UnitTest|x86.ActiveCfg = Release|x86
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Jason|Any CPU.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Jason|Mixed Platforms.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Jason|x64.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Jason|x86.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Release|x64.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.Release|x86.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.UnitTest|Any CPU.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.UnitTest|Mixed Platforms.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.UnitTest|x64.ActiveCfg = Release|Any CPU
		{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}.UnitTest|x86.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Debug|x86.Build.0 = Debug|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Jason|Any CPU.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Jason|Mixed Platforms.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Jason|x64.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Jason|x86.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|Any CPU.Build.0 = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|x64.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|x86.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.Release|x86.Build.0 = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.UnitTest|Any CPU.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.UnitTest|Mixed Platforms.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.UnitTest|x64.ActiveCfg = Release|Any CPU
		{3977C634-B117-4509-A6DA-999498661896}.UnitTest|x86.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|x64.ActiveCfg = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|x86.ActiveCfg = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Debug|x86.Build.0 = Debug|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Jason|Any CPU.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Jason|Mixed Platforms.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Jason|x64.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Jason|x86.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|Any CPU.Build.0 = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|x64.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|x86.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.Release|x86.Build.0 = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.UnitTest|Any CPU.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.UnitTest|Mixed Platforms.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.UnitTest|x64.ActiveCfg = Release|Any CPU
		{633FFFFB-896B-434E-A660-81205424F943}.UnitTest|x86.ActiveCfg = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|x64.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Debug|x86.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|x64.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|x64.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|x86.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Jason|x86.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|x64.ActiveCfg = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|x64.Build.0 = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|x86.ActiveCfg = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.Release|x86.Build.0 = Release|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|x64.Build.0 = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}.UnitTest|x86.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|x64.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Debug|x86.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|x64.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|x64.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|x86.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Jason|x86.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|x64.ActiveCfg = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|x64.Build.0 = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|x86.ActiveCfg = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.Release|x86.Build.0 = Release|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|x64.Build.0 = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{9A2F35AA-91CA-44A4-B385-C7CB00FAD2FC}.UnitTest|x86.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|x64.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Debug|x86.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|x64.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|x64.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|x86.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Jason|x86.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|x64.ActiveCfg = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|x64.Build.0 = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|x86.ActiveCfg = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.Release|x86.Build.0 = Release|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|x64.Build.0 = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5}.UnitTest|x86.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|x64.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Debug|x86.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|x64.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|x64.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|x86.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Jason|x86.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|x64.ActiveCfg = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|x64.Build.0 = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|x86.ActiveCfg = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.Release|x86.Build.0 = Release|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|x64.Build.0 = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{8D6139A7-070A-4A46-81DA-BF3B21F82657}.UnitTest|x86.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|x64.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Debug|x86.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|x64.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|x64.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|x86.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Jason|x86.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|x64.ActiveCfg = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|x64.Build.0 = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|x86.ActiveCfg = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.Release|x86.Build.0 = Release|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|x64.Build.0 = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3}.UnitTest|x86.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|x64.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Debug|x86.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|Any CPU.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|Mixed Platforms.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|x64.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|x64.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|x86.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Jason|x86.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|x64.ActiveCfg = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|x64.Build.0 = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|x86.ActiveCfg = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.Release|x86.Build.0 = Release|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|Any CPU.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|Mixed Platforms.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|x64.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|x64.Build.0 = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|x86.ActiveCfg = Debug|Any CPU
		{EBC397EF-9512-403C-824C-428AF3A3959E}.UnitTest|x86.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3977C634-B117-4509-A6DA-999498661896} = {718F241A-E383-4E47-88C7-C63FA7637266}
		{633FFFFB-896B-434E-A660-81205424F943} = {718F241A-E383-4E47-88C7-C63FA7637266}
		{0513D6D9-FA22-4890-BC07-9F513E07FBB5} = {16FDE1C3-4457-4126-8FE3-D96B2082C7F4}
		{8D6139A7-070A-4A46-81DA-BF3B21F82657} = {16FDE1C3-4457-4126-8FE3-D96B2082C7F4}
		{E0AC3AB4-3F3B-4524-80A5-2E5AD93A4EF3} = {16FDE1C3-4457-4126-8FE3-D96B2082C7F4}
		{EBC397EF-9512-403C-824C-428AF3A3959E} = {2D5F073D-B2BB-4D6B-89D7-820260981CB6}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4BCD82CF-C4E3-40FF-9F10-78F03D90ACD5}
	EndGlobalSection
	GlobalSection(SubversionScc) = preSolution
		Svn-Managed = True
		Manager = AnkhSVN - Subversion Support for Visual Studio
	EndGlobalSection
EndGlobal
