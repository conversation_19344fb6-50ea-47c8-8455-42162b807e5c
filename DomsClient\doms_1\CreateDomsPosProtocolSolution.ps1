# PowerShell script to create Doms POS Protocol C# Framework 4.8 solution
param(
    [string]$SolutionPath = "C:\Source\DomsPosProtocol",
    [string]$SolutionName = "DomsPosProtocol"
)

# Ensure we have the required tools
if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
    Write-Error "dotnet CLI is required but not found. Please install .NET SDK."
    exit 1
}

Write-Host "Creating Doms POS Protocol Solution at: $SolutionPath" -ForegroundColor Green

# Create solution directory (use a subdirectory to avoid conflicts)
$FullSolutionPath = Join-Path $SolutionPath $SolutionName
if (Test-Path $FullSolutionPath) {
    Write-Host "Directory already exists. Removing..." -ForegroundColor Yellow
    Remove-Item $FullSolutionPath -Recurse -Force
}
New-Item -ItemType Directory -Path $FullSolutionPath -Force | Out-Null
Set-Location $FullSolutionPath

# Create solution
Write-Host "Creating solution..." -ForegroundColor Blue
dotnet new sln -n $SolutionName

# Create class library project targeting .NET Framework 4.8
$ProjectName = "DomsPosProtocol"
Write-Host "Creating class library project: $ProjectName" -ForegroundColor Blue

# Create project directory
New-Item -ItemType Directory -Path $ProjectName -Force | Out-Null
Set-Location $ProjectName

# Create the .csproj file manually for .NET Framework 4.8
$projectContent = @"
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>latest</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="6.0.6" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Net.Http" />
  </ItemGroup>

</Project>
"@

$projectContent | Out-File -FilePath "$ProjectName.csproj" -Encoding UTF8

# Navigate back and add project to solution
Set-Location ..
dotnet sln add "$ProjectName\$ProjectName.csproj"

# Navigate to project directory
Set-Location $ProjectName

# Create directory structure
$directories = @(
    "Constants",
    "Models",
    "Messages\Core",
    "Messages\ForecourtController", 
    "Messages\Services",
    "Network",
    "Network\Services",
    "Examples"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

Write-Host "Creating class files..." -ForegroundColor Blue

# Create Constants.cs
$constantsContent = @'
using System;

namespace DomsPosProtocol.Constants
{
    public static class MessageNames
    {
        // General FC Messages
        public const string FC_LOGON_REQ = "FcLogon_req";
        public const string FC_LOGON_RESP = "FcLogon_resp";
        public const string FC_STATUS_REQ = "FcStatus_req";
        public const string FC_STATUS_RESP = "FcStatus_resp";
        public const string FC_DATE_TIME_REQ = "FcDateAndTime_req";
        public const string FC_DATE_TIME_RESP = "FcDateAndTime_resp";
        public const string CHANGE_FC_DATE_TIME_REQ = "change_FcDateAndTime_req";
        public const string CHANGE_FC_DATE_TIME_RESP = "change_FcDateAndTime_resp";
        
        // Status Messages
        public const string FC_INSTALL_STATUS_REQ = "FcInstallStatus_req";
        public const string FC_INSTALL_STATUS_RESP = "FcInstallStatus_resp";
        public const string FC_PRICE_SET_STATUS_REQ = "FcPriceSetStatus_req";
        public const string FC_PRICE_SET_STATUS_RESP = "FcPriceSetStatus_resp";
        
        // Service Messages
        public const string FC_SERVICE_MSG_REQ = "FcServiceMsg_req";
        public const string FC_SERVICE_MSG_RESP = "FcServiceMsg_resp";
        public const string CLEAR_FC_SERVICE_MSG_REQ = "clear_FcServiceMsg_req";
        public const string CLEAR_FC_SERVICE_MSG_RESP = "clear_FcServiceMsg_resp";
        
        // Back Office Records
        public const string BACK_OFFICE_RECORD_REQ = "BackOfficeRecord_req";
        public const string BACK_OFFICE_RECORD_RESP = "BackOfficeRecord_resp";
        public const string STORE_BACK_OFFICE_RECORD_REQ = "store_BackOfficeRecord_req";
        public const string STORE_BACK_OFFICE_RECORD_RESP = "store_BackOfficeRecord_resp";
        public const string CLEAR_BACK_OFFICE_RECORD_REQ = "clear_BackOfficeRecord_req";
        public const string CLEAR_BACK_OFFICE_RECORD_RESP = "clear_BackOfficeRecord_resp";
        
        // Installation Data
        public const string CLEAR_INSTALL_DATA_REQ = "clear_InstallData_req";
        public const string CLEAR_INSTALL_DATA_RESP = "clear_InstallData_resp";
        
        // Client Data
        public const string CLIENT_DATA_REQ = "ClientData_req";
        public const string CLIENT_DATA_RESP = "ClientData_resp";
        public const string STORE_CLIENT_DATA_REQ = "store_ClientData_req";
        public const string STORE_CLIENT_DATA_RESP = "store_ClientData_resp";
        
        // Connection Status
        public const string POS_CONNECTION_STATUS_REQ = "PosConnectionStatus_req";
        public const string POS_CONNECTION_STATUS_RESP = "PosConnectionStatus_resp";
        public const string PSS_PERIPHERALS_STATUS_REQ = "PssPeripheralsStatus_req";
        public const string PSS_PERIPHERALS_STATUS_RESP = "PssPeripheralsStatus_resp";
        
        // Utility
        public const string CHANGE_FC_STATUS_UPDATE_MODE_REQ = "change_FcStatusUpdateMode_req";
        public const string CHANGE_FC_STATUS_UPDATE_MODE_RESP = "change_FcStatusUpdateMode_resp";
        public const string ECHO_COMMAND_REQ = "echo_req";
        public const string ECHO_COMMAND_RESP = "echo_resp";
        public const string HEARTBEAT = "heartbeat";
        public const string MULTI_MESSAGE_RESP = "MultiMessage_resp";
        public const string REJECT_MESSAGE_RESP = "RejectMessage_resp";
        public const string JPL = "jpl";
    }

    public static class SubCodes
    {
        public const string SUBC_00H = "00H";
        public const string SUBC_01H = "01H";
        public const string SUBC_02H = "02H";
        public const string SUBC_03H = "03H";
    }

    public static class TcpPorts
    {
        public const int UNENCRYPTED_PORT = 8888;
        public const int ENCRYPTED_TLS_PORT = 8889;
    }

    public static class ProtocolDelimiters
    {
        public const byte STX = 2;
        public const byte ETX = 3;
    }
}
'@

$constantsContent | Out-File -FilePath "Constants\Constants.cs" -Encoding UTF8

# Create core message models
$coreModelsContent = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace DomsPosProtocol.Models
{
    // Base message class
    public abstract class JplMessage
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("subCode")]
        public string SubCode { get; set; }

        [JsonPropertyName("correlationId")]
        public object CorrelationId { get; set; }
    }

    // Request message base
    public abstract class JplRequest : JplMessage
    {
        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Response message base
    public abstract class JplResponse : JplMessage
    {
        [JsonPropertyName("solicited")]
        public bool Solicited { get; set; }

        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Enumeration wrapper
    public class EnumValue<T>
    {
        [JsonPropertyName("enum")]
        public Dictionary<string, string> Enum { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }
    }

    // Bit flags wrapper
    public class BitFlags
    {
        [JsonPropertyName("value")]
        public int Value { get; set; }

        [JsonPropertyName("bits")]
        public Dictionary<string, int> Bits { get; set; }
    }

    // Multi-message container
    public class MultiMessageResponse : JplResponse
    {
        public class MultiMessageData
        {
            [JsonPropertyName("messages")]
            public List<JplResponse> Messages { get; set; }
        }
    }

    // Reject message
    public class RejectMessageResponse : JplResponse
    {
        public class RejectData
        {
            [JsonPropertyName("RejectedExtendedMsgCode")]
            public string RejectedExtendedMsgCode { get; set; }

            [JsonPropertyName("RejectedMsgSubc")]
            public string RejectedMsgSubc { get; set; }

            [JsonPropertyName("RejectCode")]
            public EnumValue<string> RejectCode { get; set; }

            [JsonPropertyName("RejectInfo")]
            public string RejectInfo { get; set; }

            [JsonPropertyName("RejectInfoText")]
            public string RejectInfoText { get; set; }
        }
    }
}
'@

$coreModelsContent | Out-File -FilePath "Models\CoreModels.cs" -Encoding UTF8

# Create Forecourt Controller Messages (split into smaller parts to avoid string issues)
$fcMessages1 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.ForecourtController
{
    // FC Logon Messages
    public class FcLogonRequest : JplRequest
    {
        public class FcLogonData
        {
            [JsonPropertyName("FcAccessCode")]
            public string FcAccessCode { get; set; }

            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("PosVersionId")]
            public string PosVersionId { get; set; }

            [JsonPropertyName("FcLogonPars")]
            public FcLogonParameters FcLogonPars { get; set; }
        }

        public class FcLogonParameters
        {
            [JsonPropertyName("UnsolMsgList")]
            public List<UnsolicitedMessage> UnsolMsgList { get; set; }
        }

        public class UnsolicitedMessage
        {
            [JsonPropertyName("ExtMsgCode")]
            public string ExtMsgCode { get; set; }

            [JsonPropertyName("MsgSubc")]
            public string MsgSubc { get; set; }
        }
    }

    public class FcLogonResponse : JplResponse
    {
        public class FcLogonResponseData
        {
            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("FcHwType")]
            public int FcHwType { get; set; }

            [JsonPropertyName("FcHwVersionNo")]
            public string FcHwVersionNo { get; set; }

            [JsonPropertyName("FcSwType")]
            public int FcSwType { get; set; }

            [JsonPropertyName("FcSwVersionNo")]
            public string FcSwVersionNo { get; set; }

            [JsonPropertyName("FcSwDate")]
            public string FcSwDate { get; set; }

            [JsonPropertyName("FcSwBlocks")]
            public List<FcSwBlock> FcSwBlocks { get; set; }

            [JsonPropertyName("UnsolMessages")]
            public List<FcLogonRequest.UnsolicitedMessage> UnsolMessages { get; set; }
        }

        public class FcSwBlock
        {
            [JsonPropertyName("FcSwMainBlockId")]
            public string FcSwMainBlockId { get; set; }

            [JsonPropertyName("FcSwSubBlockId")]
            public string FcSwSubBlockId { get; set; }

            [JsonPropertyName("FcSwBlockReleaseNo")]
            public string FcSwBlockReleaseNo { get; set; }

            [JsonPropertyName("FcSwBlockCheckCode")]
            public string FcSwBlockCheckCode { get; set; }
        }
    }
}
'@

$fcMessages1 | Out-File -FilePath "Messages\ForecourtController\FcLogonMessages.cs" -Encoding UTF8

$fcMessages2 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.ForecourtController
{
    // FC Status Messages
    public class FcStatusRequest : JplRequest
    {
        public class FcStatusData
        {
            [JsonPropertyName("NecessaryDevices")]
            public List<NecessaryDevice> NecessaryDevices { get; set; }
        }

        public class NecessaryDevice
        {
            [JsonPropertyName("NecessaryDeviceTypeId")]
            public string NecessaryDeviceTypeId { get; set; }

            [JsonPropertyName("NecessaryDeviceStatus")]
            public string NecessaryDeviceStatus { get; set; }
        }
    }

    public class FcStatusResponse : JplResponse
    {
        public class FcStatusResponseData
        {
            [JsonPropertyName("FcStatus1Flags")]
            public BitFlags FcStatus1Flags { get; set; }

            [JsonPropertyName("FcStatus2Flags")]
            public BitFlags FcStatus2Flags { get; set; }

            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }

            [JsonPropertyName("FcMasterResetDateAndTime")]
            public string FcMasterResetDateAndTime { get; set; }

            [JsonPropertyName("FcMasterResetCode")]
            public int FcMasterResetCode { get; set; }

            [JsonPropertyName("FcResetDateAndTime")]
            public string FcResetDateAndTime { get; set; }

            [JsonPropertyName("FcResetCode")]
            public string FcResetCode { get; set; }

            [JsonPropertyName("FcStatusPars")]
            public FcStatusParameters FcStatusPars { get; set; }
        }

        public class FcStatusParameters
        {
            [JsonPropertyName("FcShiftNo")]
            public string FcShiftNo { get; set; }

            [JsonPropertyName("FcShiftChangeDateAndTime")]
            public string FcShiftChangeDateAndTime { get; set; }

            [JsonPropertyName("VATRateSeqNo")]
            public string VATRateSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingSeqNo")]
            public string FcRTCSettingSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingDateAndTime")]
            public string FcRTCSettingDateAndTime { get; set; }

            [JsonPropertyName("FcCurrencySettings")]
            public FcCurrencySettings FcCurrencySettings { get; set; }
        }

        public class FcCurrencySettings
        {
            [JsonPropertyName("CurrencyCode")]
            public string CurrencyCode { get; set; }

            [JsonPropertyName("DecimalPositionInPrice")]
            public int DecimalPositionInPrice { get; set; }

            [JsonPropertyName("DecimalPositionInVol")]
            public int DecimalPositionInVol { get; set; }

            [JsonPropertyName("DecimalPositionInMoney")]
            public int DecimalPositionInMoney { get; set; }
        }
    }
}
'@

$fcMessages2 | Out-File -FilePath "Messages\ForecourtController\FcStatusMessages.cs" -Encoding UTF8

$fcMessages3 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.ForecourtController
{
    // Date and Time Messages
    public class FcDateTimeRequest : JplRequest
    {
        public class FcDateTimeData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }
        }
    }

    public class FcDateTimeResponse : JplResponse
    {
        public class FcDateTimeResponseData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }

            [JsonPropertyName("LastDateAndTimeSetting")]
            public string LastDateAndTimeSetting { get; set; }
        }
    }

    // Installation Status Messages
    public class FcInstallStatusRequest : JplRequest
    {
        public class FcInstallStatusData
        {
            // Empty for basic requests
        }
    }

    public class FcInstallStatusResponse : JplResponse
    {
        public class FcInstallStatusResponseData
        {
            [JsonPropertyName("InstalledFcDeviceGroups")]
            public List<InstalledDeviceGroup> InstalledFcDeviceGroups { get; set; }

            [JsonPropertyName("InstalledFcEquipmentTypes")]
            public List<InstalledEquipmentType> InstalledFcEquipmentTypes { get; set; }
        }

        public class InstalledDeviceGroup
        {
            [JsonPropertyName("InstallMsgCode")]
            public string InstallMsgCode { get; set; }

            [JsonPropertyName("ExtendedInstallMsgCode")]
            public string ExtendedInstallMsgCode { get; set; }

            [JsonPropertyName("FcDeviceId")]
            public List<string> FcDeviceId { get; set; }
        }

        public class InstalledEquipmentType
        {
            [JsonPropertyName("FcEquipmentType")]
            public EnumValue<string> FcEquipmentType { get; set; }

            [JsonPropertyName("FcEquipmentId")]
            public List<string> FcEquipmentId { get; set; }
        }
    }

    // Price Set Messages
    public class FcPriceSetStatusRequest : JplRequest
    {
        public class FcPriceSetStatusData
        {
            // Empty for basic requests
        }
    }

    public class FcPriceSetStatusResponse : JplResponse
    {
        public class FcPriceSetStatusResponseData
        {
            [JsonPropertyName("FcPriceSetId")]
            public string FcPriceSetId { get; set; }

            [JsonPropertyName("FcPriceSetDateAndTime")]
            public string FcPriceSetDateAndTime { get; set; }

            [JsonPropertyName("FcPendingPriceSet")]
            public List<PendingPriceSet> FcPendingPriceSet { get; set; }
        }

        public class PendingPriceSet
        {
            [JsonPropertyName("FcPriceSetId")]
            public string FcPriceSetId { get; set; }

            [JsonPropertyName("PriceSetActivationDateAndTime")]
            public string PriceSetActivationDateAndTime { get; set; }
        }
    }
}
'@

$fcMessages3 | Out-File -FilePath "Messages\ForecourtController\FcUtilityMessages.cs" -Encoding UTF8

# Create Service Messages (split into parts)
$serviceMessages1 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Service Message Classes
    public class FcServiceMsgRequest : JplRequest
    {
        public class FcServiceMsgData
        {
            // Empty for basic requests
        }
    }

    public class FcServiceMsgResponse : JplResponse
    {
        public class FcServiceMsgResponseData
        {
            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }

            [JsonPropertyName("FcServiceMsg")]
            public string FcServiceMsg { get; set; }
        }
    }

    public class ClearFcServiceMsgRequest : JplRequest
    {
        public class ClearFcServiceMsgData
        {
            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }
        }
    }

    public class ClearFcServiceMsgResponse : JplResponse
    {
        public class ClearFcServiceMsgResponseData
        {
            [JsonPropertyName("FcServiceLogStatus")]
            public BitFlags FcServiceLogStatus { get; set; }
        }
    }
}
'@

$serviceMessages1 | Out-File -FilePath "Messages\Services\ServiceMessages.cs" -Encoding UTF8

$serviceMessages2 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Back Office Record Messages
    public class BackOfficeRecordRequest : JplRequest
    {
        public class BackOfficeRecordData
        {
            // Empty for basic requests
        }
    }

    public class BackOfficeRecordResponse : JplResponse
    {
        public class BackOfficeRecordResponseData
        {
            [JsonPropertyName("BorSeqNo")]
            public string BorSeqNo { get; set; }

            [JsonPropertyName("BorLen")]
            public int BorLen { get; set; }

            [JsonPropertyName("BorLength")]
            public int BorLength { get; set; }

            [JsonPropertyName("BorFormatId")]
            public EnumValue<string> BorFormatId { get; set; }

            [JsonPropertyName("BorFields")]
            public List<BorField> BorFields { get; set; }

            [JsonPropertyName("BorData")]
            public string BorData { get; set; }
        }

        public class BorField
        {
            [JsonPropertyName("BorFieldBytes")]
            public List<string> BorFieldBytes { get; set; }

            [JsonPropertyName("BorFieldBytes2")]
            public List<string> BorFieldBytes2 { get; set; }
        }
    }

    public class StoreBackOfficeRecordRequest : JplRequest
    {
        public class StoreBackOfficeRecordData
        {
            [JsonPropertyName("BorClientType")]
            public string BorClientType { get; set; }

            [JsonPropertyName("BorClientId")]
            public string BorClientId { get; set; }

            [JsonPropertyName("BorDataType")]
            public string BorDataType { get; set; }

            [JsonPropertyName("BorData")]
            public string BorData { get; set; }
        }
    }

    public class StoreBackOfficeRecordResponse : JplResponse
    {
        public class StoreBackOfficeRecordResponseData
        {
            [JsonPropertyName("BorSeqNo")]
            public string BorSeqNo { get; set; }
        }
    }
}
'@

$serviceMessages2 | Out-File -FilePath "Messages\Services\BackOfficeMessages.cs" -Encoding UTF8

$serviceMessages3 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Clear Installation Data
    public class ClearInstallDataRequest : JplRequest
    {
        public class ClearInstallDataRequestData
        {
            [JsonPropertyName("ExtendedInstallMsgCode")]
            public string ExtendedInstallMsgCode { get; set; }

            [JsonPropertyName("FcDeviceId")]
            public string FcDeviceId { get; set; }
        }
    }

    public class ClearInstallDataResponse : JplResponse
    {
        public class ClearInstallDataResponseData
        {
            // Empty response
        }
    }

    // Client Data Messages
    public class ClientDataRequest : JplRequest
    {
        public class ClientDataRequestData
        {
            [JsonPropertyName("PosId")]
            public string PosId { get; set; }

            [JsonPropertyName("ClientDataOffset")]
            public int ClientDataOffset { get; set; }

            [JsonPropertyName("ClientDataLen")]
            public int ClientDataLen { get; set; }
        }
    }

    public class ClientDataResponse : JplResponse
    {
        public class ClientDataResponseData
        {
            [JsonPropertyName("ClientData")]
            public List<string> ClientData { get; set; }
        }
    }

    public class StoreClientDataRequest : JplRequest
    {
        public class StoreClientDataRequestData
        {
            [JsonPropertyName("PosId")]
            public string PosId { get; set; }

            [JsonPropertyName("ClientDataOffset")]
            public int ClientDataOffset { get; set; }

            [JsonPropertyName("ClientData")]
            public List<string> ClientData { get; set; }
        }
    }

    public class StoreClientDataResponse : JplResponse
    {
        public class StoreClientDataResponseData
        {
            // Empty response
        }
    }
}
'@

$serviceMessages3 | Out-File -FilePath "Messages\Services\ClientDataMessages.cs" -Encoding UTF8

$serviceMessages4 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Connection Status Messages
    public class PosConnectionStatusRequest : JplRequest
    {
        public class PosConnectionStatusData
        {
            // Empty for basic requests
        }
    }

    public class PosConnectionStatusResponse : JplResponse
    {
        public class PosConnectionStatusResponseData
        {
            [JsonPropertyName("Connections")]
            public List<Connection> Connections { get; set; }
        }

        public class Connection
        {
            [JsonPropertyName("PosDeviceType")]
            public EnumValue<string> PosDeviceType { get; set; }

            [JsonPropertyName("ConnType")]
            public EnumValue<int> ConnType { get; set; }

            [JsonPropertyName("ConnAddress")]
            public int ConnAddress { get; set; }

            [JsonPropertyName("ServerPortNo")]
            public int ServerPortNo { get; set; }

            [JsonPropertyName("ConnStatus")]
            public BitFlags ConnStatus { get; set; }
        }
    }

    // Peripheral Status Messages
    public class PssPeripheralsStatusRequest : JplRequest
    {
        public class PssPeripheralsStatusData
        {
            // Empty for basic requests
        }
    }

    public class PssPeripheralsStatusResponse : JplResponse
    {
        public class PssPeripheralsStatusResponseData
        {
            [JsonPropertyName("Peripherals")]
            public List<Peripheral> Peripherals { get; set; }
        }

        public class Peripheral
        {
            [JsonPropertyName("PeripheralType")]
            public EnumValue<string> PeripheralType { get; set; }

            [JsonPropertyName("ConnType")]
            public EnumValue<int> ConnType { get; set; }

            [JsonPropertyName("ConnAddress")]
            public int ConnAddress { get; set; }

            [JsonPropertyName("ServerPortNo")]
            public int ServerPortNo { get; set; }

            [JsonPropertyName("PeripheralStatus")]
            public BitFlags PeripheralStatus { get; set; }
        }
    }
}
'@

$serviceMessages4 | Out-File -FilePath "Messages\Services\StatusMessages.cs" -Encoding UTF8

$serviceMessages5 = @'
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Status Update Mode
    public class ChangeFcStatusUpdateModeRequest : JplRequest
    {
        public class ChangeFcStatusUpdateModeData
        {
            [JsonPropertyName("StatusUpdateCode")]
            public int StatusUpdateCode { get; set; }
        }
    }

    public class ChangeFcStatusUpdateModeResponse : JplResponse
    {
        public class ChangeFcStatusUpdateModeResponseData
        {
            // Empty response
        }
    }

    // Echo Command
    public class EchoCommandRequest : JplRequest
    {
        public class EchoCommandData
        {
            [JsonPropertyName("EchoData")]
            public string EchoData { get; set; }
        }
    }

    public class EchoCommandResponse : JplResponse
    {
        public class EchoCommandResponseData
        {
            [JsonPropertyName("EchoData")]
            public string EchoData { get; set; }
        }
    }

    // Heartbeat
    public class HeartbeatMessage : JplMessage
    {
        public class HeartbeatData
        {
            // Empty data object
        }
    }
}
'@

$serviceMessages5 | Out-File -FilePath "Messages\Services\UtilityMessages.cs" -Encoding UTF8

# Create Network Configuration
$networkConfigContent = @'
using System;
using System.Security.Cryptography.X509Certificates;

namespace DomsPosProtocol.Network
{
    // Connection configuration
    public class PssConnectionConfig
    {
        public string HostName { get; set; } = "localhost";
        public int Port { get; set; } = 8888;
        public bool UseTls { get; set; } = false;
        public int ConnectionTimeoutMs { get; set; } = 30000;
        public int HeartbeatIntervalMs { get; set; } = 20000;
        public int MaxHeartbeatMissedCount { get; set; } = 3;
        public X509Certificate2 ClientCertificate { get; set; }
        public bool ValidateServerCertificate { get; set; } = true;
    }

    // Connection state
    public enum ConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        LoggedOn,
        Disconnecting,
        Error
    }

    // Event arguments for connection events
    public class ConnectionStateChangedEventArgs : EventArgs
    {
        public ConnectionState OldState { get; set; }
        public ConnectionState NewState { get; set; }
        public string Reason { get; set; }
    }

    public class MessageReceivedEventArgs : EventArgs
    {
        public DomsPosProtocol.Models.JplMessage Message { get; set; }
        public string RawMessage { get; set; }
    }

    public class ErrorEventArgs : EventArgs
    {
        public Exception Exception { get; set; }
        public string Message { get; set; }
        public bool IsFatal { get; set; }
    }
}
'@

$networkConfigContent | Out-File -FilePath "Network\NetworkConfiguration.cs" -Encoding UTF8

# Create PSS Client (split into parts for reliability)
$pssClientPart1 = @'
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network
{
    // Main PSS Client class
    public class PssClient : IDisposable
    {
        private readonly PssConnectionConfig _config;
        private TcpClient _tcpClient;
        private Stream _stream;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Timer _heartbeatTimer;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplResponse>> _pendingRequests;
        private ConnectionState _connectionState;
        private DateTime _lastMessageReceived;
        private readonly object _stateLock = new object();
        private readonly JsonSerializerOptions _jsonOptions;

        public event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;
        public event EventHandler<MessageReceivedEventArgs> UnsolicitedMessageReceived;
        public event EventHandler<ErrorEventArgs> ErrorOccurred;

        public ConnectionState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _connectionState;
                }
            }
            private set
            {
                lock (_stateLock)
                {
                    var oldState = _connectionState;
                    _connectionState = value;
                    ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs
                    {
                        OldState = oldState,
                        NewState = value
                    });
                }
            }
        }

        public bool IsConnected => State == ConnectionState.Connected || State == ConnectionState.LoggedOn;
        public bool IsLoggedOn => State == ConnectionState.LoggedOn;

        public PssClient(PssConnectionConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _cancellationTokenSource = new CancellationTokenSource();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplResponse>>();
            _connectionState = ConnectionState.Disconnected;
            _lastMessageReceived = DateTime.UtcNow;

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            _heartbeatTimer = new Timer(OnHeartbeatTimer, null, Timeout.Infinite, Timeout.Infinite);
        }
'@

$pssClientPart1 | Out-File -FilePath "Network\PssClient_Part1.cs" -Encoding UTF8

$pssClientPart2 = @'
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network
{
    // PSS Client methods part 2
    public partial class PssClient
    {
        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            if (IsConnected)
                return true;

            try
            {
                State = ConnectionState.Connecting;

                _tcpClient = new TcpClient();
                var connectTask = _tcpClient.ConnectAsync(_config.HostName, _config.Port);
                await connectTask;

                if (_config.UseTls)
                {
                    var sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                    var clientCertificates = _config.ClientCertificate != null 
                        ? new System.Security.Cryptography.X509Certificates.X509CertificateCollection { _config.ClientCertificate } 
                        : new System.Security.Cryptography.X509Certificates.X509CertificateCollection();

                    await sslStream.AuthenticateAsClientAsync(_config.HostName, clientCertificates, 
                        System.Security.Authentication.SslProtocols.Tls12, false);
                    _stream = sslStream;
                }
                else
                {
                    _stream = _tcpClient.GetStream();
                }

                State = ConnectionState.Connected;
                _lastMessageReceived = DateTime.UtcNow;

                // Start reading messages
                var readTask = Task.Run(async () => await ReadMessagesAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                // Start heartbeat timer
                _heartbeatTimer.Change(_config.HeartbeatIntervalMs, _config.HeartbeatIntervalMs);

                return true;
            }
            catch (Exception ex)
            {
                State = ConnectionState.Error;
                OnError(ex, "Failed to connect", true);
                return false;
            }
        }

        public async Task<FcLogonResponse> LogonAsync(string accessCode, string countryCode, string posVersionId, 
            CancellationToken cancellationToken = default(CancellationToken))
        {
            if (!IsConnected)
                throw new InvalidOperationException("Must be connected before logging on");

            var request = new FcLogonRequest
            {
                Name = MessageNames.FC_LOGON_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcLogonRequest.FcLogonData
                {
                    FcAccessCode = accessCode,
                    CountryCode = countryCode,
                    PosVersionId = posVersionId
                }
            };

            var response = await SendRequestAsync<FcLogonResponse>(request, cancellationToken);
            if (response != null)
            {
                State = ConnectionState.LoggedOn;
            }

            return response;
        }

        public async Task<T> SendRequestAsync<T>(JplRequest request, CancellationToken cancellationToken = default(CancellationToken)) 
            where T : JplResponse
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var correlationId = Guid.NewGuid().ToString();
            request.CorrelationId = correlationId;

            var tcs = new TaskCompletionSource<JplResponse>();
            _pendingRequests[correlationId] = tcs;

            try
            {
                await SendMessageAsync(request, cancellationToken);
                
                using (cancellationToken.Register(() => tcs.TrySetCanceled()))
                {
                    var response = await tcs.Task;
                    return response as T;
                }
            }
            finally
            {
                TaskCompletionSource<JplResponse> removed;
                _pendingRequests.TryRemove(correlationId, out removed);
            }
        }

        public async Task SendMessageAsync(JplMessage message, CancellationToken cancellationToken = default(CancellationToken))
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var messageBytes = Encoding.UTF8.GetBytes(json);
            
            var frame = new byte[messageBytes.Length + 2];
            frame[0] = ProtocolDelimiters.STX;
            Array.Copy(messageBytes, 0, frame, 1, messageBytes.Length);
            frame[frame.Length - 1] = ProtocolDelimiters.ETX;

            await _stream.WriteAsync(frame, 0, frame.Length, cancellationToken);
            await _stream.FlushAsync(cancellationToken);
        }

        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var heartbeat = new HeartbeatMessage
            {
                Name = MessageNames.HEARTBEAT,
                SubCode = SubCodes.SUBC_00H,
                Data = new HeartbeatMessage.HeartbeatData()
            };

            await SendMessageAsync(heartbeat, cancellationToken);
        }

        public async Task DisconnectAsync()
        {
            State = ConnectionState.Disconnecting;
            
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _cancellationTokenSource?.Cancel();

            try
            {
                _stream?.Close();
                _tcpClient?.Close();
            }
            catch (Exception ex)
            {
                OnError(ex, "Error during disconnect", false);
            }
            finally
            {
                State = ConnectionState.Disconnected;
            }
        }

        public void Dispose()
        {
            _heartbeatTimer?.Dispose();
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Dispose();
        }
    }
}
'@

$pssClientPart2 | Out-File -FilePath "Network\PssClient_Part2.cs" -Encoding UTF8

# Create the main PssClient file that combines both parts
$pssClientMain = @'
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network
{
    public partial class PssClient : IDisposable
    {
        private async Task ReadMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                    {
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    for (int i = 0; i < bytesRead; i++)
                    {
                        var currentByte = buffer[i];

                        if (currentByte == ProtocolDelimiters.STX)
                        {
                            messageBuffer.Clear();
                        }
                        else if (currentByte == ProtocolDelimiters.ETX)
                        {
                            if (messageBuffer.Count > 0)
                            {
                                var messageJson = Encoding.UTF8.GetString(messageBuffer.ToArray());
                                await ProcessReceivedMessage(messageJson);
                                messageBuffer.Clear();
                            }
                        }
                        else
                        {
                            messageBuffer.Add(currentByte);
                        }
                    }
                }
            }
            catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
            {
                OnError(ex, "Error reading messages", true);
            }
        }

        private async Task ProcessReceivedMessage(string messageJson)
        {
            try
            {
                var messageDocument = JsonDocument.Parse(messageJson);
                var root = messageDocument.RootElement;

                var name = root.GetProperty("name").GetString();
                var subCode = root.GetProperty("subCode").GetString();
                JsonElement solicitedProp;
                var solicited = root.TryGetProperty("solicited", out solicitedProp) ? solicitedProp.GetBoolean() : false;
                JsonElement corrProp;
                var correlationId = root.TryGetProperty("correlationId", out corrProp) ? corrProp.GetString() : null;

                JplMessage message = null;
                switch (name)
                {
                    case MessageNames.FC_LOGON_RESP:
                        message = JsonSerializer.Deserialize<FcLogonResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.FC_STATUS_RESP:
                        message = JsonSerializer.Deserialize<FcStatusResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.FC_DATE_TIME_RESP:
                    case MessageNames.CHANGE_FC_DATE_TIME_RESP:
                        message = JsonSerializer.Deserialize<FcDateTimeResponse>(messageJson, _jsonOptions);
                        break;
                    case MessageNames.HEARTBEAT:
                        message = JsonSerializer.Deserialize<HeartbeatMessage>(messageJson, _jsonOptions);
                        break;
                }

                if (message != null)
                {
                    MessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                    { 
                        Message = message, 
                        RawMessage = messageJson 
                    });

                    if (!string.IsNullOrEmpty(correlationId))
                    {
                        TaskCompletionSource<JplResponse> tcs;
                        if (_pendingRequests.TryRemove(correlationId, out tcs))
                        {
                            if (message is JplResponse response)
                            {
                                tcs.SetResult(response);
                            }
                        }
                    }
                    else if (!solicited)
                    {
                        UnsolicitedMessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                        { 
                            Message = message, 
                            RawMessage = messageJson 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                OnError(ex, $"Error processing message: {messageJson}", false);
            }
        }

        private void OnHeartbeatTimer(object state)
        {
            try
            {
                if (!IsConnected)
                    return;

                var timeSinceLastMessage = DateTime.UtcNow - _lastMessageReceived;
                if (timeSinceLastMessage.TotalMilliseconds > (_config.HeartbeatIntervalMs * _config.MaxHeartbeatMissedCount))
                {
                    OnError(new TimeoutException("Heartbeat timeout"), "Connection lost - no heartbeat", true);
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        await SendHeartbeatAsync(_cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        OnError(ex, "Failed to send heartbeat", false);
                    }
                });
            }
            catch (Exception ex)
            {
                OnError(ex, "Error in heartbeat timer", false);
            }
        }

        private bool ValidateServerCertificate(object sender, X509Certificate certificate, 
            X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            if (!_config.ValidateServerCertificate)
                return true;

            return sslPolicyErrors == SslPolicyErrors.None;
        }

        private void OnError(Exception exception, string message, bool isFatal)
        {
            ErrorOccurred?.Invoke(this, new ErrorEventArgs
            {
                Exception = exception,
                Message = message,
                IsFatal = isFatal
            });

            if (isFatal)
            {
                State = ConnectionState.Error;
                Task.Run(async () => await DisconnectAsync());
            }
        }
    }
}
'@

$pssClientMain | Out-File -FilePath "Network\PssClient.cs" -Encoding UTF8

# Create Services
$servicesContent = @'
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network.Services
{
    public abstract class PssServiceBase
    {
        protected readonly PssClient Client;

        protected PssServiceBase(PssClient client)
        {
            Client = client ?? throw new ArgumentNullException(nameof(client));
        }

        protected async Task<T> SendRequestAsync<T>(JplRequest request, CancellationToken cancellationToken = default(CancellationToken))
            where T : JplResponse
        {
            return await Client.SendRequestAsync<T>(request, cancellationToken);
        }
    }

    public class ForecourtControllerService : PssServiceBase
    {
        public ForecourtControllerService(PssClient client) : base(client) { }

        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcStatusRequest
            {
                Name = MessageNames.FC_STATUS_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcStatusRequest.FcStatusData()
            };

            return await SendRequestAsync<FcStatusResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcDateTimeRequest
            {
                Name = MessageNames.FC_DATE_TIME_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData()
            };

            return await SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcDateTimeRequest
            {
                Name = MessageNames.CHANGE_FC_DATE_TIME_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData
                {
                    FcDateAndTime = dateTime.ToString("yyyyMMddHHmmss")
                }
            };

            return await SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }
    }
}
'@

$servicesContent | Out-File -FilePath "Network\Services\PssServices.cs" -Encoding UTF8

# Create Connection Factory
$factoryContent = @'
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;

namespace DomsPosProtocol.Network
{
    public class PssConnectionFactory
    {
        public static PssClient CreateClient(PssConnectionConfig config)
        {
            return new PssClient(config);
        }

        public static PssClient CreateUnencryptedClient(string hostname, int port = 8888)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = false
            };
            return new PssClient(config);
        }

        public static PssClient CreateEncryptedClient(string hostname, int port = 8889, 
            X509Certificate2 clientCertificate = null)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = true,
                ClientCertificate = clientCertificate
            };
            return new PssClient(config);
        }
    }
}
'@

$factoryContent | Out-File -FilePath "Network\PssConnectionFactory.cs" -Encoding UTF8

# Create Examples
$examplesContent = @'
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Network;
using DomsPosProtocol.Network.Services;

namespace DomsPosProtocol.Examples
{
    public class PssClientUsageExample
    {
        public async Task ExampleUsage()
        {
            var config = new PssConnectionConfig
            {
                HostName = "*************",
                Port = 8888,
                UseTls = false,
                ConnectionTimeoutMs = 30000,
                HeartbeatIntervalMs = 20000
            };

            using (var client = new PssClient(config))
            {
                client.ConnectionStateChanged += OnConnectionStateChanged;
                client.UnsolicitedMessageReceived += OnUnsolicitedMessage;
                client.ErrorOccurred += OnError;

                if (await client.ConnectAsync())
                {
                    Console.WriteLine("Connected successfully");

                    var loginResponse = await client.LogonAsync(
                        "POS,APPL_ID=POS1,RI,UNSO_FPSTA_3", 
                        "1234", 
                        "POS_VERSION_1.0");

                    if (loginResponse != null)
                    {
                        Console.WriteLine("Logged in successfully");

                        var fcService = new ForecourtControllerService(client);
                        var status = await fcService.GetStatusAsync();
                        Console.WriteLine("Status received");

                        Console.WriteLine("Press any key to disconnect...");
                        Console.ReadKey();
                    }
                }

                await client.DisconnectAsync();
            }
        }

        private void OnConnectionStateChanged(object sender, ConnectionStateChangedEventArgs e)
        {
            Console.WriteLine($"Connection state changed: {e.OldState} -> {e.NewState}");
        }

        private void OnUnsolicitedMessage(object sender, MessageReceivedEventArgs e)
        {
            Console.WriteLine($"Unsolicited message received: {e.Message.Name}");
        }

        private void OnError(object sender, ErrorEventArgs e)
        {
            Console.WriteLine($"Error occurred: {e.Message}");
            if (e.Exception != null)
            {
                Console.WriteLine($"Exception: {e.Exception}");
            }
        }
    }
}
'@

$examplesContent | Out-File -FilePath "Examples\UsageExamples.cs" -Encoding UTF8



