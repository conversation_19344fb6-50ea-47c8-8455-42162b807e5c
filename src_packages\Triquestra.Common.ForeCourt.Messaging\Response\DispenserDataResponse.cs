﻿using System;
using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;
using System.Collections.Generic;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class DispenserDataResponse : CommandResponse
    {
        public List<IDispenserData> Dispensers { get; set; }
       
        public DispenserDataResponse(int sequenceNo, int sourceId, int targetId, IEnumerable<IDispenserData> dispData)
            : base(sequenceNo, sourceId, targetId, ForecourtCommandResults.DISPENSER_DATA, ForecourtCommandMessageTypes.DISPENSER_DATA)
        {
            Dispensers = new List<IDispenserData>();
            if (dispData != null)
                Dispensers.AddRange(dispData);
        }

        public static DispenserDataResponse Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            //<CommandResp Code="DISPENSER_DATA" Result="SUCCESS">
            //  <DispenserData>
            //    <Dispenser ID="1" State="IDLE">
            //      <Nozzles>
            //        <Nozzle ID="1" State="IN" />
            //      </Nozzles>
            //    </Dispenser>
            //  </DispenserData>
            //</CommandResp>
            if (result == ForecourtCommandResults.SUCCESS)
            {
                var configNode = respNode.Element("DispenserData");
                if (configNode == null)
                    throw new XmlSchemaException("Command Response does not have <DispenserData> node");
                try
                {
                    var res = new DispenserDataResponse(seqNo, sourceId, targetId,
                        configNode.Descendants("Dispenser").Select(DispenserData.Parse).ToArray());

                    return res;
                }
                catch (Exception)
                {
                    return null;
                }
            }
            else
                return new DispenserDataResponse(seqNo, sourceId, targetId, new IDispenserData[0]);
        }
    }
}
