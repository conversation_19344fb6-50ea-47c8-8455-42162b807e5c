﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Triquestra.Common;
using Triquestra.Common.GUI;
using Triquestra.Common.GUI.POSThemes;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.POSForms;
using System.Linq;
using System.Drawing;
using System.Globalization;

namespace WinFormUi.Controls
{
    public partial class PrepayForm : Form
    {
        public PrepayForm()
        {
            InitializeComponent();
            snmNumpad.TargetControl = TxtAmount;
            SelectedBlend = null;
        }

        public void AddValueButton(OnScreenStateButton btnValue)
        {
            btnValue.KeyClick += btnValue_Click;
            PnlValues.Controls.Add(btnValue);
        }

        internal void btnValue_Click(object sender, EventArgs e)
        {
            if (!(sender is OnScreenStateButton))
            {
                MessageBox.Show("sender is not OnScreenStateButton");
                return;
            }
            TxtAmount.Focus();
            TxtAmount.Text = (sender as OnScreenStateButton).Tag.ToString();
            if (this.SelectedBlend != null)
                BtnOk.PerformClick();
        }

        public void AddBlends(IEnumerable<IForecourtBlend> blends)
        {
            if (blends != null)
            {
                var blendsArrCnt = blends.Count();                
                //var topLeft = new Point(15, 15);
                foreach (var blend in blends)
                {
                    var btn = new OnScreenStateButton();
                    // btn.Location = topLeft;
                    btn.Font = BtnOk.Font;
                    btn.Size = new Size(160,80);
                    btn.UpperCase = false;
                    btn.CaseChange = false;
                    btn.DisplayValue = blend.BlendName;
                    btn.Margin = new Padding(0);
                    btn.KeyClick += OnChooseBlend;
                    btn.Tag = blend;
                    pnlBlends.Controls.Add(btn);
                }
            }
        }

        internal void OnChooseBlend(object sender, EventArgs args)
        {
            foreach (OnScreenStateButton ctrl in pnlBlends.Controls.OfType<OnScreenStateButton>())
            {
                ctrl.Down = ctrl == sender;
                if (ctrl.Down)
                    SelectedBlend = (IForecourtBlend)ctrl.Tag;
            }
        }
        public IForecourtBlend SelectedBlend
        {
            get;
            private set;
        }

        public decimal Amount
        {
            get { return decimal.Parse(TxtAmount.Text,NumberStyles.Float,CultureInfo.CurrentUICulture); }
        }

        public string PumpTitle
        {
            set { lblHeader.Text = value; }
        }

        private void PrepayForm_Shown(object sender, EventArgs e)
        {
            if (this.DesignMode)
                return;

			if (Global.Database != null && Global.Database.Connection != null)
			{
				ThemeManager.ApplyThemeToForm(this, POSThemeFormType.Standard);
				// M155552 DWP Set the OnScreenStateButton colours (Due to changes in GUI dll)
				foreach(var blend in pnlBlends.Controls.OfType<OnScreenStateButton>())
				{
					blend.ForeColor = ThemeManager.GetElementColor(POSThemeElement.FontColor);
					blend.BackColor = ThemeManager.GetElementColor(POSThemeElement.FaceSale);
				}
			}
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        internal void BtnOk_Click(object sender, EventArgs e)
        {

            if (SelectedBlend == null)
            {
                Dialogs.ShowMessage(this.GuiMode, "Please select grade", "Infinity Point of Sale", MessageBoxButtons.OK, MessageBoxIcon.Error, true);
                return;
            }
            decimal ddummy;
            if (!decimal.TryParse(TxtAmount.Text, NumberStyles.Float, CultureInfo.CurrentUICulture, out ddummy))
            {
                Dialogs.ShowMessage(this.GuiMode, "Invalid numeric value", "Infinity Point of Sale", MessageBoxButtons.OK, MessageBoxIcon.Error, true);
                return;
            }

            //TODO: the 'OK' actions should be done via Action<IPrepay> delegate
            var mf = this.MainForm;
            if (mf != null)
            {
                mf.ApplyPrepay(new Prepay
                {
                    Blend = this.SelectedBlend,
                    Amount = this.Amount,
                    DispenserId = this.DispenserId
                });                
            }

            DialogResult = System.Windows.Forms.DialogResult.OK;
            this.Close();
        }

        public Mode GuiMode { get; private set; }

        internal void SetGuiMode(Mode mode)
        {
            GuiMode = mode;         
        }
        
        internal void AddDenom(List<int> list)
        {
            var valCnt = list.Count;
            for (int idx = 0; idx < Math.Min(valCnt, 8); idx++)
            {
                var denomination = list[idx];
                var btnValue = new OnScreenStateButton
                {
                    DisplayValue = "$" + denomination,
                    Tag = denomination,
                    Size = new Size(80, 80)
                };
                this.AddValueButton(btnValue);
            }
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }
        
        public void SetDispenser(int dispenserId)
        {
            this.DispenserId = dispenserId;
            //Text = @"Prepay on Pump " + dispenserId;
            PumpTitle = @"Prepay on Pump " + dispenserId;
        }

        public int DispenserId { get; set; }

        private void PrepayForm_FormClosed(object sender, FormClosedEventArgs e)
        {            
             var mf = this.MainForm;
             if (mf != null)
             {                 
                 mf.PrepayForm = null;//TODO: detach itself from the main form... Not ideal.
                 mf.ReturnFocus();
             }
        }

        public Triquestra.Common.PumpEsm.PumpInfoPosFooter MainForm { get; set; }
    }
}
