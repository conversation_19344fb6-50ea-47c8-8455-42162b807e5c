﻿using System;
using System.Text;

namespace DomsIntegration.Core.Utilities
{
    /// <summary>
    /// Helper methods for message processing
    /// </summary>
    public static class MessageHelper
    {
        /// <summary>
        /// Converts a hex string to bytes
        /// </summary>
        /// <param name="hex">Hex string</param>
        /// <returns>Byte array</returns>
        public static byte[] HexStringToBytes(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return new byte[0];

            if (hex.EndsWith("H", StringComparison.OrdinalIgnoreCase))
                hex = hex.Substring(0, hex.Length - 1);

            var bytes = new byte[hex.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hex.Substring(i * 2, 2), 16);
            }
            return bytes;
        }

        /// <summary>
        /// Converts bytes to hex string
        /// </summary>
        /// <param name="bytes">Byte array</param>
        /// <param name="addHSuffix">Whether to add 'H' suffix</param>
        /// <returns>Hex string</returns>
        public static string BytesToHexString(byte[] bytes, bool addHSuffix = true)
        {
            if (bytes == null || bytes.Length == 0)
                return string.Empty;

            var sb = new StringBuilder();
            foreach (byte b in bytes)
            {
                sb.AppendFormat("{0:X2}", b);
            }

            return addHSuffix ? sb.ToString() + "H" : sb.ToString();
        }

        /// <summary>
        /// Validates a message name format
        /// </summary>
        /// <param name="messageName">Message name to validate</param>
        /// <returns>True if valid</returns>
        public static bool IsValidMessageName(string messageName)
        {
            return !string.IsNullOrEmpty(messageName) &&
                   (messageName.EndsWith("_req") || messageName.EndsWith("_resp"));
        }

        /// <summary>
        /// Gets the response message name for a request
        /// </summary>
        /// <param name="requestName">Request message name</param>
        /// <returns>Response message name</returns>
        public static string GetResponseName(string requestName)
        {
            if (string.IsNullOrEmpty(requestName))
                throw new ArgumentNullException(nameof(requestName));

            if (requestName.EndsWith("_req"))
                return requestName.Replace("_req", "_resp");

            throw new ArgumentException("Invalid request message name", nameof(requestName));
        }
    }
}
