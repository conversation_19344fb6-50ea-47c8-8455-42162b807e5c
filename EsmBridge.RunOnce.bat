@echo off
cls
setlocal enabledelayedexpansion
set esm=pumpRegulus.dll
set bat=EsmBridge.RunOnce.bat
set x01=Regulus.Engine.dll
set x02=Regulus.Interface.dll

if exist %cd%\%esm%.dll (
	for /f %%j in ('dir /o/b/a:-d') do (
		set tgt=%%j
		set zap=1
		
		for %%s in (%esm%.dll %bat% %x01% %x02%) do (
			if %%s==!tgt! (
				set zap=0
			)
		)
		
		if !zap! equ 1 (
			if exist !tgt! (
				del /F !tgt!
			)
		)
	)
)