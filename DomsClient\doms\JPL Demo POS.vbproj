﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{246EDAB9-DB83-4E09-B74E-8EEC0B348517}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>JPL_Demo_POS.My.MyApplication</StartupObject>
    <RootNamespace>JPL_Demo_POS</RootNamespace>
    <AssemblyName>JPL Demo POS</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.12.0.3\lib\net40\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="clsForecourt.vb" />
    <Compile Include="clsForecourtComm.vb" />
    <Compile Include="DevicesDataSet.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DevicesDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DomsPosClasses.vb" />
    <Compile Include="frmJplConsole.Designer.vb">
      <DependentUpon>frmJplConsole.vb</DependentUpon>
    </Compile>
    <Compile Include="frmJplConsole.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmJplMessages.Designer.vb">
      <DependentUpon>frmJplMessages.vb</DependentUpon>
    </Compile>
    <Compile Include="frmJplMessages.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMain.Designer.vb">
      <DependentUpon>frmMain.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmOptions.Designer.vb">
      <DependentUpon>frmOptions.vb</DependentUpon>
    </Compile>
    <Compile Include="frmOptions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPrices.Designer.vb">
      <DependentUpon>frmPrices.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPrices.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTotals.Designer.vb">
      <DependentUpon>frmTotals.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTotals.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmJplConsole.resx">
      <DependentUpon>frmJplConsole.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmJplMessages.resx">
      <DependentUpon>frmJplMessages.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMain.resx">
      <DependentUpon>frmMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOptions.resx">
      <DependentUpon>frmOptions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPrices.resx">
      <DependentUpon>frmPrices.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTotals.resx">
      <DependentUpon>frmTotals.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="DevicesDataSet.xsc">
      <DependentUpon>DevicesDataSet.xsd</DependentUpon>
    </None>
    <None Include="DevicesDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DevicesDataSet.Designer.vb</LastGenOutput>
    </None>
    <None Include="DevicesDataSet.xss">
      <DependentUpon>DevicesDataSet.xsd</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="coins.bmp" />
    <Content Include="fc.ico" />
    <Content Include="gaspump.ico" />
    <Content Include="offline.ico" />
    <Content Include="online.ico" />
    <Content Include="tg.ico" />
    <Content Include="tgdata.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>