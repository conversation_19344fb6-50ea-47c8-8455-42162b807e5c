﻿using System;
using System.Collections.Generic;

namespace DomsIntegration.Models
{
    /// <summary>
    /// Base interface for all DOMs messages
    /// </summary>
    public interface IDomsMessage
    {
        /// <summary>
        /// Message type identifier
        /// </summary>
        string MessageType { get; }

        /// <summary>
        /// Message identifier for correlation
        /// </summary>
        string MessageId { get; set; }

        /// <summary>
        /// Timestamp when message was created
        /// </summary>
        DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// DOMs controller endpoint configuration
    /// </summary>
    public class DomsEndpoint
    {
        /// <summary>
        /// Host address
        /// </summary>
        public string Host { get; set; }

        /// <summary>
        /// Port number
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// Use SSL/TLS encryption
        /// </summary>
        public bool UseSsl { get; set; }

        /// <summary>
        /// Connection timeout in milliseconds
        /// </summary>
        public int TimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// Connection status changed event arguments
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; }

        public ConnectionStatusChangedEventArgs(bool isConnected)
        {
            IsConnected = isConnected;
        }
    }

    /// <summary>
    /// Message received event arguments
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public string MessageJson { get; }

        public MessageReceivedEventArgs(string messageJson)
        {
            MessageJson = messageJson;
        }
    }

    /// <summary>
    /// Base DOMs message implementation
    /// </summary>
    public abstract class DomsMessageBase : IDomsMessage
    {
        /// <inheritdoc />
        public abstract string MessageType { get; }

        /// <inheritdoc />
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        /// <inheritdoc />
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Logon request message
    /// </summary>
    public class LogonRequest : DomsMessageBase
    {
        public override string MessageType => "LogonRequest";

        /// <summary>
        /// Username for authentication
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Password for authentication
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// Client application identifier
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// Client version
        /// </summary>
        public string ClientVersion { get; set; }
    }

    /// <summary>
    /// Logon response message
    /// </summary>
    public class LogonResponse : DomsMessageBase
    {
        public override string MessageType => "LogonResponse";

        /// <summary>
        /// Indicates if logon was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Session token for subsequent requests
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Error message if logon failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Session expiry time
        /// </summary>
        public DateTime? ExpiryTime { get; set; }
    }

    /// <summary>
    /// Forecourt status request
    /// </summary>
    public class ForecourtStatusRequest : DomsMessageBase
    {
        public override string MessageType => "ForecourtStatusRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }
    }

    /// <summary>
    /// Forecourt status response
    /// </summary>
    public class ForecourtStatusResponse : DomsMessageBase
    {
        public override string MessageType => "ForecourtStatusResponse";

        /// <summary>
        /// List of dispensers and their status
        /// </summary>
        public List<DispenserStatus> Dispensers { get; set; } = new List<DispenserStatus>();

        /// <summary>
        /// Overall forecourt status
        /// </summary>
        public ForecourtState Status { get; set; }
    }

    /// <summary>
    /// Individual dispenser status
    /// </summary>
    public class DispenserStatus
    {
        /// <summary>
        /// Dispenser identifier
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Current dispenser state
        /// </summary>
        public DispenserState State { get; set; }

        /// <summary>
        /// Nozzles on this dispenser
        /// </summary>
        public List<NozzleStatus> Nozzles { get; set; } = new List<NozzleStatus>();

        /// <summary>
        /// Current transaction if any
        /// </summary>
        public TransactionInfo CurrentTransaction { get; set; }
    }

    /// <summary>
    /// Nozzle status information
    /// </summary>
    public class NozzleStatus
    {
        /// <summary>
        /// Nozzle identifier
        /// </summary>
        public int NozzleId { get; set; }

        /// <summary>
        /// Current nozzle state
        /// </summary>
        public NozzleState State { get; set; }

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Product { get; set; }

        /// <summary>
        /// Current price per unit
        /// </summary>
        public decimal Price { get; set; }
    }

    /// <summary>
    /// Transaction information
    /// </summary>
    public class TransactionInfo
    {
        /// <summary>
        /// Transaction identifier
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Volume dispensed
        /// </summary>
        public decimal Volume { get; set; }

        /// <summary>
        /// Amount charged
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Price per unit
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Product dispensed
        /// </summary>
        public ProductType Product { get; set; }

        /// <summary>
        /// Transaction start time
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// Authorize dispense request
    /// </summary>
    public class AuthorizeDispenseRequest : DomsMessageBase
    {
        public override string MessageType => "AuthorizeDispenseRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Dispenser to authorize
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Nozzle to authorize
        /// </summary>
        public int NozzleId { get; set; }

        /// <summary>
        /// Maximum amount to authorize
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// Maximum volume to authorize
        /// </summary>
        public decimal? MaxVolume { get; set; }

        /// <summary>
        /// Price override
        /// </summary>
        public decimal? PriceOverride { get; set; }
    }

    /// <summary>
    /// Authorize dispense response
    /// </summary>
    public class AuthorizeDispenseResponse : DomsMessageBase
    {
        public override string MessageType => "AuthorizeDispenseResponse";

        /// <summary>
        /// Indicates if authorization was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Authorization identifier
        /// </summary>
        public string AuthorizationId { get; set; }

        /// <summary>
        /// Error message if authorization failed
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Stop dispense request
    /// </summary>
    public class StopDispenseRequest : DomsMessageBase
    {
        public override string MessageType => "StopDispenseRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Dispenser to stop
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Authorization ID to stop
        /// </summary>
        public string AuthorizationId { get; set; }
    }

    /// <summary>
    /// Stop dispense response
    /// </summary>
    public class StopDispenseResponse : DomsMessageBase
    {
        public override string MessageType => "StopDispenseResponse";

        /// <summary>
        /// Indicates if stop was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Final transaction details
        /// </summary>
        public TransactionInfo FinalTransaction { get; set; }

        /// <summary>
        /// Error message if stop failed
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Forecourt state enumeration
    /// </summary>
    public enum ForecourtState
    {
        Unknown = 0,
        Normal = 1,
        Emergency = 2,
        Maintenance = 3,
        Offline = 4
    }

    /// <summary>
    /// Dispenser state enumeration
    /// </summary>
    public enum DispenserState
    {
        Unknown = 0,
        Idle = 1,
        Authorized = 2,
        Dispensing = 3,
        Stopped = 4,
        OutOfOrder = 5,
        Emergency = 6
    }

    /// <summary>
    /// Nozzle state enumeration
    /// </summary>
    public enum NozzleState
    {
        Unknown = 0,
        Idle = 1,
        Authorized = 2,
        Lifted = 3,
        Dispensing = 4,
        Stopped = 5,
        OutOfOrder = 6
    }

    /// <summary>
    /// Product type enumeration
    /// </summary>
    public enum ProductType
    {
        Unknown = 0,
        Gasoline87 = 1,
        Gasoline89 = 2,
        Gasoline91 = 3,
        Gasoline93 = 4,
        Diesel = 5,
        Premium = 6,
        Kerosene = 7
    }
}
