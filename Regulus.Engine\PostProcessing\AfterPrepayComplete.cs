﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.Types;
using System.Threading;
using Triquestra.CloudFoundation.Framework;
using Cf = Triquestra.CloudFoundation.Framework.Entities;
using Triquestra.Common;
using Triquestra.Common.Database;

namespace Triquestra.Common.PumpEsm.PostProcessing
{
    class AfterPrepayComplete
    {        
        private static NLog.Logger _logger = NLog.LogManager.GetCurrentClassLogger();
        private string _connectionString;
        private PetrolSaleItem _item;
        private string _receipt;
        private int _station;
        private string _masterConnString;

        private AfterPrepayComplete(string thisConnStr, string masterConnStr, string receipt, int station, PetrolSaleItem item)
        {
            _connectionString = thisConnStr;
            _masterConnString = masterConnStr;
            _receipt = receipt;
            _item = item;
            _station = station;
        }

        public static void PostPrepayTransaction(string receipt, PetrolSaleItem item)
        {
            string masterConnStr ;
            string localConnStr = masterConnStr = Global.Database.ConnectionString;
            if (Global.Database.DatabaseType==DatabaseType.Station)
            {
                masterConnStr = Global.Database.MasterDB.ConnectionString;
            }

            int station = Global.Station;

            var ctrl = new AfterPrepayComplete(localConnStr,masterConnStr, receipt, station,item);
            var thread = new Thread(new ThreadStart(ctrl.DoEvent));
            thread.Start();
        }

        private void DoEvent()
        {
            try
            {
                using (var conn = new DisposableConnectionHandler(_masterConnString))
                {
                    SavePrepayTransaction(conn);   
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString() + "The BOS is not available. Attempting to save into the local database.");
                try
                {
                    if (_connectionString != _masterConnString)
                        using (var conn = new DisposableConnectionHandler(_connectionString))
                        {
                            SavePrepayTransaction(conn);
                        }
                }
                catch (Exception ex1)
                {
                    _logger.Error(ex1.ToString() + "The data has not been saved");                    
                }
            }
        }

        private void SavePrepayTransaction(ConnectionHandler conn)
        {
            var pumpStr = "Pump#" + _item.DispenserId;
            var pumpToSave = Cf.ESMDataLocal.GetList(conn, Consts.ESMDATALOCAL_GROUP, pumpStr)
                .ToList()
                .FirstOrDefault(fod => fod.SeqNo == Consts.ESMDATALOCAL_SEQ_PREPAY)
                ?? new Cf.ESMDataLocal()
                {
                    GroupId = Consts.ESMDATALOCAL_GROUP,
                    SeqNo = Consts.ESMDATALOCAL_SEQ_PREPAY,
                    Code = pumpStr
                };

            pumpToSave.Description1 = _receipt;
            pumpToSave.Description2 = _item.UPC;
            pumpToSave.RefVal1 = Convert.ToInt32(_item.Amount * 100m);
            pumpToSave.SaveToDatabase(conn);
        }
    }
}
