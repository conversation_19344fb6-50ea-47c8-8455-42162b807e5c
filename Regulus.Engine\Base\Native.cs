﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Linq;
using System.Runtime.InteropServices;

namespace Triquestra.Common.PumpEsm.Base
{
    internal static class Native
    {

        // these are apparantly not defined anywhere in the .net framework
        // http://msdn.microsoft.com/en-us/library/system.windows.forms.message.msg.aspx

        public const uint WM_APP = 0x8000;
        public const int WM_MOUSEMOVE = 0x200;
        public const int WM_KEYDOWN = 0x100;
        public const int WM_KEYUP = 0x101;
        public const int VK_RETURN = 13;

        private delegate bool EnumThreadDelegate(IntPtr hWnd, IntPtr lParam);

        [DllImport("kernel32.DLL")]
        public static extern int GetPrivateProfileInt(string appName, string keyName, int defaultValue, string fileName);

        [DllImport("user32.dll")]
        private static extern bool EnumThreadWindows(int dwThreadId, EnumThreadDelegate lpfn, IntPtr lParam);

        [DllImport("user32.dll")]
        public static extern int SendMessage(int hWnd, uint Msg, int wParam, int lParam);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int SetWindowText(IntPtr hWnd, StringBuilder lpString);
        [DllImport("user32.dll", SetLastError = true)]
        public static extern int PostMessage(IntPtr hWnd, uint Msg, int wParam, int lParam);

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        public static extern bool SetWindowText(IntPtr hwnd, String lpString);

        [DllImport("user32.dll", SetLastError = true)]
        public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

        [DllImport("kernel32", SetLastError = true, CharSet = CharSet.Unicode)]
        public static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32", CharSet = CharSet.Ansi, ExactSpelling = true, SetLastError = true)]
        public static extern IntPtr GetProcAddress(IntPtr hModule, string procName);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool FreeLibrary(IntPtr hModule);

        public static IEnumerable<IntPtr> EnumerateProcessWindowHandles(int processId)
        {
            var handles = new List<IntPtr>();
            foreach (ProcessThread thread in Process.GetProcessById(processId).Threads)
            {
                EnumThreadWindows(thread.Id, (hWnd, lParam) =>
                {
                    handles.Add(hWnd);
                    return true;
                }, IntPtr.Zero);
            }
            return handles;
        }

        public static IntPtr FindPOSWindow()
        {
            var windows = EnumerateProcessWindowHandles(Process.GetProcessesByName("p1")[0].Id);
            foreach (var window in windows)
            {
                var sbText = new StringBuilder(256);
                GetWindowText(window, sbText, sbText.Capacity);
                if (sbText.ToString().StartsWith("Infinity POS"))
                {
                    var sbClass = new StringBuilder(256);
                    GetClassName(window, sbClass, sbClass.Capacity);
                    if (sbClass.ToString().StartsWith("TFormAKPPOS"))
                    { // yes, "AKPPOS" with the double P
                        return window;
                    }
                }
            }
            return IntPtr.Zero;
        }
    }
}
