﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Reflection.Emit;
using Triquestra.Common.PumpEsm;

namespace pumpEmulator.Tests
{
    [TestClass]
    public class PumpEmulatorEsm_TestClass
    {
        [TestMethod]
        public void PumpEmulatorEsm_TestClass_TQA()
        {
            var esm = new PumpEmulatorEsm();

            string message = @"<pumpesmrequest> DllType>DllPump</DllType><action>notify</action><message>N=1</message></pumpesmrequest>";

            var actual = esm.PerformESMAction(message);
        }
    }
}
