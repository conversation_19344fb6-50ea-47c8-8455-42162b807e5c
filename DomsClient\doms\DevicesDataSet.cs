using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JPL_Demo_POS_CSharp
{
    public class DevicesDataSet
    {
        public DevicesDataTable Devices { get; private set; }

        public DevicesDataSet()
        {
            Devices = new DevicesDataTable();
        }

        public class DevicesDataTable
        {
            private List<DevicesRow> _rows = new List<DevicesRow>();

            public List<DevicesRow> Rows => _rows;

            public DevicesRow FindByDeviceID(int deviceId)
            {
                return _rows.FirstOrDefault(r => r.DeviceID == deviceId);
            }

            public DevicesRow AddDevicesRow(int deviceId, bool configured)
            {
                var row = new DevicesRow(deviceId, configured);
                _rows.Add(row);
                return row;
            }

            public void AcceptChanges()
            {
                // In a real DataSet, this would commit changes
                // For our simple implementation, we don't need to do anything
            }
        }

        public class DevicesRow
        {
            public int DeviceID { get; set; }
            public bool Configured { get; set; }

            public DevicesRow(int deviceId, bool configured)
            {
                DeviceID = deviceId;
                Configured = configured;
            }
        }
    }
}
