﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace Triquestra.Common.PumpEsm.Helpers
{
    class MediaHelper
    {        
        /// SND_APPLICATION -> 0x0080
        const int SND_APPLICATION = 128;
        /// SND_ALIAS_START -> 0
        const int SND_ALIAS_START = 0;
        /// SND_RESOURCE -> 0x00040004L
        const int SND_RESOURCE = 262148;
        /// SND_FILENAME -> 0x00020000L
        const int SND_FILENAME = 131072;
        /// SND_ALIAS_ID -> 0x00110000L
        const int SND_ALIAS_ID = 1114112;
        /// SND_NOWAIT -> 0x00002000L
        const int SND_NOWAIT = 8192;
        /// SND_NOSTOP -> 0x0010
        const int SND_NOSTOP = 16;
        /// SND_MEMORY -> 0x0004
        const int SND_MEMORY = 4;
        /// SND_PURGE -> 0x0040
        const int SND_PURGE = 64;
        /// SND_ASYNC -> 0x0001
        const int SND_ASYNC = 1;
        /// SND_ALIAS -> 0x00010000L
        const int SND_ALIAS = 65536;
        /// SND_SYNC -> 0x0000
        const int SND_SYNC = 0;
        /// SND_LOOP -> 0x0008
        const int SND_LOOP = 8;
        /// SND_NODEFAULT -> 0x0002
        const int SND_NODEFAULT = 2;
        // SND_SYSTEM -> 0x00200000
        const int SND_SYSTEM = 0x00200000;

        [DllImport("winmm.dll", SetLastError = true)]
        static extern bool PlaySound(string pszSound, UIntPtr hmod, uint fdwSound);

        public static void PlayNotificationSound()
        {
            //read the value of HKCU\AppEvents\Schemes\Apps\.Default\FaxBeep\.Current and play
            PlaySound(@"FaxBeep", UIntPtr.Zero, SND_SYSTEM | SND_NODEFAULT | SND_ALIAS | SND_ASYNC);
            //should I fall back to System.Media.SystemSounds.Exclamation.Play(); when no sound were referenced?
        }
    }
}
