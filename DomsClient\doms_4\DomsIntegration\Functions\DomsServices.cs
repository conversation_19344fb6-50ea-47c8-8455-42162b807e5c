﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Communication;
using DomsIntegration.Models;
using DomsIntegration.Authentication;
using DomsIntegration.Exceptions;

namespace DomsIntegration.Functions
{
    /// <summary>
    /// Interface for general forecourt controller functions
    /// </summary>
    public interface IForecourtService
    {
        /// <summary>
        /// Gets the current status of all dispensers
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Forecourt status information</returns>
        Task<ForecourtStatusResponse> GetForecourtStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of a specific dispenser
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dispenser status information</returns>
        Task<DispenserStatus> GetDispenserStatusAsync(int dispenserId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets emergency stop for the entire forecourt
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if emergency stop was successful</returns>
        Task<bool> EmergencyStopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears emergency stop for the forecourt
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if emergency clear was successful</returns>
        Task<bool> ClearEmergencyAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Interface for dispense control functions
    /// </summary>
    public interface IDispenseService
    {
        /// <summary>
        /// Authorizes dispensing for a specific nozzle
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="nozzleId">Nozzle identifier</param>
        /// <param name="maxAmount">Maximum amount to authorize</param>
        /// <param name="maxVolume">Maximum volume to authorize</param>
        /// <param name="priceOverride">Price override if applicable</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authorization response</returns>
        Task<AuthorizeDispenseResponse> AuthorizeDispenseAsync(
            int dispenserId, 
            int nozzleId, 
            decimal? maxAmount = null, 
            decimal? maxVolume = null, 
            decimal? priceOverride = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops dispensing for a specific authorization
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="authorizationId">Authorization identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Stop dispense response</returns>
        Task<StopDispenseResponse> StopDispenseAsync(
            int dispenserId, 
            string authorizationId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Suspends dispensing (temporary stop)
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if suspend was successful</returns>
        Task<bool> SuspendDispenseAsync(int dispenserId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Resumes suspended dispensing
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if resume was successful</returns>
        Task<bool> ResumeDispenseAsync(int dispenserId, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Implementation of forecourt service
    /// </summary>
    public class ForecourtService : IForecourtService
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ForecourtService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        /// <param name="authService">Authentication service</param>
        public ForecourtService(IDomsClient client, IDomsAuthenticationService authService)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        /// <inheritdoc />
        public async Task<ForecourtStatusResponse> GetForecourtStatusAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            var request = new ForecourtStatusRequest
            {
                SessionToken = _authService.SessionToken
            };

            return await _client.SendMessageAsync<ForecourtStatusResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<DispenserStatus> GetDispenserStatusAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            var forecourtStatus = await GetForecourtStatusAsync(cancellationToken);
            
            var dispenser = forecourtStatus.Dispensers.Find(d => d.DispenserId == dispenserId);
            if (dispenser == null)
                throw new DomsOperationException($"Dispenser {dispenserId} not found");

            return dispenser;
        }

        /// <inheritdoc />
        public async Task<bool> EmergencyStopAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send emergency stop message
            return await Task.FromResult(true);
        }

        /// <inheritdoc />
        public async Task<bool> ClearEmergencyAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send clear emergency message
            return await Task.FromResult(true);
        }

        private void EnsureAuthenticated()
        {
            if (!_authService.IsAuthenticated)
                throw new DomsAuthenticationException("Not authenticated. Please login first.");
        }
    }

    /// <summary>
    /// Implementation of dispense service
    /// </summary>
    public class DispenseService : IDispenseService
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DispenseService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        /// <param name="authService">Authentication service</param>
        public DispenseService(IDomsClient client, IDomsAuthenticationService authService)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        /// <inheritdoc />
        public async Task<AuthorizeDispenseResponse> AuthorizeDispenseAsync(
            int dispenserId, 
            int nozzleId, 
            decimal? maxAmount = null, 
            decimal? maxVolume = null, 
            decimal? priceOverride = null,
            CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            var request = new AuthorizeDispenseRequest
            {
                SessionToken = _authService.SessionToken,
                DispenserId = dispenserId,
                NozzleId = nozzleId,
                MaxAmount = maxAmount,
                MaxVolume = maxVolume,
                PriceOverride = priceOverride
            };

            return await _client.SendMessageAsync<AuthorizeDispenseResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<StopDispenseResponse> StopDispenseAsync(
            int dispenserId, 
            string authorizationId, 
            CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            if (string.IsNullOrEmpty(authorizationId))
                throw new ArgumentException("Authorization ID cannot be null or empty", nameof(authorizationId));

            var request = new StopDispenseRequest
            {
                SessionToken = _authService.SessionToken,
                DispenserId = dispenserId,
                AuthorizationId = authorizationId
            };

            return await _client.SendMessageAsync<StopDispenseResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<bool> SuspendDispenseAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send suspend message
            return await Task.FromResult(true);
        }

        /// <inheritdoc />
        public async Task<bool> ResumeDispenseAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send resume message
            return await Task.FromResult(true);
        }

        private void EnsureAuthenticated()
        {
            if (!_authService.IsAuthenticated)
                throw new DomsAuthenticationException("Not authenticated. Please login first.");
        }
    }
}
