﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Communication;
using DomsIntegration.Models;
using DomsIntegration.Exceptions;

namespace DomsIntegration.Authentication
{
    /// <summary>
    /// Interface for DOMs authentication operations
    /// </summary>
    public interface IDomsAuthenticationService
    {
        /// <summary>
        /// Current session token
        /// </summary>
        string SessionToken { get; }

        /// <summary>
        /// Indicates if currently authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Session expiry time
        /// </summary>
        DateTime? SessionExpiry { get; }

        /// <summary>
        /// Authenticates with the DOMs controller
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="clientId">Client identifier</param>
        /// <param name="clientVersion">Client version</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authentication result</returns>
        Task<bool> AuthenticateAsync(string username, string password, string clientId, string clientVersion, CancellationToken cancellationToken = default);

        /// <summary>
        /// Logs out from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the logout operation</returns>
        Task LogoutAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Refreshes the current session
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if refresh was successful</returns>
        Task<bool> RefreshSessionAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Implementation of DOMs authentication service
    /// </summary>
    public class DomsAuthenticationService : IDomsAuthenticationService
    {
        private readonly IDomsClient _client;
        private string _sessionToken;
        private DateTime? _sessionExpiry;

        /// <inheritdoc />
        public string SessionToken => _sessionToken;

        /// <inheritdoc />
        public bool IsAuthenticated => !string.IsNullOrEmpty(_sessionToken) && 
                                     (_sessionExpiry == null || _sessionExpiry > DateTime.UtcNow);

        /// <inheritdoc />
        public DateTime? SessionExpiry => _sessionExpiry;

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        public DomsAuthenticationService(IDomsClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        /// <inheritdoc />
        public async Task<bool> AuthenticateAsync(string username, string password, string clientId, string clientVersion, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(username))
                throw new ArgumentException("Username cannot be null or empty", nameof(username));
            
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            var request = new LogonRequest
            {
                Username = username,
                Password = password,
                ClientId = clientId ?? "DomsClientLibrary",
                ClientVersion = clientVersion ?? "1.0.0"
            };

            try
            {
                var response = await _client.SendMessageAsync<LogonResponse>(request, cancellationToken);
                
                if (response.Success)
                {
                    _sessionToken = response.SessionToken;
                    _sessionExpiry = response.ExpiryTime;
                    return true;
                }
                else
                {
                    throw new DomsAuthenticationException(response.ErrorMessage ?? "Authentication failed");
                }
            }
            catch (DomsAuthenticationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new DomsAuthenticationException("Authentication failed due to communication error", ex);
            }
        }

        /// <inheritdoc />
        public async Task LogoutAsync(CancellationToken cancellationToken = default)
        {
            // Implementation would send logout message
            _sessionToken = null;
            _sessionExpiry = null;
            
            // In a real implementation, you would send a logout message to the server
            await Task.CompletedTask;
        }

        /// <inheritdoc />
        public async Task<bool> RefreshSessionAsync(CancellationToken cancellationToken = default)
        {
            // Implementation would send session refresh message
            // For now, just return current authentication status
            return await Task.FromResult(IsAuthenticated);
        }
    }
}
