<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
    <metadata>
        <id>$id$</id>
        <version>$version$</version>
        <title>Triquestra.Common.Forecourt</title>
        <authors>Triquestra</authors>
        <owners>Triquestra International</owners>
        <requireLicenseAcceptance>false</requireLicenseAcceptance>
        <description>Triquestra.Common.Forecourt</description>
        <copyright>Copyright � Triquestra International 2023</copyright>
		</metadata>
    <files>      
      <file src="bin\Release\Triquestra.Common.PumpEsm.Comms.dll" target="lib\net48\Triquestra.Common.PumpEsm.Comms.dll" />
      <file src="bin\Release\Triquestra.Common.PumpEsm.Messaging.dll" target="lib\net48\Triquestra.Common.PumpEsm.Messaging.dll" />
      <file src="bin\Release\Triquestra.Common.PumpEsm.RegulusInterface.dll" target="lib\net48\Triquestra.Common.PumpEsm.RegulusInterface.dll" />
    </files>   
</package>