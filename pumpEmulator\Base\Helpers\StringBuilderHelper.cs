﻿using System;
using System.Text;

namespace Triquestra.Common.PumpEsm.Base.Helpers
{
    // this is an extension for Stringbuilder class

    // !!!! this class MUST BE removed, if it conflicts with the same class in Utils.dll. It is safe.
    /// <summary>
    /// This class extends the <see cref="System.Text.StringBuilder"/> class with the functionality similar to System.String class
    /// </summary>
    static class StringBuilderHelper
    {
        /// <summary>
        /// Reports the index of the first occurrence of the specified string in the current System.Text.StringBuilder object
        /// </summary>
        /// <param name="value">The System.String to seek</param>
        /// <returns>The zero-based index position of value if that string is found, or -1 if it is not. If value is Empty, the return value is 0.</returns>
        public static int IndexOf(this StringBuilder sb, string value)
        {
            return IndexOf(sb, value, 0, sb.Length);
        }

        /// <summary>
        /// Reports the index of the first occurrence of the specified string in the current System.Text.StringBuilder object
        /// </summary>
        /// <param name="value">The System.String to seek</param>
        /// <param name="startIndex">The search starting position</param>
        /// <returns>The zero-based index position of value if that string is found, or -1 if it is not. If value is Empty, the return value is 0.</returns>
        public static int IndexOf(this StringBuilder sb, string value, int startIndex)
        {
            return IndexOf(sb, value, startIndex, sb.Length - startIndex);
        }
        /// <summary>
        /// Reports the index of the first occurrence of the specified string in the current System.Text.StringBuilder object
        /// </summary>
        /// <param name="value">The System.String to seek</param>
        /// <param name="startIndex">The search starting position</param>
        /// <param name="count">The number of character positions to examine</param>
        /// <returns>The zero-based index position of value if that string is found, or -1 if it is not. If value is Empty, the return value is 0.</returns>
        public static int IndexOf(this StringBuilder sb, string value, int startIndex, int count)
        {
            // this algorithm uses Knuth–Morris–Pratt algorithm to improve the speed of search
            // http://en.wikipedia.org/wiki/Knuth–Morris–Pratt_algorithm

            if (sb == null)
                throw new NullReferenceException();

            if (value == null)
                throw new ArgumentNullException("value");

            if (startIndex < 0 || startIndex > sb.Length)
                throw new ArgumentOutOfRangeException("startIndex");

            if (count < 0 || count > sb.Length - startIndex)
                throw new ArgumentOutOfRangeException("count");

            //Contract.EndContractBlock();

            if (value.Length == 0)
                return 0;//empty strings are everywhere!

            int endIndex = startIndex + count;
            if (value.Length == 1)//can't beat just spinning through for it
            {
                char c = value[0];
                for (int idx = startIndex; idx != endIndex; ++idx)
                    if (sb[idx] == c)
                        return idx;
                return -1;
            }
            
            int m = startIndex;
            int i = 0;
            int[] kmpTable = _KMPTable(value);
            while (m + i < endIndex)
            {
                if (value[i] == sb[m + i])
                {
                    if (i == value.Length - 1)
                        return m == value.Length ? -1 : m;//match -1 = failure to find conventional in .NET
                    ++i;
                }
                else
                {
                    m = m + i - kmpTable[i];
                    i = kmpTable[i] > -1 ? kmpTable[i] : 0;
                }
            }
            /*
            int i = 0;
            int j = startIndex;
            int[] kmpNext = _KMPTable(value);
            while (j < endIndex-1)
            {
                while (i > -1 && value[i] != sb[j])
                    i = kmpNext[i];
                i++;
                j++;
                if (i >= value.Length)
                {
                    return (j - i);
                } else {
                    i = kmpNext[i];
                }
            }
            */
            return -1;
        }
        /// <summary>
        /// Knuth–Morris–Pratt table to improve the speed of search
        /// </summary>
        /// <param name="x">Search string</param>
        /// <returns>Preintialised table</returns>
        private static int[] _KMPTable(string sought)
        {
            int[] table = new int[sought.Length];
            int pos = 2;
            int cnd = 0;
            table[0] = -1;
            table[1] = 0;
            while (pos < table.Length)
                if (sought[pos - 1] == sought[cnd])
                    table[pos++] = ++cnd;
                else if (cnd > 0)
                    cnd = table[cnd];
                else
                    table[pos++] = 0;
            return table;
        }
        /*
        private static int[] _KMPTable(string x)
        {
            int i, j;
            int[] kmpNext = new int[x.Length];
            i = 0;
            j = kmpNext[0] = -1;
            while (i < x.Length)
            {
                while (j > -1 && x[i] != x[j])
                    j = kmpNext[j];
                i++;
                j++;
                if (x[i] == x[j])
                    kmpNext[i] = kmpNext[j];
                else
                    kmpNext[i] = j;
            }
            return kmpNext;
        }
         */ 
        /// <summary>
        /// Retrieves a substring from this instance. The substring starts at a specified character position and has a specified length.
        /// </summary>
        /// <param name="startIndex">The zero-based starting character position of a substring in this instance.</param>
        /// <param name="length">The number of characters in the substring.</param>
        /// <returns>A String equivalent to the substring of length length that begins at startIndex in this instance, or Empty if startIndex is equal to the length of this instance and length is zero</returns>
        public static string Substring(this StringBuilder sb, int startIndex, int length)
        {
            return sb.ToString(startIndex, length);
        }

        /// <summary>
        /// Retrieves a substring from this instance. The substring starts at a specified character position and has a specified length.
        /// </summary>
        /// <param name="startIndex">The zero-based starting character position of a substring in this instance.</param>
        /// <returns>A String equivalent to the substring of length length that begins at startIndex in this instance, or Empty if startIndex is equal to the length of this instance and length is zero</returns>
        public static string Substring(this StringBuilder sb, int startIndex)
        {
            return sb.ToString(startIndex, sb.Length - startIndex);
        }
    }
}
