-- SQL script to add JPL controller configuration to Fuel_Config table
-- This allows switching between standard Regulus controller and JPL controller

-- Check if the configuration already exists
IF NOT EXISTS (SELECT 1 FROM Fuel_Config WHERE Parameter = 'UseJplController')
BEGIN
    -- Add the JPL controller configuration parameter
    INSERT INTO Fuel_Config (Parameter, GroupID, Value, Description, ValueNotes, BVisible)
    VALUES (
        'UseJplController',
        0,
        'false',
        'Enable JPL-based pump controller instead of standard Regulus controller',
        'Set to "true" or "1" to use JPL controller, "false" or "0" for standard Regulus controller',
        1
    );
    
    PRINT 'JPL controller configuration added successfully.';
END
ELSE
BEGIN
    PRINT 'JPL controller configuration already exists.';
END

-- Display current configuration
SELECT Parameter, GroupID, Value, Description, ValueNotes, BVisible 
FROM Fuel_Config 
WHERE Parameter = 'UseJplController';

-- Example: To enable JPL controller, run:
-- UPDATE Fuel_Config SET Value = 'true' WHERE Parameter = 'UseJplController';

-- Example: To disable JPL controller (use standard Regulus), run:
-- UPDATE Fuel_Config SET Value = 'false' WHERE Parameter = 'UseJplController';
