﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmOptions
  Inherits System.Windows.Forms.Form

  'Form overrides dispose to clean up the component list.
  <System.Diagnostics.DebuggerNonUserCode()>
  Protected Overrides Sub Dispose(ByVal disposing As Boolean)
    Try
      If disposing AndAlso components IsNot Nothing Then
        components.Dispose()
      End If
    Finally
      MyBase.Dispose(disposing)
    End Try
  End Sub

  'Required by the Windows Form Designer
  Private components As System.ComponentModel.IContainer

  'NOTE: The following procedure is required by the Windows Form Designer
  'It can be modified using the Windows Form Designer.  
  'Do not modify it using the code editor.
  <System.Diagnostics.DebuggerStepThrough()>
  Private Sub InitializeComponent()
    Me.btnOk = New System.Windows.Forms.Button()
    Me.btnCancel = New System.Windows.Forms.Button()
    Me.Label1 = New System.Windows.Forms.Label()
    Me.Label2 = New System.Windows.Forms.Label()
    Me.GroupBox1 = New System.Windows.Forms.GroupBox()
    Me.udAmountDecPointPos = New System.Windows.Forms.NumericUpDown()
    Me.udPriceDecPointPos = New System.Windows.Forms.NumericUpDown()
    Me.udVolDecPointPos = New System.Windows.Forms.NumericUpDown()
    Me.Label5 = New System.Windows.Forms.Label()
    Me.Label4 = New System.Windows.Forms.Label()
    Me.Label3 = New System.Windows.Forms.Label()
    Me.txtFcLogonString = New System.Windows.Forms.TextBox()
    Me.txtIPAddress = New System.Windows.Forms.TextBox()
    Me.Label6 = New System.Windows.Forms.Label()
    Me.udPosId = New System.Windows.Forms.NumericUpDown()
    Me.cbClearDeliveryReports = New System.Windows.Forms.CheckBox()
    Me.GroupBox2 = New System.Windows.Forms.GroupBox()
    Me.cbShowUnsolicitedMessages = New System.Windows.Forms.CheckBox()
    Me.cbShowHeartbeatMessages = New System.Windows.Forms.CheckBox()
    Me.cbClearBackOfficeRecords = New System.Windows.Forms.CheckBox()
    Me.cbAutoLockTransactions = New System.Windows.Forms.CheckBox()
    Me.GroupBox1.SuspendLayout()
    CType(Me.udAmountDecPointPos, System.ComponentModel.ISupportInitialize).BeginInit()
    CType(Me.udPriceDecPointPos, System.ComponentModel.ISupportInitialize).BeginInit()
    CType(Me.udVolDecPointPos, System.ComponentModel.ISupportInitialize).BeginInit()
    CType(Me.udPosId, System.ComponentModel.ISupportInitialize).BeginInit()
    Me.GroupBox2.SuspendLayout()
    Me.SuspendLayout()
    '
    'btnOk
    '
    Me.btnOk.Location = New System.Drawing.Point(165, 280)
    Me.btnOk.Name = "btnOk"
    Me.btnOk.Size = New System.Drawing.Size(75, 23)
    Me.btnOk.TabIndex = 6
    Me.btnOk.Text = "&OK"
    Me.btnOk.UseVisualStyleBackColor = True
    '
    'btnCancel
    '
    Me.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
    Me.btnCancel.Location = New System.Drawing.Point(246, 280)
    Me.btnCancel.Name = "btnCancel"
    Me.btnCancel.Size = New System.Drawing.Size(75, 23)
    Me.btnCancel.TabIndex = 7
    Me.btnCancel.Text = "&Cancel"
    Me.btnCancel.UseVisualStyleBackColor = True
    '
    'Label1
    '
    Me.Label1.AutoSize = True
    Me.Label1.Location = New System.Drawing.Point(9, 12)
    Me.Label1.Name = "Label1"
    Me.Label1.Size = New System.Drawing.Size(85, 13)
    Me.Label1.TabIndex = 2
    Me.Label1.Text = "PSS IP Address:"
    '
    'Label2
    '
    Me.Label2.AutoSize = True
    Me.Label2.Location = New System.Drawing.Point(9, 61)
    Me.Label2.Name = "Label2"
    Me.Label2.Size = New System.Drawing.Size(70, 13)
    Me.Label2.TabIndex = 3
    Me.Label2.Text = "Logon String:"
    '
    'GroupBox1
    '
    Me.GroupBox1.Controls.Add(Me.udAmountDecPointPos)
    Me.GroupBox1.Controls.Add(Me.udPriceDecPointPos)
    Me.GroupBox1.Controls.Add(Me.udVolDecPointPos)
    Me.GroupBox1.Controls.Add(Me.Label5)
    Me.GroupBox1.Controls.Add(Me.Label4)
    Me.GroupBox1.Controls.Add(Me.Label3)
    Me.GroupBox1.Location = New System.Drawing.Point(12, 148)
    Me.GroupBox1.Name = "GroupBox1"
    Me.GroupBox1.Size = New System.Drawing.Size(462, 54)
    Me.GroupBox1.TabIndex = 2
    Me.GroupBox1.TabStop = False
    Me.GroupBox1.Text = "Decimal Point Position"
    '
    'udAmountDecPointPos
    '
    Me.udAmountDecPointPos.Location = New System.Drawing.Point(339, 23)
    Me.udAmountDecPointPos.Maximum = New Decimal(New Integer() {5, 0, 0, 0})
    Me.udAmountDecPointPos.Name = "udAmountDecPointPos"
    Me.udAmountDecPointPos.Size = New System.Drawing.Size(40, 20)
    Me.udAmountDecPointPos.TabIndex = 2
    Me.udAmountDecPointPos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Me.udAmountDecPointPos.Value = New Decimal(New Integer() {2, 0, 0, 0})
    '
    'udPriceDecPointPos
    '
    Me.udPriceDecPointPos.Location = New System.Drawing.Point(237, 23)
    Me.udPriceDecPointPos.Maximum = New Decimal(New Integer() {5, 0, 0, 0})
    Me.udPriceDecPointPos.Name = "udPriceDecPointPos"
    Me.udPriceDecPointPos.Size = New System.Drawing.Size(40, 20)
    Me.udPriceDecPointPos.TabIndex = 1
    Me.udPriceDecPointPos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Me.udPriceDecPointPos.Value = New Decimal(New Integer() {2, 0, 0, 0})
    '
    'udVolDecPointPos
    '
    Me.udVolDecPointPos.Location = New System.Drawing.Point(139, 23)
    Me.udVolDecPointPos.Maximum = New Decimal(New Integer() {5, 0, 0, 0})
    Me.udVolDecPointPos.Name = "udVolDecPointPos"
    Me.udVolDecPointPos.Size = New System.Drawing.Size(40, 20)
    Me.udVolDecPointPos.TabIndex = 0
    Me.udVolDecPointPos.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Me.udVolDecPointPos.Value = New Decimal(New Integer() {2, 0, 0, 0})
    '
    'Label5
    '
    Me.Label5.AutoSize = True
    Me.Label5.Location = New System.Drawing.Point(83, 25)
    Me.Label5.Name = "Label5"
    Me.Label5.Size = New System.Drawing.Size(50, 13)
    Me.Label5.TabIndex = 0
    Me.Label5.Text = "Volumes:"
    '
    'Label4
    '
    Me.Label4.AutoSize = True
    Me.Label4.Location = New System.Drawing.Point(192, 25)
    Me.Label4.Name = "Label4"
    Me.Label4.Size = New System.Drawing.Size(39, 13)
    Me.Label4.TabIndex = 4
    Me.Label4.Text = "Prices:"
    '
    'Label3
    '
    Me.Label3.AutoSize = True
    Me.Label3.Location = New System.Drawing.Point(294, 25)
    Me.Label3.Name = "Label3"
    Me.Label3.Size = New System.Drawing.Size(42, 13)
    Me.Label3.TabIndex = 3
    Me.Label3.Text = "Money:"
    '
    'txtFcLogonString
    '
    Me.txtFcLogonString.Location = New System.Drawing.Point(12, 78)
    Me.txtFcLogonString.Name = "txtFcLogonString"
    Me.txtFcLogonString.Size = New System.Drawing.Size(462, 20)
    Me.txtFcLogonString.TabIndex = 2
    Me.txtFcLogonString.Text = "POS,APPL_ID=VXYZ12,RI"
    '
    'txtIPAddress
    '
    Me.txtIPAddress.Location = New System.Drawing.Point(12, 28)
    Me.txtIPAddress.Name = "txtIPAddress"
    Me.txtIPAddress.Size = New System.Drawing.Size(115, 20)
    Me.txtIPAddress.TabIndex = 0
    Me.txtIPAddress.Text = "*************"
    '
    'Label6
    '
    Me.Label6.AutoSize = True
    Me.Label6.Location = New System.Drawing.Point(143, 31)
    Me.Label6.Name = "Label6"
    Me.Label6.Size = New System.Drawing.Size(47, 13)
    Me.Label6.TabIndex = 5
    Me.Label6.Text = "POS Id.:"
    '
    'udPosId
    '
    Me.udPosId.Location = New System.Drawing.Point(196, 29)
    Me.udPosId.Maximum = New Decimal(New Integer() {99, 0, 0, 0})
    Me.udPosId.Minimum = New Decimal(New Integer() {1, 0, 0, 0})
    Me.udPosId.Name = "udPosId"
    Me.udPosId.Size = New System.Drawing.Size(40, 20)
    Me.udPosId.TabIndex = 1
    Me.udPosId.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Me.udPosId.Value = New Decimal(New Integer() {25, 0, 0, 0})
    '
    'cbClearDeliveryReports
    '
    Me.cbClearDeliveryReports.AutoSize = True
    Me.cbClearDeliveryReports.Checked = True
    Me.cbClearDeliveryReports.CheckState = System.Windows.Forms.CheckState.Checked
    Me.cbClearDeliveryReports.Location = New System.Drawing.Point(16, 114)
    Me.cbClearDeliveryReports.Name = "cbClearDeliveryReports"
    Me.cbClearDeliveryReports.Size = New System.Drawing.Size(156, 17)
    Me.cbClearDeliveryReports.TabIndex = 3
    Me.cbClearDeliveryReports.Text = "Clear ATG Delivery Reports"
    Me.cbClearDeliveryReports.UseVisualStyleBackColor = True
    '
    'GroupBox2
    '
    Me.GroupBox2.Controls.Add(Me.cbShowUnsolicitedMessages)
    Me.GroupBox2.Controls.Add(Me.cbShowHeartbeatMessages)
    Me.GroupBox2.Location = New System.Drawing.Point(12, 208)
    Me.GroupBox2.Name = "GroupBox2"
    Me.GroupBox2.Size = New System.Drawing.Size(462, 57)
    Me.GroupBox2.TabIndex = 8
    Me.GroupBox2.TabStop = False
    Me.GroupBox2.Text = "JPL Messages"
    '
    'cbShowUnsolicitedMessages
    '
    Me.cbShowUnsolicitedMessages.AutoSize = True
    Me.cbShowUnsolicitedMessages.Checked = True
    Me.cbShowUnsolicitedMessages.CheckState = System.Windows.Forms.CheckState.Checked
    Me.cbShowUnsolicitedMessages.Location = New System.Drawing.Point(50, 26)
    Me.cbShowUnsolicitedMessages.Name = "cbShowUnsolicitedMessages"
    Me.cbShowUnsolicitedMessages.Size = New System.Drawing.Size(159, 17)
    Me.cbShowUnsolicitedMessages.TabIndex = 0
    Me.cbShowUnsolicitedMessages.Text = "Show Unsolicited Messages"
    Me.cbShowUnsolicitedMessages.UseVisualStyleBackColor = True
    '
    'cbShowHeartbeatMessages
    '
    Me.cbShowHeartbeatMessages.AutoSize = True
    Me.cbShowHeartbeatMessages.Checked = True
    Me.cbShowHeartbeatMessages.CheckState = System.Windows.Forms.CheckState.Checked
    Me.cbShowHeartbeatMessages.Location = New System.Drawing.Point(259, 26)
    Me.cbShowHeartbeatMessages.Name = "cbShowHeartbeatMessages"
    Me.cbShowHeartbeatMessages.Size = New System.Drawing.Size(154, 17)
    Me.cbShowHeartbeatMessages.TabIndex = 1
    Me.cbShowHeartbeatMessages.Text = "Show Heartbeat Messages"
    Me.cbShowHeartbeatMessages.UseVisualStyleBackColor = True
    '
    'cbClearBackOfficeRecords
    '
    Me.cbClearBackOfficeRecords.AutoSize = True
    Me.cbClearBackOfficeRecords.Checked = True
    Me.cbClearBackOfficeRecords.CheckState = System.Windows.Forms.CheckState.Checked
    Me.cbClearBackOfficeRecords.Location = New System.Drawing.Point(178, 114)
    Me.cbClearBackOfficeRecords.Name = "cbClearBackOfficeRecords"
    Me.cbClearBackOfficeRecords.Size = New System.Drawing.Size(149, 17)
    Me.cbClearBackOfficeRecords.TabIndex = 4
    Me.cbClearBackOfficeRecords.Text = "Clear Back Office Reports"
    Me.cbClearBackOfficeRecords.UseVisualStyleBackColor = True
    '
    'cbAutoLockTransactions
    '
    Me.cbAutoLockTransactions.AutoSize = True
    Me.cbAutoLockTransactions.Checked = True
    Me.cbAutoLockTransactions.CheckState = System.Windows.Forms.CheckState.Checked
    Me.cbAutoLockTransactions.Location = New System.Drawing.Point(335, 114)
    Me.cbAutoLockTransactions.Name = "cbAutoLockTransactions"
    Me.cbAutoLockTransactions.Size = New System.Drawing.Size(139, 17)
    Me.cbAutoLockTransactions.TabIndex = 5
    Me.cbAutoLockTransactions.Text = "Auto Lock Transactions"
    Me.cbAutoLockTransactions.UseVisualStyleBackColor = True
    '
    'frmOptions
    '
    Me.AcceptButton = Me.btnOk
    Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
    Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
    Me.CancelButton = Me.btnCancel
    Me.ClientSize = New System.Drawing.Size(486, 318)
    Me.Controls.Add(Me.cbAutoLockTransactions)
    Me.Controls.Add(Me.cbClearBackOfficeRecords)
    Me.Controls.Add(Me.GroupBox2)
    Me.Controls.Add(Me.cbClearDeliveryReports)
    Me.Controls.Add(Me.udPosId)
    Me.Controls.Add(Me.Label6)
    Me.Controls.Add(Me.txtFcLogonString)
    Me.Controls.Add(Me.txtIPAddress)
    Me.Controls.Add(Me.GroupBox1)
    Me.Controls.Add(Me.Label2)
    Me.Controls.Add(Me.Label1)
    Me.Controls.Add(Me.btnCancel)
    Me.Controls.Add(Me.btnOk)
    Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
    Me.MaximizeBox = False
    Me.MinimizeBox = False
    Me.Name = "frmOptions"
    Me.Text = "Options"
    Me.GroupBox1.ResumeLayout(False)
    Me.GroupBox1.PerformLayout()
    CType(Me.udAmountDecPointPos, System.ComponentModel.ISupportInitialize).EndInit()
    CType(Me.udPriceDecPointPos, System.ComponentModel.ISupportInitialize).EndInit()
    CType(Me.udVolDecPointPos, System.ComponentModel.ISupportInitialize).EndInit()
    CType(Me.udPosId, System.ComponentModel.ISupportInitialize).EndInit()
    Me.GroupBox2.ResumeLayout(False)
    Me.GroupBox2.PerformLayout()
    Me.ResumeLayout(False)
    Me.PerformLayout()

  End Sub
  Friend WithEvents btnOk As System.Windows.Forms.Button
  Friend WithEvents btnCancel As System.Windows.Forms.Button
  Friend WithEvents Label1 As System.Windows.Forms.Label
  Friend WithEvents Label2 As System.Windows.Forms.Label
  Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
  Friend WithEvents udAmountDecPointPos As System.Windows.Forms.NumericUpDown
  Friend WithEvents udPriceDecPointPos As System.Windows.Forms.NumericUpDown
  Friend WithEvents udVolDecPointPos As System.Windows.Forms.NumericUpDown
  Friend WithEvents Label5 As System.Windows.Forms.Label
  Friend WithEvents Label4 As System.Windows.Forms.Label
  Friend WithEvents Label3 As System.Windows.Forms.Label
  Friend WithEvents txtIPAddress As System.Windows.Forms.TextBox
  Friend WithEvents txtFcLogonString As System.Windows.Forms.TextBox
  Friend WithEvents Label6 As System.Windows.Forms.Label
  Friend WithEvents udPosId As System.Windows.Forms.NumericUpDown
  Friend WithEvents cbClearDeliveryReports As CheckBox
  Friend WithEvents GroupBox2 As GroupBox
  Friend WithEvents cbShowUnsolicitedMessages As CheckBox
  Friend WithEvents cbShowHeartbeatMessages As CheckBox
  Friend WithEvents cbClearBackOfficeRecords As CheckBox
  Friend WithEvents cbAutoLockTransactions As CheckBox
End Class
