using System;
using System.Collections.Generic;

namespace JPL_Demo_POS_CSharp
{
    // Tank Gauge related message types

    #region TgStatus

    public class TgStatusRespType : DomsPosResponseType
    {
        public class TgStatusRespDataType
        {
            public class TgSubStateFlags
            {
                public int TankGaugeOnline { get; set; }
                public int TankGaugeInAlarm { get; set; }
                public int TankGaugeInWarning { get; set; }
                public int TankGaugeInService { get; set; }
                public int TankGaugeInTest { get; set; }
                public int TankGaugeInCalibration { get; set; }
                public int TankGaugeInMaintenance { get; set; }
                public int TankGaugeInError { get; set; }
            }

            public class TgMainStateType
            {
                public string Unconfigured { get; set; }
                public string Configured { get; set; }
                public string Online { get; set; }
                public string Offline { get; set; }
                public string InAlarm { get; set; }
                public string InWarning { get; set; }
                public string InService { get; set; }
                public string InTest { get; set; }
                public string InCalibration { get; set; }
                public string InMaintenance { get; set; }
                public string InError { get; set; }
            }

            public class TgAlarmStatusFlags
            {
                public int HighLevelAlarm { get; set; }
                public int HighHighLevelAlarm { get; set; }
                public int LowLevelAlarm { get; set; }
                public int LowLowLevelAlarm { get; set; }
                public int HighWaterAlarm { get; set; }
                public int TankLeakAlarm { get; set; }
                public int TankDataMissingAlarm { get; set; }
                public int HighHighWaterAlarm { get; set; }
                public int TicketedDeliveryDataLost { get; set; }
                public int DeliveryDataLost { get; set; }
                public int OtherAlarm { get; set; }
            }

            public string TgId { get; set; }
            public EnumType<TgMainStateType> TgMainState { get; set; }
            public BitFlagType<TgSubStateFlags> TgSubStates { get; set; }
            public BitFlagType<TgAlarmStatusFlags> TgAlarmStatus { get; set; }
        }

        public TgStatusRespDataType data { get; set; }

        public string MainStateText()
        {
            string s = null;

            switch (this.data.TgMainState.value)
            {
                case "00H": s = "Unconfigured"; break;
                case "01H": s = "Configured"; break;
                case "02H": s = "Online"; break;
                case "03H": s = "Offline"; break;
                case "04H": s = "In Alarm"; break;
                case "05H": s = "In Warning"; break;
                case "06H": s = "In Service"; break;
                case "07H": s = "In Test"; break;
                case "08H": s = "In Calibration"; break;
                case "09H": s = "In Maintenance"; break;
                case "0AH": s = "In Error"; break;
            }

            return s;
        }
    }

    #endregion

    #region TgData

    public class TgDataReqType : DomsPosBaseType
    {
        public class TgDataReqDataType
        {
            public string TgId { get; set; }
        }

        public TgDataReqDataType data { get; set; }

        public TgDataReqType(int tgId) : base("TgData_req", "00H")
        {
            this.data = new TgDataReqDataType();
            this.data.TgId = tgId.ToString();
        }
    }

    public class TgDataRespType : DomsPosResponseType
    {
        public class SignedTemperatureType
        {
            public string Temperature { get; set; }
        }

        public class TgDataRespDataType
        {
            public class TankDataItemsType
            {
                public string TankLevel { get; set; }
                public string TankVolume { get; set; }
                public string TankUllage { get; set; }
                public string TankCapacity { get; set; }
                public string TankWaterLevel { get; set; }
                public string TankWaterVolume { get; set; }
                public SignedTemperatureType TankAvgTemp { get; set; }
                public string TankDensity { get; set; }
                public string TankMass { get; set; }
                public string TankDataDateAndTime { get; set; }
            }

            public string TgId { get; set; }
            public TankDataItemsType TankDataItems { get; set; }
        }

        public TgDataRespDataType data { get; set; }
    }

    #endregion

    #region TankDeliveryData

    public class TankDeliveryDataReqType : DomsPosBaseType
    {
        public class TankDeliveryDataReqDataType
        {
            public string TgId { get; set; }
        }

        public TankDeliveryDataReqDataType data { get; set; }

        public TankDeliveryDataReqType(int tgId) : base("TankDeliveryData_req", "00H")
        {
            this.data = new TankDeliveryDataReqDataType();
            this.data.TgId = tgId.ToString();
        }
    }

    public class TankDeliveryDataRespType : DomsPosResponseType
    {
        public class TankDeliveryDataRespDataType
        {
            public class TankDeliveryDataItemsType
            {
                public string TankDeliverySeqNo { get; set; }
                public string DeliveryStartDateAndTime { get; set; }
                public string DeliveryEndDateAndTime { get; set; }
                public string DeliveryStartVol { get; set; }
                public string DeliveryEndVol { get; set; }
                public string DeliveryVol { get; set; }
                public string DeliveryStartLevel { get; set; }
                public string DeliveryEndLevel { get; set; }
                public string DeliveryStartWaterLevel { get; set; }
                public string DeliveryEndWaterLevel { get; set; }
                public string DeliveryStartTemp { get; set; }
                public string DeliveryEndTemp { get; set; }
                public string DeliveryStartDensity { get; set; }
                public string DeliveryEndDensity { get; set; }
            }

            public string TgId { get; set; }
            public string DeliveryReportSeqNo { get; set; }
            public TankDeliveryDataItemsType TankDeliveryDataItems { get; set; }
        }

        public TankDeliveryDataRespDataType data { get; set; }
    }

    #endregion

    #region SiteDeliveryStatus

    public class SiteDeliveryStatusRespType : DomsPosResponseType
    {
        public class SiteDeliveryStatusRespDataType
        {
            public class DeliveryStatusFlagsType
            {
                public int SiteDeliveryDataIsReady { get; set; }
                public int SiteDeliveryInProgress { get; set; }
                public int SiteDeliveryCompleted { get; set; }
                public int SiteDeliveryAborted { get; set; }
            }

            public string TgId { get; set; }
            public string DeliveryReportSeqNo { get; set; }
            public BitFlagType<DeliveryStatusFlagsType> DeliveryStatusFlags { get; set; }
        }

        public SiteDeliveryStatusRespDataType data { get; set; }
    }

    #endregion

    #region clear_DeliveryReport

    public class ClearDeliveryReportReqType : DomsPosBaseType
    {
        public class ClearDeliveryReportReqDataType
        {
            public string TgId { get; set; }
            public string DeliveryReportSeqNo { get; set; }
        }

        public ClearDeliveryReportReqDataType data { get; set; }

        public ClearDeliveryReportReqType(string tgId, string deliveryReportSeqNo) : base("clear_DeliveryReport_req", "00H")
        {
            this.data = new ClearDeliveryReportReqDataType();
            this.data.TgId = tgId;
            this.data.DeliveryReportSeqNo = deliveryReportSeqNo;
        }
    }

    #endregion

    #region FcGradeNames

    public class FcGradeNamesReqType : DomsPosBaseType
    {
        public object data { get; set; } = new object();

        public FcGradeNamesReqType() : base("FcGradeNames_req", "00H")
        {
        }
    }

    public class FcGradeNamesRespType : DomsPosResponseType
    {
        public class FcGradeNamesRespDataType
        {
            public class FcGradeType
            {
                public string FcGradeId { get; set; }
                public string FcGradeName { get; set; }
            }

            public List<FcGradeType> FcGrades { get; set; }
        }

        public FcGradeNamesRespDataType data { get; set; }
    }

    #endregion
}
