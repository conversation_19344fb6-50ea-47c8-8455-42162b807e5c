﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmJplConsole
  Inherits System.Windows.Forms.Form

  'Form overrides dispose to clean up the component list.
  <System.Diagnostics.DebuggerNonUserCode()> _
  Protected Overrides Sub Dispose(ByVal disposing As Boolean)
    Try
      If disposing AndAlso components IsNot Nothing Then
        components.Dispose()
      End If
    Finally
      MyBase.Dispose(disposing)
    End Try
  End Sub

  'Required by the Windows Form Designer
  Private components As System.ComponentModel.IContainer

  'NOTE: The following procedure is required by the Windows Form Designer
  'It can be modified using the Windows Form Designer.  
  'Do not modify it using the code editor.
  <System.Diagnostics.DebuggerStepThrough()> _
  Private Sub InitializeComponent()
    Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
    Me.Label1 = New System.Windows.Forms.Label()
    Me.btnSendMessage = New System.Windows.Forms.Button()
    Me.txtRequest = New System.Windows.Forms.TextBox()
    Me.Label2 = New System.Windows.Forms.Label()
    Me.txtResponse = New System.Windows.Forms.TextBox()
    CType(Me.SplitContainer1, System.ComponentModel.ISupportInitialize).BeginInit()
    Me.SplitContainer1.Panel1.SuspendLayout()
    Me.SplitContainer1.Panel2.SuspendLayout()
    Me.SplitContainer1.SuspendLayout()
    Me.SuspendLayout()
    '
    'SplitContainer1
    '
    Me.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill
    Me.SplitContainer1.Location = New System.Drawing.Point(0, 0)
    Me.SplitContainer1.Name = "SplitContainer1"
    Me.SplitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal
    '
    'SplitContainer1.Panel1
    '
    Me.SplitContainer1.Panel1.Controls.Add(Me.Label1)
    Me.SplitContainer1.Panel1.Controls.Add(Me.btnSendMessage)
    Me.SplitContainer1.Panel1.Controls.Add(Me.txtRequest)
    Me.SplitContainer1.Panel1MinSize = 100
    '
    'SplitContainer1.Panel2
    '
    Me.SplitContainer1.Panel2.Controls.Add(Me.Label2)
    Me.SplitContainer1.Panel2.Controls.Add(Me.txtResponse)
    Me.SplitContainer1.Panel2MinSize = 100
    Me.SplitContainer1.Size = New System.Drawing.Size(587, 472)
    Me.SplitContainer1.SplitterDistance = 235
    Me.SplitContainer1.TabIndex = 0
    '
    'Label1
    '
    Me.Label1.AutoSize = True
    Me.Label1.Location = New System.Drawing.Point(12, 9)
    Me.Label1.Name = "Label1"
    Me.Label1.Size = New System.Drawing.Size(50, 13)
    Me.Label1.TabIndex = 2
    Me.Label1.Text = "Request:"
    '
    'btnSendMessage
    '
    Me.btnSendMessage.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
    Me.btnSendMessage.Location = New System.Drawing.Point(445, 195)
    Me.btnSendMessage.Name = "btnSendMessage"
    Me.btnSendMessage.Size = New System.Drawing.Size(130, 23)
    Me.btnSendMessage.TabIndex = 1
    Me.btnSendMessage.Text = "&Send Message"
    Me.btnSendMessage.UseVisualStyleBackColor = True
    '
    'txtRequest
    '
    Me.txtRequest.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
    Me.txtRequest.DataBindings.Add(New System.Windows.Forms.Binding("Text", Global.JPL_Demo_POS.My.MySettings.Default, "CurrentRequest", True, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged))
    Me.txtRequest.Font = New System.Drawing.Font("Consolas", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
    Me.txtRequest.Location = New System.Drawing.Point(12, 24)
    Me.txtRequest.Multiline = True
    Me.txtRequest.Name = "txtRequest"
    Me.txtRequest.ScrollBars = System.Windows.Forms.ScrollBars.Both
    Me.txtRequest.Size = New System.Drawing.Size(563, 165)
    Me.txtRequest.TabIndex = 0
    Me.txtRequest.Text = Global.JPL_Demo_POS.My.MySettings.Default.CurrentRequest
    '
    'Label2
    '
    Me.Label2.AutoSize = True
    Me.Label2.Location = New System.Drawing.Point(12, 0)
    Me.Label2.Name = "Label2"
    Me.Label2.Size = New System.Drawing.Size(58, 13)
    Me.Label2.TabIndex = 3
    Me.Label2.Text = "Response:"
    '
    'txtResponse
    '
    Me.txtResponse.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
    Me.txtResponse.Font = New System.Drawing.Font("Consolas", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
    Me.txtResponse.Location = New System.Drawing.Point(12, 16)
    Me.txtResponse.Multiline = True
    Me.txtResponse.Name = "txtResponse"
    Me.txtResponse.ReadOnly = True
    Me.txtResponse.ScrollBars = System.Windows.Forms.ScrollBars.Both
    Me.txtResponse.Size = New System.Drawing.Size(563, 205)
    Me.txtResponse.TabIndex = 1
    '
    'frmJsonConsole
    '
    Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
    Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
    Me.ClientSize = New System.Drawing.Size(587, 472)
    Me.Controls.Add(Me.SplitContainer1)
    Me.MinimizeBox = False
    Me.MinimumSize = New System.Drawing.Size(300, 300)
    Me.Name = "frmJsonConsole"
    Me.Text = "JPL Console"
    Me.SplitContainer1.Panel1.ResumeLayout(False)
    Me.SplitContainer1.Panel1.PerformLayout()
    Me.SplitContainer1.Panel2.ResumeLayout(False)
    Me.SplitContainer1.Panel2.PerformLayout()
    CType(Me.SplitContainer1, System.ComponentModel.ISupportInitialize).EndInit()
    Me.SplitContainer1.ResumeLayout(False)
    Me.ResumeLayout(False)

  End Sub

  Friend WithEvents SplitContainer1 As SplitContainer
  Friend WithEvents Label1 As Label
  Friend WithEvents btnSendMessage As Button
  Friend WithEvents txtRequest As TextBox
  Friend WithEvents Label2 As Label
  Friend WithEvents txtResponse As TextBox
End Class
