﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.SystemMessages
{
    public class ForecourtSystemResponse : ForecourtMessage
    {
        public ForecourtCommandMessageTypes ResponseCode { get; set; }

        

        public ForecourtSystemResponse(ForecourtCommandMessageTypes responseCode, int sequenceNo)
            : base(ForecourtMessageClasses.SYSMGT_RESPONSE, sequenceNo)
        {
            ResponseCode = responseCode;
        }

        public static ForecourtSystemResponse Deserialise(XContainer messageNode, int sequence)
        {
            //<FCCMessage>
            //  <Header MessageType="SYSMGT_RESPONSE" SeqNo="1" SourceID="0" TargetID="0" />
            //  <SysMgtResp Code="APPLICATION_STATUS" Result="SUCCESS">
            //    <Application Name="PumpControl" Status="Disabled" />
            //  </SysMgtResp>
            //</FCCMessage>

            var sysMgt = messageNode.Element("SysMgtResp");
            if (sysMgt == null)
            {
                throw new XmlSchemaValidationException("Message does not have <SysMgtResp> node.");
            }

            var code =
                (ForecourtCommandMessageTypes)
                    Enum.Parse(typeof (ForecourtCommandMessageTypes), sysMgt.Attribute("Code").Value);

            switch (code)
            {
                    case ForecourtCommandMessageTypes.APPLICATION_STATUS:
                    return ForecourtSystemStatusResponse.Parse(sysMgt,sequence);
            }

            return null;
        }
    }
}
