﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class AuthorizeRequest : ForecourtCommandRequest
    {
        public AuthModes Mode { get; set; }
        public LimitTypes LimitType { get; set; }
        public decimal Limit { get; set; }
        public IForecourtBlend Blend { get; set; }


        public AuthorizeRequest(int sequenceNo, int sourceId, int targetId, AuthModes mode, LimitTypes limitType,
            decimal limit, IForecourtBlend blend)
            : base(sequenceNo, sourceId, targetId, ForecourtCommandMessageTypes.AUTHORIZE)
        {
            Mode = mode;
            LimitType = limitType;
            Limit = limit;
            Blend = blend;
        }

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            var req = xdoc.Root.Element("CommandReq");
            if (req == null) return xdoc;
            var auth = new XElement("DispenserAuthorize",
                new XAttribute("Mode", Mode),
                new XAttribute("LimitType", LimitType),
                 new XAttribute("Limit", Limit),
                new XAttribute("PriceLevel", 0)
                );
            if (Blend != null)
            {
                auth.Add(new XElement("Blends",
                    new XElement("Blend",
                        new XAttribute("ID", Blend.BlendId),
                        new XAttribute("Name", Blend.BlendName))
                    ));
            }
            req.Add(auth);

            return xdoc;
        }
    }
}
