﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.DispenseControl;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class DispenseControlService
    {
        private readonly PssClient _client;

        public DispenseControlService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<FpStatusResponse> GetFuellingPointStatusAsync(string fpId, 
            CancellationToken cancellationToken = default)
        {
            var request = new FpStatusRequest
            {
                Name = JplConstants.MessageNames.FP_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FpStatusRequest.FpStatusRequestData
                {
                    FpId = fpId
                }
            };

            return await _client.SendRequestAsync<FpStatusResponse>(request, cancellationToken);
        }
    }
}
