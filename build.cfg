[Release]
Release=52A
Build=4

#CheckCoreVersionBelow

# .
# This is the path where ccnet will upload the qa builds.
# It is relative to svn://alm/Releases/Dev/
# Once you've done a qa build, you can tag this path into svn://alm/Releases/QA Test/ (manually for now)

ReleaseSvnUrl=tstCustomer Releases/Z

# ************** Core patched files ************************
#
# 
# 
#
# **********************************************************

# This parameter specifies which version of core to tag into the release
# It will be tagged and renamed to ${ReleasePath}/Infinity
# This will occur *before* release files are copied
# Leave it blank if this project is not distributed with Core
# R52: CoreVersion=82.21.26.120
# R51: CoreVersion=82.17.15.118

CoreVersion=82.22.4.120
irn.sys={CoreVersion}; Z Energy {Release} - Build {Build}

[Exec]
#build.bat
# Change the last parameter to match Release and Build in the [Release] section ([Release].[Build].*)

[Setup Projects]
# msbuild.exe (which is what ccnet is using to build the solution) does not work with visual studio setup projects.
# I know, wtf? We have to call devenv.exe to manually build these. Awesome.
# put a list of paths to vdproj files here, relative to the solution directory.
# They will be built in the order listed here.
#Integration.Setup\Integration.Setup.vdproj
CaltexBOSMigrationTool\CaltexMigrationSetup\CaltexMigrationSetup.vdproj

[Persisted Folders]
Components


[Release Files]
"OneOffScripts\*.*"						"OneOffScripts\"
"pumpRegulus\bin\Release\*.dll"					"Infinity\Components\pumpRegulus"
"rptZEnergy\bin\Release\*.dll"					"Infinity\Components"
"rptZOnly\bin\Release\*.dll"					"Infinity\Components"
"Z Energy ZCard Reference Datafeed\Package.dtsx"		"Z Card SSIS Package"
"CaltexBOSMigrationTool\CaltexMigrationSetup\Release\*.*"	"CaltexMigration"
#"CaltexOnly\*.*"	                                	"CaltexOnly"
"IncludeIntoBuild\*.*"							"Infinity\Components"

# because of the CCNet does not support SSIS project so common it
# "ZCardReferenceDataFeed\bin\Development\ZCardReferenceDataFeed.ispac"	"Infinity\Components\SSIS"