﻿Public Class frmPrices

  Private _PriceDecimalPointPos As Integer
  Private _Forecourt As clsForecourt
  Private _FcPriceSetResponse As FcPriceSetRespType
  Private _PricesSetCallback As clsForecourt.OperationCompletedDelegate


  Public Property PricesSetCallback As clsForecourt.OperationCompletedDelegate
    Get
      Return _PricesSetCallback
    End Get
    Set(ByVal value As clsForecourt.OperationCompletedDelegate)
      _PricesSetCallback = value
    End Set
  End Property

  Public Property FcPriceSetResponse As FcPriceSetRespType
    Get
      Return _FcPriceSetResponse
    End Get
    Set(ByVal value As FcPriceSetRespType)
      _FcPriceSetResponse = value
    End Set
  End Property

  Public Property Forecourt As clsForecourt
    Get
      Return _Forecourt
    End Get
    Set(ByVal value As clsForecourt)
      _Forecourt = value
    End Set
  End Property


  Public Property PriceDecimalPointPos() As Integer
    Get
      Return _PriceDecimalPointPos
    End Get
    Set(ByVal value As Integer)
      _PriceDecimalPointPos = value
    End Set
  End Property

  Private Sub DataGridView1_CellFormatting(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) Handles DataGridView1.CellFormatting
    If (e.ColumnIndex - 2) > 0 AndAlso (e.ColumnIndex - 2) Mod 2 <> 0 Then
      e.Value = frmMain.FormatNumber(e.Value, _PriceDecimalPointPos)
      e.FormattingApplied = True
    End If
  End Sub

  Private Sub DataGridView1_DataError(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewDataErrorEventArgs) Handles DataGridView1.DataError
    MsgBox(e.Exception.Message, MsgBoxStyle.Exclamation)
  End Sub

  Private Sub btnChangePrices_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnChangePrices.Click
    Dim dt As DataTable = DirectCast(DataGridView1.DataSource, DataTable)
    Dim GradeIndex, PriceGroupIndex As Integer
    Dim ColumnName As String
    Dim PriceStr As String

    If DataGridView1.IsCurrentCellDirty Then
      MsgBox("To commit the value of the current cell, move to another cell.", MsgBoxStyle.Information)
      Return
    End If

    For Each dr As DataRow In dt.Rows
      GradeIndex = dr(0)

      For ColumnIndex As Integer = 2 To dt.Columns.Count - 1
        ColumnName = dt.Columns(ColumnIndex).ColumnName

        If ColumnName.StartsWith("_") Then
          PriceGroupIndex = dr(ColumnIndex)
        Else
          PriceStr = RemoveSeparators(frmMain.FormatNumber(dr(ColumnIndex), _PriceDecimalPointPos))
          _FcPriceSetResponse.data.FcPriceGroups(PriceGroupIndex)(GradeIndex) = PriceStr
        End If
      Next
    Next

    _Forecourt.SetPrices(_FcPriceSetResponse.data, _PricesSetCallback)
    Close()
  End Sub

  Private Sub frmPrices_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
    Dim dt As New DataTable
    Dim FcPriceGroupId, FcGradeId, GradeName As String

    dt.Columns.Add("_GradeIndex", GetType(Byte))
    dt.Columns.Add("Grade", GetType(String))

    Try
      For PriceGroupIndex As Integer = 0 To _FcPriceSetResponse.data.FcPriceGroupId.Count - 1
        FcPriceGroupId = _FcPriceSetResponse.data.FcPriceGroupId(PriceGroupIndex)

        Dim GroupIndexColumn As New DataColumn(String.Format("_GroupIndex {0}", FcPriceGroupId), GetType(Byte))
        Dim GroupPriceColumn As New DataColumn(String.Format("Group {0}", FcPriceGroupId), GetType(Decimal))

        dt.Columns.Add(GroupIndexColumn)
        dt.Columns.Add(GroupPriceColumn)
      Next

      For GradeIndex As Integer = 0 To _FcPriceSetResponse.data.FcGradeId.Count - 1
        FcGradeId = _FcPriceSetResponse.data.FcGradeId(GradeIndex)
        GradeName = _Forecourt.LookUpGradeName(FcGradeId)

        Dim dr As DataRow = dt.NewRow
        dr("_GradeIndex") = GradeIndex
        dr("Grade") = GradeName

        For PriceGroupIndex As Integer = 0 To _FcPriceSetResponse.data.FcPriceGroupId.Count - 1
          FcPriceGroupId = _FcPriceSetResponse.data.FcPriceGroupId(PriceGroupIndex)

          dr(String.Format("_GroupIndex {0}", FcPriceGroupId)) = PriceGroupIndex
          dr(String.Format("Group {0}", FcPriceGroupId)) = clsForecourt.BCDBufToDecimal(_FcPriceSetResponse.data.FcPriceGroups(PriceGroupIndex)(GradeIndex), My.Settings.PriceDecimalPointPosition)
        Next

        dt.Rows.Add(dr)
      Next

      DataGridView1.DataSource = dt
      DataGridView1.Columns.Item(1).ReadOnly = True

      For i As Integer = 0 To DataGridView1.Columns.Count - 1
        DataGridView1.Columns(i).Visible = Not DataGridView1.Columns(i).Name.StartsWith("_")
      Next
    Catch ex As Exception
      MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
  End Sub

  Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
    Me.Close()
  End Sub

  Private Sub DataGridView1_CellValidating(sender As Object, e As DataGridViewCellValidatingEventArgs) Handles DataGridView1.CellValidating
    Dim d As Decimal

    If (e.ColumnIndex - 2) > 0 AndAlso (e.ColumnIndex - 2) Mod 2 <> 0 Then
      If Decimal.TryParse(e.FormattedValue, Globalization.NumberStyles.AllowDecimalPoint Or Globalization.NumberStyles.AllowThousands, Nothing, d) Then
        e.Cancel = RemoveSeparators(frmMain.FormatNumber(d, _PriceDecimalPointPos)).Length > 6
        If e.Cancel Then lblError.Text = "Field contains too many digits."
      Else
        e.Cancel = True
        lblError.Text = "Field contains invalid numeric value"
      End If

      If Not e.Cancel Then
        lblError.Text = String.Empty
        btnChangePrices.Enabled = True
      Else
        btnChangePrices.Enabled = False
      End If
    End If
  End Sub

  Private Sub frmPrices_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
    e.Cancel = False
  End Sub

  Private Function RemoveSeparators(s As String) As String
    Return s.Replace(My.Application.Culture.NumberFormat.NumberDecimalSeparator, "").Replace(My.Application.Culture.NumberFormat.NumberGroupSeparator, "")
  End Function

End Class