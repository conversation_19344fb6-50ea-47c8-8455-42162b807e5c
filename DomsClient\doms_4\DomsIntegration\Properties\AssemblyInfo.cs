﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("DomsIntegration")]
[assembly: AssemblyDescription("DOMs Controller Client Library using JPL")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("DomsIntegration")]
[assembly: AssemblyCopyright("Copyright Â© 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("a1b2c3d4-e5f6-7890-abcd-123456789012")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
