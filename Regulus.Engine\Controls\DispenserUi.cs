﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;
using Triquestra.Common.PumpEsm.Messaging;
using WinFormUi.Controls;
using Triquestra.Common.PumpEsm.Helpers;
using Triquestra.Common.GUI;
using Triquestra.Common;
using Triquestra.Common.PumpEsm.Types;

namespace Triquestra.Common.PumpEsm.Controls
{
    public partial class DispenserUi : UserControl
    {
        #region Constants

        public const string MessagePumpCannotBeReserved = "Pump cannot be reserved.";

        #endregion

        private static NLog.Logger _log = NLog.LogManager.GetCurrentClassLogger();
        private DispenserModes _mode;
        private PumpInfoPosFooter _forecourt;
        private DispenserStates _dispenserState;
        private static readonly Color ColorAuthorized = Color.FromArgb(34, 213, 24);
        private static readonly Color ColorCalling = Color.FromArgb(238, 73, 17);
        private static readonly Color ColorDelivering = Color.FromArgb(115, 188, 245);
        private static readonly Color ColorReserved = Color.FromArgb(212, 162, 208);
        private static readonly Color ColorSuspended = Color.FromArgb(240, 224, 38);
        private static readonly Color ColorTimeout = Color.Red;
        private static readonly Color ColorDefault = Color.FromArgb(232, 232, 232);
        private DeliveryStates _deliveryState;
        private StackDeliveriesForm _stackForm;

        private readonly IGuiHelper _guiHelper;
        public int LastDeliveryId { get; set; }

        #region Ctors

        public DispenserUi()
        {
            InitializeComponent();
            Blends = new List<IForecourtBlend>();
            SetStyle(ControlStyles.StandardClick, true);
            //SetStyle(ControlStyles.Selectable, true);
            ImgPump.Image = Properties.Resources.pump_idle;
                        
            PnlStack.Visible = false;

            _guiHelper = new GuiHelper();
        }

        /// <summary>
        /// This constructor should be used for testing only
        /// </summary>
        /// <param name="guiHelper"></param>
        internal DispenserUi(IGuiHelper guiHelper)
        {
            InitializeComponent();
            Blends = new List<IForecourtBlend>();
            _guiHelper = guiHelper;
        }

        #endregion

        public bool IsBlinking { get; set; }
        public void DoBlink()
        {
            ImgIcon.Visible = !ImgIcon.Visible;
        }

        public void Connect(PumpInfoPosFooter forecourt)
        {
            _forecourt = forecourt;
        }

        public IEnumerable<IDelivery> DeliveryList
        {
			get
			{
                var dList = new List<IDelivery>();
                dList.AddRange(PnlStack.Controls.Cast<IDelivery>());
                dList.AddRange(PnlLight.Controls.Cast<IDelivery>());
                return dList;
			}
        }

        private int _dispenserId;
        public int DispenserId
        {
            get
            {
                return _dispenserId;
                
            }
            set
            {
                _dispenserId = value;
                LabelPumpId.Text = value.ToString(CultureInfo.InvariantCulture);
            }
        }

        private void Calling(bool on)
        {
            if (on)
            {
                PnlLight.BackColor = ColorCalling;
                ImgIcon.Image = Properties.Resources.calling;
                IsBlinking = true;
            }
            else
            {
                IsBlinking = false;
                ImgIcon.Visible = false;
            }
        }

        public DeliveryUi CurrentDelivery
        {
            get
            {
                if (PnlLight.Controls.Count > 0)
                    return (DeliveryUi)PnlLight.Controls[0];
                else
                    return null;
            }
        }

        public DispenserStates DispenserState
        {
            get { return _dispenserState; }
            set
            {
                _dispenserState = value;
                UpdatePumpForState();
            }
        }

        private void UpdatePumpForState()
        {
            var state = _dispenserState;
            DeliveryUi curDelivery;
            switch (state)
            {
                case DispenserStates.CALLING:
                    if (_mode == DispenserModes.COMP_AUTH)
                    {
                        Calling(true);
                    }
                    //Beep 
                    break;                
                case DispenserStates.CLEAR_REQUIRED:
                    curDelivery = CurrentDelivery;
                    if (curDelivery != null && curDelivery.Mode == AuthModes.PREPAY && curDelivery.IsTimedOut())
                    {
                        if (curDelivery.State == DeliveryStates.PREPAY_REFUND)
                            PnlLight.BackColor = ColorTimeout;
                        else
                            PnlLight.BackColor = ColorDefault;
                        ImgIcon.Image = Properties.Resources.prepay_timeout;
                        ImgIcon.Visible = true;
                        IsBlinking = false;
                    }
                    else
                    {
                        UpdateDispenserMode();
                        ImgIcon.Image = Properties.Resources.locked;
                        ImgIcon.Visible = true;
                        IsBlinking = false;
                    }
                    break;
                case DispenserStates.IDLE:
                    curDelivery = CurrentDelivery;
                    if (curDelivery != null && curDelivery.Mode == AuthModes.PREPAY && curDelivery.IsTimedOut())
                    {
                        if (curDelivery.State == DeliveryStates.PREPAY_REFUND)
                            PnlLight.BackColor = ColorTimeout;
                        else
                            PnlLight.BackColor = ColorDefault;
                        ImgIcon.Image = Properties.Resources.prepay_timeout;
                        ImgIcon.Visible = true;
                        IsBlinking = false;
                    }
                    else
                    {
                        UpdateDispenserMode();
                        Calling(false);
                    }
                    break;
                case DispenserStates.RESERVED:
                    PnlLight.BackColor = ColorReserved;
                    break;
                case DispenserStates.AUTHORIZED:
                    PnlLight.BackColor = ColorAuthorized;
                    Calling(false);
                    if (AuthMode == AuthModes.PREPAY)
                    {
                        ImgIcon.Image = Properties.Resources.prepay;
                        ImgIcon.Visible = true;
                    }
                    else if (AuthMode == AuthModes.PREAUTH)
                    {
                        ImgIcon.Image = Properties.Resources.opt;
                        ImgIcon.Visible = true;
                    }
                    break;

                case DispenserStates.DELIVERING:
                    Calling(false);
                    PnlLight.BackColor = ColorDelivering;
                    if (AuthMode == AuthModes.PREPAY)
                    {
                        ImgIcon.Image = Properties.Resources.prepay;
                        ImgIcon.Visible = true;
                    }
                    else if (AuthMode == AuthModes.PREAUTH)
                    {
                        ImgIcon.Image = Properties.Resources.opt;
                        ImgIcon.Visible = true;
                    }
                    break;

                case DispenserStates.SUSPENDED:
                    PnlLight.BackColor = ColorSuspended;
                    Calling(false);
                    ImgIcon.Image = Properties.Resources.stopped;
                    ImgIcon.Visible = true;
                    break;

                case DispenserStates.PRICE_CHANGING:
                    ImgIcon.Image = Properties.Resources.price_changing;
                    PnlLight.BackColor = Color.Black;
                    IsBlinking = true;
                    break;

                case DispenserStates.NOT_RESPONDING:
                    ImgIcon.Image = Properties.Resources.not_responding;
                    PnlLight.BackColor = Color.Black;
                    IsBlinking = true;
                    break;
                default:
                    PnlLight.BackColor = ColorDefault;
                    break;
            }
        }

        public DispenserModes Mode
        {
            get { return _mode; }
            set
            {
                _mode = value;
                UpdateDispenserMode();
            }
        }

        public AuthModes AuthMode { get; set; }

        public DeliveryStates DeliveryState
        {
            get { return _deliveryState; }            
        }

        public void SetDeliveryState(int deliveryId, DeliveryStates state)
        {
            LastDeliveryId = deliveryId;
            _deliveryState = state;
            var delivery = GetDelivery(deliveryId);
            if (delivery != null)
            {
                delivery.State = state;
                UpdatePumpForState();
            }
            else //undescribed delivery
            {
                _forecourt.QueryDeliveryInformation(DispenserId,deliveryId);
            }
            //switch (_deliveryState)
            //{
            //    case DeliveryStates.RESERVED:
            //        PnlLight.BackColor = ColorReserved;
            //        break;
            //}

        }

        private void UpdateDispenserMode()
        {
            switch (_mode)
            {                    
                case DispenserModes.AUTO_AUTH: PnlLight.BackColor = ColorAuthorized; break;
                case DispenserModes.COMP_AUTH: PnlLight.BackColor = Color.FromArgb(245, 248, 245); break;
                //default:
                //    PnlLight.BackColor = ColorDelivering;
            }
        }

        public void StartDelivery(IDelivery delivery)
        {
            if (DeliveryList.Any(fi => fi.DeliveryId == delivery.DeliveryId))
            {
                // Delivery is already in list
                var curDel = CurrentDelivery;
                if (curDel != null && curDel.DeliveryId == delivery.DeliveryId && curDel.NozzleId == delivery.NozzleId)
                {
                    UpdateDispenserDelivery(delivery);
                }
                else
                {
                    _log.Debug("Delivery already known. Pump #{0}, Delivery {1}", DispenserId, delivery.DeliveryId);
                }
                return;
            }

            var blend = Blends.SingleOrDefault(b => b.BlendId == delivery.BlendId && b.NozzleId == delivery.NozzleId);
            if (blend == null)
            {
                _log.Debug("Unknown blend ID {0} on a pump {1}, nozzle {2}.", delivery.BlendId, delivery.DispenserId, delivery.NozzleId);
                return;
            }
            
            var ui = new DeliveryUi()
            {
                Amount = delivery.Amount,
                Volume = delivery.Volume,
                BlendName = blend.BlendName,
                DeliveryId = delivery.DeliveryId,
                DispenserId = delivery.DispenserId,
                BlendId = delivery.BlendId,
                Mode = delivery.Mode,
                State = delivery.State,
                Price = delivery.Price,
                Limit = delivery.Limit,
                LimitType = delivery.LimitType,
                TankId = delivery.TankId,
                NozzleId = delivery.NozzleId,
                Exception = delivery.Exception,
                Owner = delivery.Owner
            };


            if (PnlLight.Controls.Count == 0)
            {
                PnlLight.Controls.Add(ui);
                ui.OnUserClick += PnlLight_Click;                
            }
            else
            {
                if (PnlLight.Controls[0] is DeliveryUi)
                {
                    var old = PnlLight.Controls[0] as DeliveryUi;
                    PnlLight.Controls.Remove(old);

                    if (PnlStack.Controls.Count == 0)
                    {
                        PnlStack.Controls.Add(old);
                        old.OnUserClick -= PnlLight_Click;
                        old.OnUserClick += PnlStack_Click;
                    }
                    else
                    {
                        if (PnlStack.Controls.Count == 1)
                        {
                            // don't need the click event anymore as there will be a stack ui on the top
                            var dui = (PnlStack.Controls[0]) as DeliveryUi;
                            dui.OnUserClick -= PnlStack_Click;
                            dui.OnUserClick += PnlStackMulti_Click;
                            dui.EnableStackImage();
                        }

                        old.OnUserClick -= PnlLight_Click;
                        old.OnUserClick += PnlStackMulti_Click;
                        old.EnableStackImage();
                        PnlStack.Controls.Add(old);
                    }
                    
                    PnlLight.Controls.Add(ui);
                    ui.OnUserClick += PnlLight_Click;
                }
            }
            PnlStack.Visible = PnlStack.Controls.Count>0;
        }

        public List<IForecourtBlend> Blends { get; set; }

        public void UpdateNozzleState(int nid, NozzleStates nozzleState)
        {
            switch (nozzleState)
            {
                case NozzleStates.IN:
                    
                    ImgPump.Image = Properties.Resources.pump_idle;
                    break;

                case NozzleStates.OUT:
                    //ProductNameText.Text = Blends[nid - 1].BlendName;
                    ImgPump.Image = Properties.Resources.pump_out;

                    break;
            }
        }

        private void LightOn()
        {
            PnlLight.BackColor = Color.FromArgb(226, 251, 159);
        }

        private void LightOff()
        {
            PnlLight.BackColor = Color.FromArgb(232, 232, 232);
        }

        public void AddBlend(IForecourtBlend blend)
        {
            Blends.Add(blend);
        }

        public void UpdateDispenserDelivery(IDelivery delivery)
        {
            var ui = PnlLight.Controls.Cast<DeliveryUi>().SingleOrDefault(d => d.DeliveryId == delivery.DeliveryId);

            if (ui == null)
            {
                return;
            }
            //MessageBox.Show("delivery.Amount=" + delivery.Amount + " delivery.Volume=" + delivery.Volume);
            ui.Amount = delivery.Amount;
            ui.Volume = delivery.Volume;
            ui.Limit = delivery.Limit;
            ui.Mode = delivery.Mode;
            ui.State = delivery.State;
            ui.LimitType = delivery.LimitType;
            ui.Price = delivery.Price;
            ui.Exception = delivery.Exception;

            if (delivery.NozzleId != 0)
                ui.NozzleId = delivery.NozzleId;
            else
                _log.Debug("Nozzle ID is 0");
            ui.Price = delivery.Price;
            ui.BlendId = delivery.BlendId;
            ui.PriceLevel = delivery.PriceLevel;
            ui.PriceId = delivery.PriceId;
            if (delivery.TankId != 0)
                ui.TankId = delivery.TankId;
            else
                _log.Debug("Tank ID is 0");
            ui.BlendName = Blends.SingleOrDefault(b => b.BlendId == delivery.BlendId && b.NozzleId == delivery.NozzleId).BlendName;
            ui.Owner = delivery.Owner;
        }

        public void LockDelivery(int deliveryId)
        {
            var delUi = GetDelivery(deliveryId);
            if (delUi == null) return;
            delUi.LockDelivery();
        }

        public void UnlockDelivery(int deliveryId)
        {
            var delUi = GetDelivery(deliveryId);
            if (delUi == null) return;
            delUi.UnlockDelivery();
        }

        // delivery and stacking
        internal void PnlLight_Click(object sender, System.EventArgs e)
        {
            if (_forecourt.BtnStop.Down)
            {
                _forecourt.BtnStop.Down = false;
                try
                {
                    if (DispenserState != DispenserStates.SUSPENDED)
                    {
                        _forecourt.DoSuspend(DispenserId);                        
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex.ToString());
                    _guiHelper.ShowError("Unable to suspend the pump: " + ex.Message, MessageBoxIcon.Error);
                }
                _forecourt.ReturnFocus();

            }
            else if (_forecourt.BtnPlay.Down)
            {
                _forecourt.BtnPlay.Down = false;
                try
                {
                    if (DispenserState == DispenserStates.SUSPENDED)
                    {
                        _forecourt.DoResume(DispenserId);                        
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex.ToString());
                    _guiHelper.ShowError("Unable to resume the pump: " + ex.Message, MessageBoxIcon.Error);
                }
                _forecourt.ReturnFocus();

            }
            else
            {
                if (!_forecourt.AllowSales)
                {
                    _forecourt.ReturnFocus();
                    return;
                }
                if (_forecourt.IsPrepay)
                {
                    _forecourt.IsPrepay = false;

                    //S21854 Sharetank not allowed with Prepay
                    var dataProvider = _forecourt.PumpDataProvider;
                    if (dataProvider.IsPrepayBlocked())
                    {
                        Dialogs.ShowMessage(_forecourt.GuiMode,
                            string.Format("Prepay function is not supported with {0}", dataProvider.GetCustomTextPrepay()));
                        return;
                    }

                    if (this.DispenserState == DispenserStates.IDLE)
                    {
                        _forecourt.TapForPrepay(this);
                        return;
                    }
                    else
                    {
                        _guiHelper.ShowMessage( MessagePumpCannotBeReserved, MessageBoxIcon.Error);
                    }
                    return;
                }

                if (PnlLight.Controls.Count == 0)
                {
                    _guiHelper.ShowMessage("No delivery.", MessageBoxIcon.Error);
                    _forecourt.ReturnFocus();
                    return;
                }
                var delivery = PnlLight.Controls.Cast<DeliveryUi>().FirstOrDefault();

                if (delivery.State == DeliveryStates.COMPLETED /*POSTPAY*/ || delivery.State == DeliveryStates.PREPAY_REFUND /*PREPAY*/|| delivery.IsLocked())
                {
                    SendDeliveryToSale(delivery);
                }
                else
                {
                    _log.Warn("Attempt to add the delivery into sale, but the current state is {0}", delivery.State);
                   _guiHelper.ShowMessage("Please wait for the delivery to be completed.", MessageBoxIcon.Error);
                }
            }

            _forecourt.ReturnFocus();
        }

        public void SendDeliveryToSale(DeliveryUi delivery)
        {
            _log.Debug("The delivery #{0} of the pump #{1} is about to be added to the sale. Its current status is {2}", delivery.DeliveryId, delivery.DispenserId,delivery.State);           
            _forecourt.SendDeliveryAsync(delivery);
        }

        private void ImgPump_Click(object sender, System.EventArgs e)
        {
            var p = ImgPump.PointToClient(Cursor.Position);
            var topOfPump = ImgPump.ClientRectangle.Contains(p) && p.Y < (LabelPumpId.Top-ImgPump.Top);
            if (topOfPump)
                PnlLight_Click(sender, e);
            else
                _forecourt.TapPump(DispenserId);

        }

        internal void PnlStackMulti_Click(object sender, EventArgs e)
        {
            if (!_forecourt.AllowSales)
                return;

            try
            {
                if (PnlStack.Controls.Count == 0)
                {
                    _guiHelper.ShowMessage("No delivery.", MessageBoxIcon.Error);
                    return;
                }
                var deliveries = PnlStack.Controls.Cast<DeliveryUi>().OrderBy(s => s.DeliveryId).ToList();
                if (_stackForm == null)
                {
                    _stackForm = new StackDeliveriesForm(this);
                    _stackForm.GuiMode = _forecourt.GuiMode;
                    _stackForm.StartPosition = FormStartPosition.Manual;
                    _stackForm.Location = new Point(_forecourt.Size.Width / 2 - _stackForm.Width, _forecourt.Location.Y - _stackForm.Height);
                }

                // To prevent the form being open twice
                if (!_stackForm.Visible)
                {
                    _stackForm.SetDeliveries(deliveries);
                    _stackForm.Show(this);
                }

            }
            finally
            {
                _forecourt.ReturnFocus();
            }
        }


        internal void PnlStack_Click(object sender, EventArgs e)
        {
            if (!_forecourt.AllowSales)
            {
                //b147249 do not suspend the pump
                //_forecourt.TapPump(DispenserId);
                return;
            }
            
            try
            {
                
                if (PnlStack.Controls.Count == 0)
                {
                    _guiHelper.ShowMessage("No delivery.", MessageBoxIcon.Error);
                    return;
                }
                var delivery = PnlStack.Controls.Cast<DeliveryUi>().FirstOrDefault();
                if (delivery == null)
                {
                    return;
                }
                SendDeliveryToSale(delivery);

            }
            finally
            {
                _forecourt.ReturnFocus();
            }
        }

        public void DeleteDelivery(int deliveryId)
        {
            var delivery = DeliveryList.SingleOrDefault(d => d.DeliveryId == deliveryId);
            if (delivery == null)
            {
                _log.Trace("Attempt to delete the delivery {0} on pump {1}, but it does not exist on the pump", deliveryId, DispenserId);
                return;
            }
            

            if (PnlLight.Controls.Count == 1)
            {
                var delUi = PnlLight.Controls.Cast<DeliveryUi>().FirstOrDefault(u=>u.DeliveryId == deliveryId);
                if (delUi != null && delUi.DeliveryId==deliveryId)
                {
                    PnlLight.Controls.Remove(delUi);
                    LastDeliveryId = 0;
                }
                else
                    _log.Trace("Attempt to delete the delivery {0} on pump {1}, but it does not exist onscreen", deliveryId, DispenserId);
            }

            if (PnlStack.Controls.Count > 0)
            {
                var delUi = PnlStack.Controls.Cast<DeliveryUi>().FirstOrDefault(u=>u.DeliveryId == deliveryId);
                if (delUi != null && delUi.DeliveryId == deliveryId)
                {
                    PnlStack.Controls.Remove(delUi);
                    if (PnlStack.Controls.Count == 1)
                    {
                        var stackedDelivery = PnlStack.Controls[0] as DeliveryUi;
                        stackedDelivery.OnUserClick -= PnlStackMulti_Click;
                        stackedDelivery.OnUserClick += PnlStack_Click;
                        stackedDelivery.DisableStackImage();
                    }
                    else if (PnlStack.Controls.Count == 0)
                        PnlStack.Visible = false;
                }      
                else
                    _log.Trace("Attempt to delete the delivery {0} on pump {1}, but it does not exist on the stack", deliveryId, DispenserId);
            }

        }

        public DeliveryUi GetDelivery(int deliveryId)
        {
            if (PnlLight.Controls.Count == 1)
            {
                var delUi = PnlLight.Controls.Cast<DeliveryUi>().FirstOrDefault();
                if (delUi != null && delUi.DeliveryId == deliveryId)
                {
                    return delUi;
                }
            }

            if (PnlStack.Controls.Count > 0)
            {
                var delUi = PnlStack.Controls.Cast<DeliveryUi>().FirstOrDefault(u=>u.DeliveryId == deliveryId);
                if (delUi != null && delUi.DeliveryId == deliveryId)
                {
                    return delUi;
                }
            }

            return null;
        }

        private void DispenserUi_Click(object sender, EventArgs e)
        {
            if (_forecourt != null)
                _forecourt.ReturnFocus();
        }

        private void panel1_Click(object sender, EventArgs e)
        {
            if (_forecourt != null)
                _forecourt.ReturnFocus();
        }
    }
}
