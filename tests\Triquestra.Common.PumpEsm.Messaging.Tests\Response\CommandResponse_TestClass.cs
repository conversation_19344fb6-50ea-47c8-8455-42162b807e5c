﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Triquestra.Common.PumpEsm.Messaging.Response;

namespace Triquestra.Common.PumpEsm.Messaging.Tests.Response
{
    [TestClass]
    public class CommandResponse_TestClass
    {
        // this is breaking test case - we don't have Nozzle_Out in enum ForecourtCommandResults for some reason (?)
        [TestMethod]
        public void CommandResponse_TestClass_ParseResult_NozzleOut()
        {
            string message = @"<FCCMessage><Header MessageType=""COMMAND_RESPONSE"" SeqNo=""0"" SourceID=""3"" TargetID=""1""/><CommandResp Code=""RESERVE"" Result=""NOZZLE_OUT""/></FCCMessage>";

            var actual = CommandResponse.Deserialise(message);

            Assert.IsNotNull(actual);
        }
    }
}
