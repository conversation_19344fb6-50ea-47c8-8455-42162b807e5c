﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class ForecourtControllerService
    {
        private readonly PssClient _client;

        public ForecourtControllerService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default)
        {
            var request = new FcStatusRequest
            {
                Name = JplConstants.MessageNames.FC_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcStatusRequest.FcStatusData()
            };

            return await _client.SendRequestAsync<FcStatusResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default)
        {
            var request = new FcDateTimeRequest
            {
                Name = JplConstants.MessageNames.FC_DATE_TIME_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData()
            };

            return await _client.SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default)
        {
            var request = new FcDateTimeRequest
            {
                Name = JplConstants.MessageNames.CHANGE_FC_DATE_TIME_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData
                {
                    FcDateAndTime = dateTime.ToString("yyyyMMddHHmmss")
                }
            };

            return await _client.SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task EnableStatusUpdates(CancellationToken cancellationToken = default)
        {
            var request = new ChangeFcStatusUpdateModeRequest
            {
                Name = JplConstants.MessageNames.CHANGE_FC_STATUS_UPDATE_MODE_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new ChangeFcStatusUpdateModeRequest.ChangeFcStatusUpdateModeData
                {
                    StatusUpdateCode = 3 // Enable all status updates
                }
            };

            await _client.SendRequestAsync<ChangeFcStatusUpdateModeResponse>(request, cancellationToken);
        }
    }
}
