﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{46183E79-1842-4E38-B646-75ABA1DFF4CA}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Triquestra.Common.PumpEsm</RootNamespace>
    <AssemblyName>Regulus.Engine</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
    <SccProjectName>Svn</SccProjectName>
    <SccLocalPath>Svn</SccLocalPath>
    <SccAuxPath>Svn</SccAuxPath>
    <SccProvider>SubversionScc</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\..\..\..\InfinityPos\pumpRegulus\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CloudFoundation, Version=12.0.0.5, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\CloudFoundation.12.0.0.5\lib\net48\CloudFoundation.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.2.0.143\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DataAccess, Version=5.0.0.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAccess.5.0.0.3\lib\net48\DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="GUI, Version=15.0.0.8, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GUI.15.0.0.8\lib\net48\GUI.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.3\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Utils, Version=5.0.0.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Utils.5.0.0.3\lib\net48\Utils.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\ConfigFormPropertiesResult.cs" />
    <Compile Include="Base\ConfigGetItemDataResult.cs" />
    <Compile Include="Base\Main.cs" />
    <Compile Include="Base\PumpEsmAction.cs" />
    <Compile Include="Base\PumpEsmCompleteRequest.cs" />
    <Compile Include="Base\Helpers\EventLogger.cs" />
    <Compile Include="Base\Helpers\PseudoXmlHelper.cs" />
    <Compile Include="Base\Helpers\StringBuilderHelper.cs" />
    <Compile Include="Base\Native.cs" />
    <Compile Include="Base\PumpActions.cs" />
    <Compile Include="Base\PumpConfigs.cs" />
    <Compile Include="Base\PumpEsmActionRequest.cs" />
    <Compile Include="Base\PumpEsmActionResult.cs" />
    <Compile Include="Base\PumpEsmBase.cs" />
    <Compile Include="Base\PumpEsmConfigRequest.cs" />
    <Compile Include="Base\PumpEsmNotifyRequest.cs" />
    <Compile Include="Base\PumpEsmRequest.cs" />
    <Compile Include="Base\PumpNotifications.cs" />
    <Compile Include="Base\WindowWrapper.cs" />
    <Compile Include="Builders\DeliveryBuilder.cs" />
    <Compile Include="Consts.cs" />
    <Compile Include="Controls\StackDeliveriesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Controls\StackDeliveriesForm.Designer.cs">
      <DependentUpon>StackDeliveriesForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Helpers\DigifortHelper.cs" />
    <Compile Include="Helpers\GuiHelper.cs" />
    <Compile Include="Helpers\IDigifortHelper.cs" />
    <Compile Include="Helpers\IGuiHelper.cs" />
    <Compile Include="Helpers\MediaHelper.cs" />
    <Compile Include="Helpers\ReadWriteTextFileHelper.cs" />
    <Compile Include="PostProcessing\AfterPrepayComplete.cs" />
    <Compile Include="Controls\DeliveryUi.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DeliveryUi.Designer.cs">
      <DependentUpon>DeliveryUi.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\DispenserUi.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DispenserUi.Designer.cs">
      <DependentUpon>DispenserUi.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\PrepayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Controls\PrepayForm.Designer.cs">
      <DependentUpon>PrepayForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="PumpInfoPosFooter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PumpInfoPosFooter.Designer.cs">
      <DependentUpon>PumpInfoPosFooter.cs</DependentUpon>
    </Compile>
    <Compile Include="PumpRegulusEsm.cs" />
    <Compile Include="Types\IPumpDataProvider.cs" />
    <Compile Include="Types\PendingDelivery.cs" />
    <Compile Include="Types\PetrolSaleItem.cs" />
    <Compile Include="Types\POSModeType.cs" />
    <Compile Include="Types\PumpDataProvider.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Controls\DeliveryUi.resx">
      <DependentUpon>DeliveryUi.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\DispenserUi.resx">
      <DependentUpon>DispenserUi.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\PrepayForm.resx">
      <DependentUpon>PrepayForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\StackDeliveriesForm.resx">
      <DependentUpon>StackDeliveriesForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="PumpInfoPosFooter.resx">
      <DependentUpon>PumpInfoPosFooter.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\calling.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\not_responding.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\OPT.jpg" />
    <None Include="Resources\prepay.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\price_changing.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pump_idle.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pump_out.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\stopped.jpg" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\FuelDataAccess\FuelDataAccess.csproj">
      <Project>{af35b5ea-1dab-47ba-bd7f-a2d79fa4a089}</Project>
      <Name>FuelDataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\Regulus.Interface\Regulus.Interface.csproj">
      <Project>{DE293021-69C1-4A4E-9A1A-3671AAAB4E7F}</Project>
      <Name>Regulus.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\src_packages\Triquestra.Common.ForeCourt.Comms\Triquestra.Common.ForeCourt.Comms.csproj">
      <Project>{0513d6d9-fa22-4890-bc07-9f513e07fbb5}</Project>
      <Name>Triquestra.Common.ForeCourt.Comms</Name>
    </ProjectReference>
    <ProjectReference Include="..\src_packages\Triquestra.Common.ForeCourt.Messaging\Triquestra.Common.ForeCourt.Messaging.csproj">
      <Project>{8d6139a7-070a-4a46-81da-bf3b21f82657}</Project>
      <Name>Triquestra.Common.ForeCourt.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\src_packages\Triquestra.Common.ForeCourt.RegulusInterface\Triquestra.Common.Forecourt.csproj">
      <Project>{e0ac3ab4-3f3b-4524-80a5-2e5ad93a4ef3}</Project>
      <Name>Triquestra.Common.Forecourt</Name>
    </ProjectReference>
    <ProjectReference Include="..\tests\Triquestra.Common.PumpEsm.Messaging.Tests\Triquestra.Common.PumpEsm.Messaging.Tests.csproj">
      <Project>{633ffffb-896b-434e-a660-81205424f943}</Project>
      <Name>Triquestra.Common.PumpEsm.Messaging.Tests</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\prepay_timeout.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\locked.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\multiStacked.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\multiDeliveryStacked.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\deliveriesStacked.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ILoyPOS1.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>if $(ConfigurationName) == Release (
   if exist fuelDataAccess.dll.config del fuelDataAccess.dll.config
)</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>