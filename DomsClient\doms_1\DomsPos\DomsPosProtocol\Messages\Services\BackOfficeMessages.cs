﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Back Office Record Messages
    public class BackOfficeRecordRequest : JplRequest
    {
        public class BackOfficeRecordData
        {
            // Empty for basic requests
        }
    }

    public class BackOfficeRecordResponse : JplResponse
    {
        public class BackOfficeRecordResponseData
        {
            [JsonPropertyName("BorSeqNo")]
            public string BorSeqNo { get; set; }

            [JsonPropertyName("BorLen")]
            public int BorLen { get; set; }

            [JsonPropertyName("BorLength")]
            public int BorLength { get; set; }

            [JsonPropertyName("BorFormatId")]
            public EnumValue<string> BorFormatId { get; set; }

            [JsonPropertyName("BorFields")]
            public List<BorField> BorFields { get; set; }

            [JsonPropertyName("BorData")]
            public string BorData { get; set; }
        }

        public class BorField
        {
            [JsonPropertyName("BorFieldBytes")]
            public List<string> BorFieldBytes { get; set; }

            [JsonPropertyName("BorFieldBytes2")]
            public List<string> BorFieldBytes2 { get; set; }
        }
    }

    public class StoreBackOfficeRecordRequest : JplRequest
    {
        public class StoreBackOfficeRecordData
        {
            [JsonPropertyName("BorClientType")]
            public string BorClientType { get; set; }

            [JsonPropertyName("BorClientId")]
            public string BorClientId { get; set; }

            [JsonPropertyName("BorDataType")]
            public string BorDataType { get; set; }

            [JsonPropertyName("BorData")]
            public string BorData { get; set; }
        }
    }

    public class StoreBackOfficeRecordResponse : JplResponse
    {
        public class StoreBackOfficeRecordResponseData
        {
            [JsonPropertyName("BorSeqNo")]
            public string BorSeqNo { get; set; }
        }
    }
}
