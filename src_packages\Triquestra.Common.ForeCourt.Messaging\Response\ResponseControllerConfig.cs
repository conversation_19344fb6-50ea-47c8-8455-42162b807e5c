﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ResponseControllerConfig : CommandResponse
    {
        public string Version { get; private set; }

        public ResponseControllerConfig(int sequenceNo, int sourceId, int targetId,
            ForecourtCommandResults commandResult, string version)
            : base(sequenceNo, sourceId, targetId, commandResult, ForecourtCommandMessageTypes.CONTROLLER_CONFIG)
        {
            Version = version;
        }

        public static ResponseControllerConfig Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            var configNode = respNode.Element("ControllerConfig");
            if (configNode == null)
                throw new XmlSchemaException("Command Response does not have <ControllerConfig> node");
            return new ResponseControllerConfig(seqNo, sourceId, targetId, result,
                configNode.Attribute("Version").Value);
        }
    }
}
