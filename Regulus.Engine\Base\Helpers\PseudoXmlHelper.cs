﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Base.Helpers
{
    class PseudoXmlHelper
    {
        /// <summary>
        /// Get the value between opening and closing XML tags
        /// </summary>
        /// <param name="xmlText"></param>
        /// <param name="xmlField"></param>
        /// <returns></returns>
        public static string GetXMLField(string xmlText, string xmlField)
        {
            if (string.IsNullOrEmpty(xmlText))
                return null;
            if (string.IsNullOrEmpty(xmlField))
                throw new ArgumentException("The field cannot be null or empty", "xmlField");

            int i1 = xmlText.IndexOf("<" + xmlField + ">");
            if (-1 == i1)
                return null;
            
            i1 += xmlField.Length + 2;

            int i2 = xmlText.IndexOf("</" + xmlField + ">", i1 + 1);
            if (-1 == i2)
                return null;
            return xmlText.Substring(i1, i2 - i1);
        }
    }
}
