﻿using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.PriceDisplay
{
    public class PriceDisplayStatusRequest : JplRequest
    {
        public class PriceDisplayStatusRequestData
        {
            [JsonPropertyName("PriceDisplayId")]
            public string PriceDisplayId { get; set; }
        }
    }

    public class PriceDisplayStatusResponse : JplResponse
    {
        public class PriceDisplayStatusResponseData
        {
            [JsonPropertyName("PriceDisplayId")]
            public string PriceDisplayId { get; set; }
            
            [JsonPropertyName("PriceDisplayState")]
            public EnumValue<string> PriceDisplayState { get; set; }
            
            [JsonPropertyName("PriceDisplayStatus")]
            public BitFlags PriceDisplayStatus { get; set; }
        }
    }
}
