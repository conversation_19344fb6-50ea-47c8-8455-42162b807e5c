using System;
using System.Collections.Generic;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Reflection;
using Newtonsoft.Json;

namespace JPL_Demo_POS_CSharp
{
    public class Forecourt
    {
        public enum DeviceTypes
        {
            FUELLING_POINT,
            TANK_GAUGE
        }

        public delegate void OperationCompletedDelegate(bool success, object data, Exception ex);

        private class WorkerQueueItem
        {
            public enum Actions
            {
                FC_LOGON,
                HEARTBEAT,
                READ_PRICES,
                SET_PRICES,
                AUTH_FP,
                DEAUTH_FP,
                LOCK_FP_TRANS,
                UNLOCK_FP_TRANS,
                CLEAR_FP_TRANS,
                READ_FP_ERROR,
                CLEAR_FP_ERROR,
                READ_FP_TOTALS,
                READ_FUELLING_DATA,
                HANDLE_BACKOFFICE_RECORDS,
                READ_TG_DATA,
                READ_TANK_DELIVERY_DATA,
                CLEAR_DELIVERY_REPORT,
                SEND_USER_DEFINED_REQUEST
            }

            public Actions Action { get; private set; }
            public object Params { get; private set; }
            public OperationCompletedDelegate OperationCompletedCallBack { get; private set; }

            public WorkerQueueItem(Actions action, object parameters, OperationCompletedDelegate operationCompletedCallBack = null)
            {
                this.Action = action;
                this.Params = parameters;
                this.OperationCompletedCallBack = operationCompletedCallBack;
            }
        }

        private const int INTERVAL_BETWEEN_FUELLINGDATA_READINGS = 1000;

        #region Member Variables
        private ForecourtComm _forecourtComm;
        private Queue _fcWorkerQueue;
        private Queue _fcLowPriorityWorkerQueue;
        private AutoResetEvent _newItemInqueueEvent;
        private DevicesDataSet _definedFuellingPoints = new DevicesDataSet();
        private DevicesDataSet _definedTankGauges = new DevicesDataSet();
        private bool _isLoggedOn;
        private Dictionary<string, string> _grades;
        private Dictionary<int, FpStatusRespType> _cachedFpStatusResponses = new Dictionary<int, FpStatusRespType>();
        private bool _pendingBackOfficeRecordClear;
        private Timer _timer;
        #endregion

        #region Events definitions
        public event Action<DeviceTypes, int> DeviceRemoved;
        public event Action<DeviceTypes, int> DeviceConfigured;
        public event Action<int> InitFpTransactionList;
        public event Action<int, string, decimal, decimal, int, decimal, int> FpTransaction;
        public event Action<DeviceTypes, int, string, bool, object> DeviceStatusChanged;
        public event Action<int, int, string, bool> DeviceError;
        public event Action<bool> ConnectionClosed;
        public event Action ConnectionEstablished;
        public event Action<bool, string> OperationCompleted;
        public event Action<string> RequestSent;
        public event Action<string> ResponseReceived;
        #endregion

        public Forecourt()
        {
            _forecourtComm = new ForecourtComm();
            _forecourtComm.ConnectionLost += _ForecourtComm_ConnectionLost;
            _forecourtComm.RequestSent += _ForecourtComm_RequestSent;
            _forecourtComm.ResponseReceived += _ForecourtComm_ResponseReceived;
            _forecourtComm.UnsolicitedMsgReceived += _ForecourtComm_UnsolMsgReceived;

            _timer = new Timer(new TimerCallback(_HandleHeartBeat), null, 20000, 20000);

            // Timer for checking fuelling data
            Timer fuellingDataTimer = new Timer(new TimerCallback(_CheckFuellingDataTimer), null, INTERVAL_BETWEEN_FUELLINGDATA_READINGS, INTERVAL_BETWEEN_FUELLINGDATA_READINGS);

            Thread t = new Thread(new ThreadStart(_FcWorkerThread));

            _fcWorkerQueue = Queue.Synchronized(new Queue());
            _fcLowPriorityWorkerQueue = Queue.Synchronized(new Queue());

            _newItemInqueueEvent = new AutoResetEvent(false);

            t.IsBackground = true;
            t.Name = "FcThread";
            t.Start();
        }

        #region Forecourt Event handlers

        private void _HandleFcInstallStatus(string msg)
        {
            FcInstallStatusRespType fcInstallStatusResponse = JsonConvert.DeserializeObject<FcInstallStatusRespType>(msg);
            List<int> deviceList = new List<int>();

            foreach (var deviceGroup in fcInstallStatusResponse.data.InstalledFcDeviceGroups)
            {
                if (deviceGroup.ExtendedInstallMsgCode == "0010H" || deviceGroup.ExtendedInstallMsgCode == "0040H")
                {
                    deviceList.Clear();

                    foreach (string fcDeviceId in deviceGroup.FcDeviceId)
                    {
                        deviceList.Add(int.Parse(fcDeviceId));
                    }

                    if (deviceGroup.ExtendedInstallMsgCode == "0010H")
                    {
                        _CheckDeviceConfiguration(DeviceTypes.FUELLING_POINT, _definedFuellingPoints, deviceList);
                    }
                    else
                    {
                        _CheckDeviceConfiguration(DeviceTypes.TANK_GAUGE, _definedTankGauges, deviceList);
                    }
                }
            }
        }

        private void _CheckDeviceConfiguration(DeviceTypes deviceType, DevicesDataSet deviceDataset, List<int> deviceList)
        {
            // Reset the configured flag for all devices
            foreach (var deviceRow in deviceDataset.Devices.Rows)
            {
                deviceRow.Configured = false;
            }

            // Check all currently configured devices in the PSS
            foreach (int deviceID in deviceList)
            {
                var deviceRow = deviceDataset.Devices.FindByDeviceID(deviceID);

                if (deviceRow == null)
                {
                    deviceDataset.Devices.AddDevicesRow(deviceID, true);
                    DeviceConfigured?.Invoke(deviceType, deviceID);
                }
                else
                {
                    deviceRow.Configured = true;
                }
            }

            // Remove devices that are no longer configured
            var devicesToRemove = new List<DevicesDataSet.DevicesRow>();
            foreach (var deviceRow in deviceDataset.Devices.Rows)
            {
                if (!deviceRow.Configured)
                {
                    DeviceRemoved?.Invoke(deviceType, deviceRow.DeviceID);
                    devicesToRemove.Add(deviceRow);
                }
            }

            foreach (var deviceRow in devicesToRemove)
            {
                deviceDataset.Devices.Rows.Remove(deviceRow);
            }

            deviceDataset.Devices.AcceptChanges();
        }

        private void _HandleFpStatusChanged(string msg)
        {
            FpStatusRespType fpStatusResponse = JsonConvert.DeserializeObject<FpStatusRespType>(msg);
            bool isOnline = fpStatusResponse.data.FpSubStates.bits.IsOnline != 0;
            int fpId = int.Parse(fpStatusResponse.data.FpId);

            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses[fpId] = fpStatusResponse;
            }

            if (fpStatusResponse.data.FpSubStates.bits.IsInErrorState != 0)
            {
                _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_FP_ERROR, new { FpId = fpId, IsOnline = isOnline }));
            }
            else
            {
                if (fpStatusResponse.data.FpMainState.value != "09H") // Not in Fuelling state
                {
                    DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, fpId, fpStatusResponse.MainStateText(), isOnline, null);
                }
            }
        }

        private void _HandleTgStatusChanged(string msg)
        {
            TgStatusRespType tgStatusResponse = JsonConvert.DeserializeObject<TgStatusRespType>(msg);
            DeviceStatusChanged?.Invoke(DeviceTypes.TANK_GAUGE, int.Parse(tgStatusResponse.data.TgId), tgStatusResponse.MainStateText(), tgStatusResponse.data.TgSubStates.bits.TankGaugeOnline != 0, null);
        }

        private void _HandleFpTransBufChanged(string msg)
        {
            FpSupTransBufStatusRespType fpSupTransBufStatusResponse = JsonConvert.DeserializeObject<FpSupTransBufStatusRespType>(msg);
            int fpId = int.Parse(fpSupTransBufStatusResponse.data.FpId);
            string gradeName = "?????";
            decimal volume, money, unitPrice = 0;

            FpStatusRespType fpStatusResponse = null;
            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses.TryGetValue(fpId, out fpStatusResponse);
            }

            if (fpStatusResponse != null)
            {
                DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, fpId, fpStatusResponse.MainStateText(), fpStatusResponse.data.FpSubStates.bits.IsOnline != 0, null);
            }
            InitFpTransactionList?.Invoke(fpId);

            foreach (var transBufEntry in fpSupTransBufStatusResponse.data.TransInSupBuffer)
            {
                money = BCDBufToDecimal(transBufEntry.MoneyDue_e, Properties.Settings.Default.MoneyDecimalPointPosition);
                volume = BCDBufToDecimal(transBufEntry.Vol_e, Properties.Settings.Default.VolumeDecimalPointPosition);

                if (volume > 0M)
                    unitPrice = money / volume;

                if (_grades != null && _grades.ContainsKey(transBufEntry.FcGradeId))
                    gradeName = _grades[transBufEntry.FcGradeId];

                int transLockId = int.Parse(transBufEntry.TransLockId);
                int transSeqNo = int.Parse(transBufEntry.TransSeqNo);

                FpTransaction?.Invoke(fpId, gradeName, volume, money, transSeqNo, unitPrice, transLockId);

                if (transLockId == 0 && Properties.Settings.Default.AutoLockTransactions)
                    LockTransaction(fpId, transSeqNo);
            }
        }

        private void _ForecourtComm_UnsolMsgReceived(string msgName, string msg)
        {
            switch (msgName)
            {
                case "MultiMessage_resp":
                    MultiMessageRespType multiMsgResponse = JsonConvert.DeserializeObject<MultiMessageRespType>(msg);
                    foreach (var multiMsgResponseEntry in multiMsgResponse.data.messages)
                    {
                        _ForecourtComm_UnsolMsgReceived(multiMsgResponseEntry.name, JsonConvert.SerializeObject(multiMsgResponseEntry));
                    }
                    break;
                case "FcInstallStatus_resp":
                    _HandleFcInstallStatus(msg);
                    break;
                case "FpStatus_resp":
                    _HandleFpStatusChanged(msg);
                    break;
                case "TgStatus_resp":
                    _HandleTgStatusChanged(msg);
                    break;
                case "FpSupTransBufStatus_resp":
                    _HandleFpTransBufChanged(msg);
                    break;
                case "SiteDeliveryStatus_resp":
                    _HandleSiteDeliveryStatusChanged(msg);
                    break;
                case "FcStatus_resp":
                    HandleFcStatusChanged(msg);
                    break;
            }
        }

        private void _ForecourtComm_ConnectionLost()
        {
            _InitVariables();
            ConnectionClosed?.Invoke(true);
        }

        private void _ForecourtComm_RequestSent(string request)
        {
            RequestSent?.Invoke(request);
        }

        private void _ForecourtComm_ResponseReceived(string response)
        {
            ResponseReceived?.Invoke(response);
        }

        #endregion

        // Helper method to convert BCD buffer to decimal
        public static decimal BCDBufToDecimal(string bcdBuf, int decimalPointPosition)
        {
            if (string.IsNullOrEmpty(bcdBuf))
                return 0;

            try
            {
                // Convert hex string to byte array
                byte[] bcdBytes = new byte[bcdBuf.Length / 2];
                for (int i = 0; i < bcdBytes.Length; i++)
                {
                    bcdBytes[i] = Convert.ToByte(bcdBuf.Substring(i * 2, 2), 16);
                }

                // Convert BCD bytes to decimal
                long result = 0;
                for (int i = 0; i < bcdBytes.Length; i++)
                {
                    byte b = bcdBytes[i];
                    int highNibble = (b >> 4) & 0x0F;
                    int lowNibble = b & 0x0F;

                    if (highNibble > 9 || lowNibble > 9)
                        return 0; // Invalid BCD

                    result = result * 100 + highNibble * 10 + lowNibble;
                }

                return (decimal)result / (decimal)Math.Pow(10, decimalPointPosition);
            }
            catch
            {
                return 0;
            }
        }

        // Helper method to convert BCD buffer to date and time
        public static string BCDBufToDateAndTime(string bcdBuf)
        {
            if (string.IsNullOrEmpty(bcdBuf) || bcdBuf.Length < 14)
                return string.Empty;

            try
            {
                // BCD format: YYYYMMDDHHMMSS (7 bytes = 14 hex chars)
                string year = BCDToDecimalString(bcdBuf.Substring(0, 4));
                string month = BCDToDecimalString(bcdBuf.Substring(4, 2));
                string day = BCDToDecimalString(bcdBuf.Substring(6, 2));
                string hour = BCDToDecimalString(bcdBuf.Substring(8, 2));
                string minute = BCDToDecimalString(bcdBuf.Substring(10, 2));
                string second = BCDToDecimalString(bcdBuf.Substring(12, 2));

                return $"{year}-{month.PadLeft(2, '0')}-{day.PadLeft(2, '0')} {hour.PadLeft(2, '0')}:{minute.PadLeft(2, '0')}:{second.PadLeft(2, '0')}";
            }
            catch
            {
                return bcdBuf; // Return original if conversion fails
            }
        }

        private static string BCDToDecimalString(string bcdHex)
        {
            if (string.IsNullOrEmpty(bcdHex) || bcdHex.Length % 2 != 0)
                return "0";

            try
            {
                string result = "";
                for (int i = 0; i < bcdHex.Length; i += 2)
                {
                    byte b = Convert.ToByte(bcdHex.Substring(i, 2), 16);
                    int highNibble = (b >> 4) & 0x0F;
                    int lowNibble = b & 0x0F;

                    if (highNibble > 9 || lowNibble > 9)
                        return "0"; // Invalid BCD

                    result += highNibble.ToString() + lowNibble.ToString();
                }
                return result.TrimStart('0');
            }
            catch
            {
                return "0";
            }
        }

        private void _AddToFcWorkerQueue(WorkerQueueItem item)
        {
            _fcWorkerQueue.Enqueue(item);
            _newItemInqueueEvent.Set();
        }

        private void _InitVariables()
        {
            _isLoggedOn = false;
            _grades = null;
            _cachedFpStatusResponses.Clear();
            _pendingBackOfficeRecordClear = false;
        }

        private void _HandleSiteDeliveryStatusChanged(string msg)
        {
            SiteDeliveryStatusRespType siteDeliveryStatusResponse = JsonConvert.DeserializeObject<SiteDeliveryStatusRespType>(msg);

            if (Properties.Settings.Default.ClearDeliveryReports && siteDeliveryStatusResponse.data.DeliveryStatusFlags.bits.SiteDeliveryDataIsReady != 0)
            {
                _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.CLEAR_DELIVERY_REPORT,
                    new { DeliveryReportSeqNo = siteDeliveryStatusResponse.data.DeliveryReportSeqNo, TgId = siteDeliveryStatusResponse.data.TgId }, null));
            }
        }

        private void HandleFcStatusChanged(string msg)
        {
            FcStatusRespType fcStatusResponse = JsonConvert.DeserializeObject<FcStatusRespType>(msg);

            if (Properties.Settings.Default.ClearBackOfficeRecords && !_pendingBackOfficeRecordClear && fcStatusResponse.data.FcStatus2Flags.bits.BackOfficeRecordExists != 0)
            {
                _pendingBackOfficeRecordClear = true;
                _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.HANDLE_BACKOFFICE_RECORDS, null, null));
            }
        }

        private void _HandleHeartBeat(object state)
        {
            try
            {
                if (_isLoggedOn)
                    _forecourtComm.SendMessage(new HearBeatRespType(), false);
            }
            catch (Exception)
            {
                // Ignore heartbeat errors
            }
        }

        private void _FcWorkerThread()
        {
            while (true) // Loop forever (until application terminates)
            {
                if (_fcWorkerQueue.Count == 0 && _fcLowPriorityWorkerQueue.Count == 0)
                {
                    _newItemInqueueEvent.WaitOne(500); // Wait for something to do
                }

                while (_fcWorkerQueue.Count > 0) // Keep working until queue is empty
                {
                    _DoWork((WorkerQueueItem)_fcWorkerQueue.Dequeue()); // Get next worker queue item
                }

                while (_fcLowPriorityWorkerQueue.Count > 0) // Keep working until queue is empty
                {
                    _DoWork((WorkerQueueItem)_fcLowPriorityWorkerQueue.Dequeue()); // Get next worker queue item
                    if (_fcWorkerQueue.Count > 0) break; // Exit loop if there is something more important to do
                }
            }
        }

        private void _DoWork(WorkerQueueItem workerQueueItem)
        {
            try
            {
                if (!_isLoggedOn && workerQueueItem.Action != WorkerQueueItem.Actions.FC_LOGON)
                    return; // Skip all items except FC_LOGON if we're not logged in

                switch (workerQueueItem.Action)
                {
                    case WorkerQueueItem.Actions.FC_LOGON:
                        _FcLogon(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_PRICES:
                        _ReadPrices(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.SET_PRICES:
                        _SetPrices(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.AUTH_FP:
                        _CheckResponse(_forecourtComm.SendMessage(new FpAuthReqType((int)workerQueueItem.Params, Properties.Settings.Default.PosId)));
                        _CallCompletedHandler(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.DEAUTH_FP:
                        _CheckResponse(_forecourtComm.SendMessage(new FpCancelAuthReqType((int)workerQueueItem.Params, Properties.Settings.Default.PosId)));
                        _CallCompletedHandler(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_TG_DATA:
                        _ReadTgData(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_TANK_DELIVERY_DATA:
                        _ReadTankDeliveryData(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.CLEAR_DELIVERY_REPORT:
                        _ClearDeliveryReport(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_FP_ERROR:
                        _ReadFpError(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.CLEAR_FP_ERROR:
                        _ClearFpError(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_FUELLING_DATA:
                        _ReadFuellingData(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.READ_FP_TOTALS:
                        _ReadTotals(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.LOCK_FP_TRANS:
                        dynamic lockParams = workerQueueItem.Params;
                        _CheckResponse(_forecourtComm.SendMessage(new FpSupTransReqType(lockParams.FpId, lockParams.TransSeqNo, Properties.Settings.Default.PosId)));
                        _CallCompletedHandler(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.UNLOCK_FP_TRANS:
                        dynamic unlockParams = workerQueueItem.Params;
                        _CheckResponse(_forecourtComm.SendMessage(new FpUnLockSupTransReqType(unlockParams.FpId, unlockParams.TransSeqNo, Properties.Settings.Default.PosId)));
                        _CallCompletedHandler(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.CLEAR_FP_TRANS:
                        _ClearTransaction(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.HANDLE_BACKOFFICE_RECORDS:
                        _HandleBackOfficeRecords(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.SEND_USER_DEFINED_REQUEST:
                        _SendUserDefinedRequest(workerQueueItem);
                        break;

                    case WorkerQueueItem.Actions.HEARTBEAT:
                        _HandleHeartBeat(workerQueueItem);
                        break;
                }
            }
            catch (Exception ex)
            {
                if (workerQueueItem.OperationCompletedCallBack != null)
                    workerQueueItem.OperationCompletedCallBack(false, workerQueueItem.Params, ex);
            }
        }

        private void _CallCompletedHandler(WorkerQueueItem workerQueueItem)
        {
            if (workerQueueItem.OperationCompletedCallBack != null)
                workerQueueItem.OperationCompletedCallBack(true, workerQueueItem.Params, null);
        }

        private void _CheckResponse(ForecourtComm.Response response)
        {
            if (response.IsReject)
            {
                RejectMessageRespType rejectMessageResponse = JsonConvert.DeserializeObject<RejectMessageRespType>(response.Json);
                throw new Exception(rejectMessageResponse.data.RejectInfo);
            }
        }

        // Public methods
        public void FcLogon(string ipAddress, int posId, string logonString, int volDecimalPointPos, int priceDecimalPointPos, int amountDecimalPointPos, OperationCompletedDelegate callback = null)
        {
            var parameters = new { IPAddress = ipAddress, PosId = posId, LogonString = logonString, VolDecimalPointPos = volDecimalPointPos, PriceDecimalPointPos = priceDecimalPointPos, AmountDecimalPointPos = amountDecimalPointPos };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.FC_LOGON, parameters, callback));
        }

        public void LockTransaction(int fpId, int transSeqNo)
        {
            var parameters = new { FpId = fpId, TransSeqNo = transSeqNo };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.LOCK_FP_TRANS, parameters));
        }

        private void _FcLogon(WorkerQueueItem workerQueueItem)
        {
            try
            {
                // Suppress connection lost events because we are disconnecting on purpose
                _forecourtComm.ConnectionLost -= _ForecourtComm_ConnectionLost;

                dynamic parameters = workerQueueItem.Params;

                if (_isLoggedOn) // Were we already connected?
                {
                    _forecourtComm.Disconnect();
                    ConnectionClosed?.Invoke(false); // Tell the client that we disconnected
                }

                _InitVariables(); // Initialize variables

                _forecourtComm.Connect(parameters.IPAddress);

                List<FcLogonReqType.FcLogonReqDataType.UnsolgMsgType> unsolMsgList = null;

                if (!((string)parameters.LogonString).Contains("MFDR"))
                {
                    unsolMsgList = new List<FcLogonReqType.FcLogonReqDataType.UnsolgMsgType>
                    {
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8006H", "00H"), // FcStatus_resp
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8007H", "01H"), // FcInstallStatus_resp
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8015H", "03H"), // FpStatus_resp
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8016H", "03H"), // FpSupTransBufStatus_resp
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8042H", "00H"), // TgStatus_resp
                        new FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8046H", "00H")  // SiteDeliveryStatus_resp
                    };
                }

                var response = _forecourtComm.SendMessage(new FcLogonReqType(parameters.LogonString, "0045", "1234", new int[] { 2 }, unsolMsgList));

                if (!response.IsReject)
                {
                    _isLoggedOn = true; // Set flag telling that we're logged on
                    _CallCompletedHandler(workerQueueItem); // Tell client that we're done
                    ConnectionEstablished?.Invoke();

                    if (!response.IsReject) _ReadGradeNames();
                }
                else
                {
                    _forecourtComm.Disconnect();
                    RejectMessageRespType rejectResponse = JsonConvert.DeserializeObject<RejectMessageRespType>(response.Json);

                    if (!string.IsNullOrEmpty(rejectResponse.data.RejectInfoText))
                    {
                        throw new Exception($"Forecourt logon failed - {rejectResponse.data.RejectInfoText}");
                    }
                    else
                    {
                        throw new Exception("Forecourt logon failed");
                    }
                }
            }
            finally
            {
                _forecourtComm.ConnectionLost += _ForecourtComm_ConnectionLost;
            }
        }

        private void _ReadGradeNames()
        {
            var response = _forecourtComm.SendMessage(new PassthroughReqType(new byte[] { 0xFF, 0x0B, 0x00, 0x00, 0x04, 0x01, 0x00 }));

            _grades = new Dictionary<string, string>();

            if (!response.IsReject)
            {
                PassThroughRespType passThroughResponse = JsonConvert.DeserializeObject<PassThroughRespType>(response.Json);
                byte[] binaryResponse = passThroughResponse.ToByteArray();

                if (binaryResponse != null && binaryResponse.Length > 3)
                {
                    ushort msgCode = BitConverter.ToUInt16(binaryResponse, 1);

                    if (msgCode == 0x800B && binaryResponse.Length > 6)
                    {
                        var textEncoding = System.Text.Encoding.GetEncoding("ibm850"); // Grade names are using code page 850

                        for (int x = 0; x < binaryResponse[6]; x++) // Loop through the grades
                        {
                            int offset = 7 + x * 13;
                            if (offset + 12 < binaryResponse.Length)
                            {
                                string gradeId = $"{binaryResponse[offset]:x2}"; // Convert GradeId from BCD to hex
                                string gradeName = textEncoding.GetString(binaryResponse, offset + 1, 12).TrimEnd();

                                if (!_grades.ContainsKey(gradeId))
                                    _grades.Add(gradeId, gradeName);
                            }
                        }
                    }
                }
            }
        }

        public string LookUpGradeName(string gradeId)
        {
            if (_grades == null)
            {
                return "???";
            }
            else
            {
                return _grades.ContainsKey(gradeId) ? _grades[gradeId] : "???";
            }
        }

        // Additional public methods
        public void ReadPrices(OperationCompletedDelegate callback)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_PRICES, null, callback));
        }

        public void SetPrices(object prices, OperationCompletedDelegate callback = null)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.SET_PRICES, prices, callback));
        }

        public void AuthorizeFuellingPoint(int fpId, OperationCompletedDelegate callback = null)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.AUTH_FP, fpId, callback));
        }

        public void DeauthorizeFuellingPoint(int fpId, OperationCompletedDelegate callback = null)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.DEAUTH_FP, fpId, callback));
        }

        public void UnlockTransaction(int fpId, int transSeqNo, OperationCompletedDelegate callback = null)
        {
            var parameters = new { FpId = fpId, TransSeqNo = transSeqNo };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.UNLOCK_FP_TRANS, parameters, callback));
        }

        public void ClearTransaction(int fpId, int transSeqNo, OperationCompletedDelegate callback = null)
        {
            var parameters = new { FpId = fpId, TransSeqNo = transSeqNo };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.CLEAR_FP_TRANS, parameters, callback));
        }

        public void ReadTgData(int tgId, OperationCompletedDelegate callback)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_TG_DATA, tgId, callback));
        }

        public void ReadDeliveries(int tgId, OperationCompletedDelegate callback)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_TANK_DELIVERY_DATA, tgId, callback));
        }

        public void ClearFpError(int fpId, OperationCompletedDelegate callback = null)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.CLEAR_FP_ERROR, fpId, callback));
        }

        public void ReadTotals(int fpId, OperationCompletedDelegate callback)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_FP_TOTALS, fpId, callback));
        }

        public void SendUserDefinedRequest(string request, OperationCompletedDelegate callback)
        {
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.SEND_USER_DEFINED_REQUEST, request, callback));
        }

        public bool IsLoggedOn => _isLoggedOn;

        // Additional worker methods
        private void _ReadPrices(WorkerQueueItem workerQueueItem)
        {
            var response = _forecourtComm.SendMessage(new FcPriceSetReqType());
            _CheckResponse(response);

            if (workerQueueItem.OperationCompletedCallBack != null)
            {
                FcPriceSetRespType pricesResponse = JsonConvert.DeserializeObject<FcPriceSetRespType>(response.Json);
                workerQueueItem.OperationCompletedCallBack(true, pricesResponse, null);
            }
        }

        private void _SetPrices(WorkerQueueItem workerQueueItem)
        {
            var response = _forecourtComm.SendMessage(new ChangeFcPriceSetReqType((FcPriceSetDataType)workerQueueItem.Params));
            _CheckResponse(response);

            if (workerQueueItem.OperationCompletedCallBack != null)
                workerQueueItem.OperationCompletedCallBack(true, null, null);
        }

        private void _ReadTotals(WorkerQueueItem workerQueueItem)
        {
            int fpId = (int)workerQueueItem.Params;

            try
            {
                var response1 = _forecourtComm.SendMessage(new FpGradeTotalsReqType(fpId));
                var response2 = _forecourtComm.SendMessage(new PumpGradeTotalsReqType(fpId));

                _CheckResponse(response1);

                if (workerQueueItem.OperationCompletedCallBack != null)
                {
                    FpGradeTotalsRespType fpGradeTotalsResponse = JsonConvert.DeserializeObject<FpGradeTotalsRespType>(response1.Json);
                    PumpGradeTotalsRespType pumpGradeTotalsResponse = null;

                    if (!response2.IsReject)
                        pumpGradeTotalsResponse = JsonConvert.DeserializeObject<PumpGradeTotalsRespType>(response2.Json);

                    workerQueueItem.OperationCompletedCallBack(true, new { FpId = fpId, FpGradeTotalsResponse = fpGradeTotalsResponse, PumpGradeTotalsResponse = pumpGradeTotalsResponse }, null);
                }
            }
            catch (Exception ex)
            {
                workerQueueItem.OperationCompletedCallBack?.Invoke(false, new { FpId = fpId }, ex);
            }
        }

        private void _ReadFuellingData(WorkerQueueItem workerQueueItem)
        {
            dynamic parameters = workerQueueItem.Params;
            int fpId = parameters.FpId;

            FpStatusRespType fpStatusResponse = null;
            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses.TryGetValue(fpId, out fpStatusResponse);
            }

            if (fpStatusResponse != null && fpStatusResponse.data.FpMainState.value == "09H") // Fuelling state
            {
                var response = _forecourtComm.SendMessage(new FpFuellingDataReqType(fpId));

                if (!response.IsReject)
                {
                    FpFuellingDataRespType fuellingDataResponse = JsonConvert.DeserializeObject<FpFuellingDataRespType>(response.Json);

                    // Tell client what the status is including the fuelling data
                    var fuellingData = new
                    {
                        Volume_e = BCDBufToDecimal(fuellingDataResponse.data.Vol_e, Properties.Settings.Default.VolumeDecimalPointPosition),
                        Money_e = BCDBufToDecimal(fuellingDataResponse.data.Money_e, Properties.Settings.Default.MoneyDecimalPointPosition)
                    };

                    DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, fpId, fpStatusResponse.MainStateText(), fpStatusResponse.data.FpSubStates.bits.IsOnline != 0, fuellingData);
                }
            }
        }

        private void _ReadFpError(WorkerQueueItem workerQueueItem)
        {
            dynamic parameters = workerQueueItem.Params;
            int fpId = parameters.FpId;
            bool isOnline = parameters.IsOnline;

            string errorCode, errorText;
            _ReadFpError(fpId, out errorCode, out errorText);

            // Call callback supplying DeviceType, DeviceId, ErrorText, and whether or not device was online
            DeviceError?.Invoke((int)DeviceTypes.FUELLING_POINT, fpId, errorText, isOnline);
        }

        private void _ReadFpError(int fpId, out string errorCode, out string errorText)
        {
            var response = _forecourtComm.SendMessage(new FpErrorMsgReqType(fpId));
            FpErrorRespType fpErrorMsgResponse = JsonConvert.DeserializeObject<FpErrorRespType>(response.Json);

            errorCode = fpErrorMsgResponse.data.FpErrorCode.value;
            errorText = fpErrorMsgResponse.ErrorText();
        }

        private void _ClearFpError(WorkerQueueItem workerQueueItem)
        {
            int fpId = (int)workerQueueItem.Params;

            FpStatusRespType fpStatusResponse = null;
            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses.TryGetValue(fpId, out fpStatusResponse);
            }

            if (fpStatusResponse != null && fpStatusResponse.data.FpSubStates.bits.IsInErrorState != 0)
            {
                string errorCode, errorText;
                _ReadFpError(fpId, out errorCode, out errorText);
                if (errorCode != "00")
                    _forecourtComm.SendMessage(new FpClearErrorReqType(fpId, errorCode));
            }

            _CallCompletedHandler(workerQueueItem); // Tell client that we're done
        }

        private void _ClearTransaction(WorkerQueueItem workerQueueItem)
        {
            dynamic parameters = workerQueueItem.Params;
            var transParIdList = new List<string> { FpSupTransReqType.TransParIdType.Money_e, FpSupTransReqType.TransParIdType.Vol_e };
            var response = _forecourtComm.SendMessage(new FpSupTransReqType(parameters.FpId, parameters.TransSeqNo, 0, transParIdList));
            FpSupTransRespType fpSupTransResponse = JsonConvert.DeserializeObject<FpSupTransRespType>(response.Json);

            _CheckResponse(_forecourtComm.SendMessage(new FpClrSupTransBufReqType(parameters.FpId, parameters.TransSeqNo,
                fpSupTransResponse.data.TransPars.Vol_e, fpSupTransResponse.data.TransPars.Money_e, Properties.Settings.Default.PosId.ToString())));
        }

        private void _HandleBackOfficeRecords(WorkerQueueItem workerQueueItem)
        {
            try
            {
                bool moreBackOfficeRecordExists = false;

                do
                {
                    var response = _forecourtComm.SendMessage(new BackOfficeRecordReqType());

                    if (!response.IsReject)
                    {
                        BackOfficeRecordRespType backOfficeRecordResponse = JsonConvert.DeserializeObject<BackOfficeRecordRespType>(response.Json);
                        response = _forecourtComm.SendMessage(new ClearBackOfficeRecordReqType(backOfficeRecordResponse.data.BorSeqNo));

                        if (!response.IsReject)
                        {
                            ClearBackOfficeRecordRespType clearBackOfficeRecordResponse = JsonConvert.DeserializeObject<ClearBackOfficeRecordRespType>(response.Json);
                            moreBackOfficeRecordExists = clearBackOfficeRecordResponse.data.BorBufferStatus.bits.BufferNotEmpty != 0;
                        }
                    }
                } while (moreBackOfficeRecordExists);
            }
            catch (Exception)
            {
                // We might fail if we cannot deserialize BorData into a string. This might happen with some exotic PSS versions
            }
            finally
            {
                _pendingBackOfficeRecordClear = false;
            }
        }

        private void _SendUserDefinedRequest(WorkerQueueItem workerQueueItem)
        {
            var response = _forecourtComm.SendRawMessage((string)workerQueueItem.Params);
            workerQueueItem.OperationCompletedCallBack?.Invoke(true, new { Response = response }, null);
        }

        private void _ReadTgData(WorkerQueueItem workerQueueItem)
        {
            int tgId = (int)workerQueueItem.Params;
            var response = _forecourtComm.SendMessage(new TgDataReqType(tgId));
            _CheckResponse(response);

            TgDataRespType tgDataResponse = JsonConvert.DeserializeObject<TgDataRespType>(response.Json);

            // Call callback supplying TgId and TgData
            if (workerQueueItem.OperationCompletedCallBack != null)
                workerQueueItem.OperationCompletedCallBack(true, new { TgDataResponse = tgDataResponse }, null);
        }

        private void _ReadTankDeliveryData(WorkerQueueItem workerQueueItem)
        {
            int tgId = (int)workerQueueItem.Params;
            var response = _forecourtComm.SendMessage(new TankDeliveryDataReqType(tgId));
            _CheckResponse(response);

            TankDeliveryDataRespType deliveryDataResponse = JsonConvert.DeserializeObject<TankDeliveryDataRespType>(response.Json);

            // Call the callback supplying DeliveryData
            if (workerQueueItem.OperationCompletedCallBack != null)
                workerQueueItem.OperationCompletedCallBack(true, new { DeliveryDataResponse = deliveryDataResponse }, null);
        }

        private void _ClearDeliveryReport(WorkerQueueItem workerQueueItem)
        {
            dynamic parameters = workerQueueItem.Params;
            string deliveryReportSeqNo = parameters.DeliveryReportSeqNo;
            string tgId = parameters.TgId;

            try
            {
                var response = _forecourtComm.SendMessage(new ClearDeliveryReportReqType(tgId, deliveryReportSeqNo));

                if (response.IsReject)
                {
                    RejectMessageRespType rejectMessageResp = JsonConvert.DeserializeObject<RejectMessageRespType>(response.Json);
                    OperationCompleted?.Invoke(false, $"Delivery report {deliveryReportSeqNo} could not be cleared ({rejectMessageResp.data.RejectInfo})");
                }
                else
                {
                    OperationCompleted?.Invoke(true, $"Delivery report {deliveryReportSeqNo} was cleared successfully");
                }
            }
            catch (Exception ex)
            {
                OperationCompleted?.Invoke(false, $"Error clearing delivery report {deliveryReportSeqNo}: {ex.Message}");
            }
        }

        public void CheckFuellingData()
        {
            if (!_isLoggedOn) return;

            bool fuellingDataInFpStatus = Properties.Settings.Default.FcLogonString.Contains("MFDR");

            lock (_cachedFpStatusResponses)
            {
                foreach (var fpStatusResponse in _cachedFpStatusResponses.Values)
                {
                    if (fpStatusResponse.data.FpMainState.value == "09H") // Fuelling state
                    {
                        if (fuellingDataInFpStatus) // Check if fuelling data is supplied as part of the fuelling point status
                        {
                            object fuellingData = null;
                            if (!string.IsNullOrEmpty(fpStatusResponse.data.FpSupplStatusPars?.FuellingDataMon_e) &&
                                !string.IsNullOrEmpty(fpStatusResponse.data.FpSupplStatusPars?.FuellingDataVol_e))
                            {
                                fuellingData = new
                                {
                                    Volume_e = BCDBufToDecimal(fpStatusResponse.data.FpSupplStatusPars.FuellingDataVol_e, Properties.Settings.Default.VolumeDecimalPointPosition),
                                    Money_e = BCDBufToDecimal(fpStatusResponse.data.FpSupplStatusPars.FuellingDataMon_e, Properties.Settings.Default.MoneyDecimalPointPosition)
                                };
                            }

                            DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, int.Parse(fpStatusResponse.data.FpId), fpStatusResponse.MainStateText(), fpStatusResponse.data.FpSubStates.bits.IsOnline != 0, fuellingData);
                        }
                        else
                        {
                            _AddToLowPriorityFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_FUELLING_DATA, new { FpId = int.Parse(fpStatusResponse.data.FpId) }));
                        }
                    }
                }
            }
        }

        private void _AddToLowPriorityFcWorkerQueue(WorkerQueueItem item)
        {
            _fcLowPriorityWorkerQueue.Enqueue(item);
            _newItemInqueueEvent.Set();
        }

        private void _CheckFuellingDataTimer(object state)
        {
            try
            {
                CheckFuellingData();
            }
            catch (Exception)
            {
                // Ignore timer errors
            }
        }
    }
}
