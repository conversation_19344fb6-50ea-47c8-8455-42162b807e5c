﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryStateChangeMessage:ForecourtEventMessage
    {
        public int DeliveryId { get; set; }
        public int DispenserId { get { return SourceId; } set { SourceId = value; } }
        public DeliveryStates DeliveryState { get; set; }

        public EventDeliveryStateChangeMessage(int sequenceNo, int sourceId, int targetId, int deliveryId, DeliveryStates deliveryState) :
            base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_STATE_CHANGE)
        {
            DeliveryId = deliveryId;
            DeliveryState = deliveryState;
        }

        public static EventDeliveryStateChangeMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryStateChangeNode = eventNode.Element("DeliveryStateChange");
            if (deliveryStateChangeNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryStateChange> node");
            }
            var deliveryId = int.Parse(deliveryStateChangeNode.Attribute("DeliveryID").Value);
            var ds =
                (DeliveryStates)
                    Enum.Parse(typeof(DeliveryStates),
                        deliveryStateChangeNode.Attribute("State").Value);
            return new EventDeliveryStateChangeMessage(seqNo, sourceId, targetId, deliveryId, ds);

        }
    }
}
