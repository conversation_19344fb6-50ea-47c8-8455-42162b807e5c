﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ConfigResponse : ForecourtMessage
    {
        public ConfigResponse( ForecourtConfigurationTypes configType, ForecourtCommandResults commandResult)
            : base(ForecourtMessageClasses.CONFIG_RESPONSE, 0)
        {
            CommandResult = commandResult;
            ConfigType = configType;
        }

        public ForecourtCommandResults CommandResult { get; set; }

        public ForecourtConfigurationTypes ConfigType { get; set; }

        public static ForecourtMessage Deserialise(XContainer messageNode)
        {
            var respNode = messageNode.Element("ConfigResp");
            if (respNode == null)
                throw new XmlSchemaException("Node does not have <ConfigResp> node");
            if (respNode.Attribute("Result") == null)
                throw new XmlSchemaException("Command Response does not have <Result> node");
            if (respNode.Attribute("Type") == null)
                throw new XmlSchemaException("Command Response does not have <Code> node");
            
            ForecourtCommandResults result;

            
            try
            {
                result = (ForecourtCommandResults)Enum.Parse(typeof(ForecourtCommandResults), respNode.Attribute("Result").Value, true);
            }
            catch(ArgumentException)
            {
                throw new XmlSchemaException("Cannot parse Result node in Command Response.");
            }

            ForecourtConfigurationTypes type;

            try
            {
                type = (ForecourtConfigurationTypes)Enum.Parse(typeof(ForecourtConfigurationTypes), respNode.Attribute("Type").Value, true);
            }
            catch(ArgumentException)
            {
                throw new XmlSchemaException("Cannot parse Result node in Command Response.");
            }

            switch (type)
            {
                    case ForecourtConfigurationTypes.GET_AUTHMODE_PROFILES:
                    return ResponseProfilesConfig.Deserialise(messageNode, result);
            }

            return new ConfigResponse(type, result);
        }
    }
}
