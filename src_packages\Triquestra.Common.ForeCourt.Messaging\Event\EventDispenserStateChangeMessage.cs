﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDispenserStateChangeMessage:ForecourtEventMessage
    {
        public DispenserStates DispenserState { get; set; }
        public AuthModes AuthMode { get; set; }
        public int DispenserId { get { return SourceId; } set { SourceId = value; } }
        public EventDispenserStateChangeMessage(int sequenceNo, int sourceId, int targetId, DispenserStates dispenserState)
            : this(sequenceNo, sourceId, targetId, dispenserState, AuthModes.None)
        { }
        public EventDispenserStateChangeMessage(int sequenceNo, int sourceId, int targetId, DispenserStates dispenserState, AuthModes authMode) : 
            base(sequenceNo, sourceId, targetId, EventMessageTypes.DISPENSER_STATE_CHANGE)
        {
            DispenserState = dispenserState;
            AuthMode = authMode;
        }

        public static EventDispenserStateChangeMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var dispenserStateChangeNode = eventNode.Element("DispenserStateChange");
            if (dispenserStateChangeNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DispenserStateChange> node");
            }
            var dis =
                (DispenserStates)
                    Enum.Parse(typeof(DispenserStates),
                        dispenserStateChangeNode.Attribute("State").Value);
            var am = AuthModes.None;
            if (dispenserStateChangeNode.Attribute("Mode") != null)
            {
                am =
                (AuthModes)
                    Enum.Parse(typeof(AuthModes),
                        dispenserStateChangeNode.Attribute("Mode").Value);
            }
            return new EventDispenserStateChangeMessage(seqNo, sourceId, targetId, dis, am);
        }
    }
}
