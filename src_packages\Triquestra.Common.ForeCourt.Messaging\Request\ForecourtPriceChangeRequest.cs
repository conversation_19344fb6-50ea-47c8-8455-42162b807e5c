﻿using System.Collections.Generic;
using System.Xml.Linq;
using System.Linq;
namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class ForecourtPriceChangeRequest:ForecourtConfigRequest
    {
        public List<PriceChangeRequestModel> _priceChanges;

        public ForecourtPriceChangeRequest(int blendId, int priceId, decimal amount) 
            : base(ForecourtConfigurationTypes.SET_CONFIG)
        {
            _priceChanges = new List<PriceChangeRequestModel>();
            _priceChanges.Add(new PriceChangeRequestModel
            {
                BlendID = blendId,
                PriceID = priceId,
                TargetPrice = amount
            });
        }

        public ForecourtPriceChangeRequest(List<PriceChangeRequestModel> priceChanges)
            : base(ForecourtConfigurationTypes.SET_CONFIG)
        {
            _priceChanges = priceChanges;
        }


        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            var configReq = xdoc.Root.Element("ConfigReq");
            if (configReq == null) return xdoc;
            if (_priceChanges != null && _priceChanges.Any())
            {
                foreach (var change in _priceChanges)
                {
                    configReq.Add(new XElement("Blend",
                   new XAttribute("Action", "UPDATE"),
                   new XAttribute("BlendID", change.BlendID),
                   new XElement("Price",
                       new XAttribute("Action", "UPDATE"),
                       new XAttribute("Amount", change.TargetPrice),
                       new XAttribute("PriceID", change.PriceID))
                   ));
                }
            }

            return xdoc;
        }


    }
}
