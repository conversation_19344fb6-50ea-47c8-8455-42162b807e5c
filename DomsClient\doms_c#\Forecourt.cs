using System;
using System.Collections.Generic;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Reflection;
using Newtonsoft.Json;

namespace JPL_Demo_POS_CSharp
{
    public class Forecourt
    {
        public enum DeviceTypes
        {
            FUELLING_POINT,
            TANK_GAUGE
        }

        public delegate void OperationCompletedDelegate(bool success, object data, Exception ex);

        private class WorkerQueueItem
        {
            public enum Actions
            {
                FC_LOGON,
                HEARTBEAT,
                READ_PRICES,
                SET_PRICES,
                AUTH_FP,
                DEAUTH_FP,
                LOCK_FP_TRANS,
                UNLOCK_FP_TRANS,
                CLEAR_FP_TRANS,
                READ_FP_ERROR,
                CLEAR_FP_ERROR,
                READ_FP_TOTALS,
                READ_FUELLING_DATA,
                HANDLE_BACKOFFICE_RECORDS,
                READ_TG_DATA,
                READ_TANK_DELIVERY_DATA,
                CLEAR_DELIVERY_REPORT,
                SEND_USER_DEFINED_REQUEST
            }

            public Actions Action { get; private set; }
            public object Params { get; private set; }
            public OperationCompletedDelegate OperationCompletedCallBack { get; private set; }

            public WorkerQueueItem(Actions action, object parameters, OperationCompletedDelegate operationCompletedCallBack = null)
            {
                this.Action = action;
                this.Params = parameters;
                this.OperationCompletedCallBack = operationCompletedCallBack;
            }
        }

        private const int INTERVAL_BETWEEN_FUELLINGDATA_READINGS = 1000;

        #region Member Variables
        private ForecourtComm _forecourtComm;
        private Queue _fcWorkerQueue;
        private Queue _fcLowPriorityWorkerQueue;
        private AutoResetEvent _newItemInqueueEvent;
        private DevicesDataSet _definedFuellingPoints = new DevicesDataSet();
        private DevicesDataSet _definedTankGauges = new DevicesDataSet();
        private bool _isLoggedOn;
        private Dictionary<string, string> _grades;
        private Dictionary<int, FpStatusRespType> _cachedFpStatusResponses = new Dictionary<int, FpStatusRespType>();
        private bool _pendingBackOfficeRecordClear;
        private Timer _timer;
        #endregion

        #region Events definitions
        public event Action<DeviceTypes, int> DeviceRemoved;
        public event Action<DeviceTypes, int> DeviceConfigured;
        public event Action<int> InitFpTransactionList;
        public event Action<int, string, decimal, decimal, int, decimal, int> FpTransaction;
        public event Action<DeviceTypes, int, string, bool, object> DeviceStatusChanged;
        public event Action<int, int, string, bool> DeviceError;
        public event Action<bool> ConnectionClosed;
        public event Action ConnectionEstablished;
        public event Action<bool, string> OperationCompleted;
        public event Action<string> RequestSent;
        public event Action<string> ResponseReceived;
        #endregion

        public Forecourt()
        {
            _forecourtComm = new ForecourtComm();
            _forecourtComm.ConnectionLost += _ForecourtComm_ConnectionLost;
            _forecourtComm.RequestSent += _ForecourtComm_RequestSent;
            _forecourtComm.ResponseReceived += _ForecourtComm_ResponseReceived;
            _forecourtComm.UnsolicitedMsgReceived += _ForecourtComm_UnsolMsgReceived;

            _timer = new Timer(new TimerCallback(_HandleHeartBeat), null, 20000, 20000);

            Thread t = new Thread(new ThreadStart(_FcWorkerThread));

            _fcWorkerQueue = Queue.Synchronized(new Queue());
            _fcLowPriorityWorkerQueue = Queue.Synchronized(new Queue());

            _newItemInqueueEvent = new AutoResetEvent(false);

            t.IsBackground = true;
            t.Name = "FcThread";
            t.Start();
        }

        #region Forecourt Event handlers

        private void _HandleFcInstallStatus(string msg)
        {
            FcInstallStatusRespType fcInstallStatusResponse = JsonConvert.DeserializeObject<FcInstallStatusRespType>(msg);
            List<int> deviceList = new List<int>();

            foreach (var deviceGroup in fcInstallStatusResponse.data.InstalledFcDeviceGroups)
            {
                if (deviceGroup.ExtendedInstallMsgCode == "0010H" || deviceGroup.ExtendedInstallMsgCode == "0040H")
                {
                    deviceList.Clear();

                    foreach (string fcDeviceId in deviceGroup.FcDeviceId)
                    {
                        deviceList.Add(int.Parse(fcDeviceId));
                    }

                    if (deviceGroup.ExtendedInstallMsgCode == "0010H")
                    {
                        _CheckDeviceConfiguration(DeviceTypes.FUELLING_POINT, _definedFuellingPoints, deviceList);
                    }
                    else
                    {
                        _CheckDeviceConfiguration(DeviceTypes.TANK_GAUGE, _definedTankGauges, deviceList);
                    }
                }
            }
        }

        private void _CheckDeviceConfiguration(DeviceTypes deviceType, DevicesDataSet deviceDataset, List<int> deviceList)
        {
            // Reset the configured flag for all devices
            foreach (var deviceRow in deviceDataset.Devices.Rows)
            {
                deviceRow.Configured = false;
            }

            // Check all currently configured devices in the PSS
            foreach (int deviceID in deviceList)
            {
                var deviceRow = deviceDataset.Devices.FindByDeviceID(deviceID);

                if (deviceRow == null)
                {
                    deviceDataset.Devices.AddDevicesRow(deviceID, true);
                    DeviceConfigured?.Invoke(deviceType, deviceID);
                }
                else
                {
                    deviceRow.Configured = true;
                }
            }

            // Remove devices that are no longer configured
            var devicesToRemove = new List<DevicesDataSet.DevicesRow>();
            foreach (var deviceRow in deviceDataset.Devices.Rows)
            {
                if (!deviceRow.Configured)
                {
                    DeviceRemoved?.Invoke(deviceType, deviceRow.DeviceID);
                    devicesToRemove.Add(deviceRow);
                }
            }

            foreach (var deviceRow in devicesToRemove)
            {
                deviceDataset.Devices.Rows.Remove(deviceRow);
            }

            deviceDataset.Devices.AcceptChanges();
        }

        private void _HandleFpStatusChanged(string msg)
        {
            FpStatusRespType fpStatusResponse = JsonConvert.DeserializeObject<FpStatusRespType>(msg);
            bool isOnline = fpStatusResponse.data.FpSubStates.bits.IsOnline != 0;
            int fpId = int.Parse(fpStatusResponse.data.FpId);

            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses[fpId] = fpStatusResponse;
            }

            if (fpStatusResponse.data.FpSubStates.bits.IsInErrorState != 0)
            {
                _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.READ_FP_ERROR, new { FpId = fpId, IsOnline = isOnline }));
            }
            else
            {
                if (fpStatusResponse.data.FpMainState.value != "09H") // Not in Fuelling state
                {
                    DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, fpId, fpStatusResponse.MainStateText(), isOnline, null);
                }
            }
        }

        private void _HandleTgStatusChanged(string msg)
        {
            TgStatusRespType tgStatusResponse = JsonConvert.DeserializeObject<TgStatusRespType>(msg);
            DeviceStatusChanged?.Invoke(DeviceTypes.TANK_GAUGE, int.Parse(tgStatusResponse.data.TgId), tgStatusResponse.MainStateText(), tgStatusResponse.data.TgSubStates.bits.TankGaugeOnline != 0, null);
        }

        private void _HandleFpTransBufChanged(string msg)
        {
            FpSupTransBufStatusRespType fpSupTransBufStatusResponse = JsonConvert.DeserializeObject<FpSupTransBufStatusRespType>(msg);
            int fpId = int.Parse(fpSupTransBufStatusResponse.data.FpId);
            string gradeName = "?????";
            decimal volume, money, unitPrice = 0;

            FpStatusRespType fpStatusResponse = null;
            lock (_cachedFpStatusResponses)
            {
                _cachedFpStatusResponses.TryGetValue(fpId, out fpStatusResponse);
            }

            if (fpStatusResponse != null)
            {
                DeviceStatusChanged?.Invoke(DeviceTypes.FUELLING_POINT, fpId, fpStatusResponse.MainStateText(), fpStatusResponse.data.FpSubStates.bits.IsOnline != 0, null);
            }
            InitFpTransactionList?.Invoke(fpId);

            foreach (var transBufEntry in fpSupTransBufStatusResponse.data.TransInSupBuffer)
            {
                money = BCDBufToDecimal(transBufEntry.MoneyDue_e, Properties.Settings.Default.MoneyDecimalPointPosition);
                volume = BCDBufToDecimal(transBufEntry.Vol_e, Properties.Settings.Default.VolumeDecimalPointPosition);

                if (volume > 0M)
                    unitPrice = money / volume;

                if (_grades != null && _grades.ContainsKey(transBufEntry.FcGradeId))
                    gradeName = _grades[transBufEntry.FcGradeId];

                int transLockId = int.Parse(transBufEntry.TransLockId);
                int transSeqNo = int.Parse(transBufEntry.TransSeqNo);

                FpTransaction?.Invoke(fpId, gradeName, volume, money, transSeqNo, unitPrice, transLockId);

                if (transLockId == 0 && Properties.Settings.Default.AutoLockTransactions)
                    LockTransaction(fpId, transSeqNo);
            }
        }

        private void _ForecourtComm_UnsolMsgReceived(string msgName, string msg)
        {
            switch (msgName)
            {
                case "MultiMessage_resp":
                    MultiMessageRespType multiMsgResponse = JsonConvert.DeserializeObject<MultiMessageRespType>(msg);
                    foreach (var multiMsgResponseEntry in multiMsgResponse.data.messages)
                    {
                        _ForecourtComm_UnsolMsgReceived(multiMsgResponseEntry.name, JsonConvert.SerializeObject(multiMsgResponseEntry));
                    }
                    break;
                case "FcInstallStatus_resp":
                    _HandleFcInstallStatus(msg);
                    break;
                case "FpStatus_resp":
                    _HandleFpStatusChanged(msg);
                    break;
                case "TgStatus_resp":
                    _HandleTgStatusChanged(msg);
                    break;
                case "FpSupTransBufStatus_resp":
                    _HandleFpTransBufChanged(msg);
                    break;
                case "SiteDeliveryStatus_resp":
                    _HandleSiteDeliveryStatusChanged(msg);
                    break;
                case "FcStatus_resp":
                    HandleFcStatusChanged(msg);
                    break;
            }
        }

        private void _ForecourtComm_ConnectionLost()
        {
            _InitVariables();
            ConnectionClosed?.Invoke(true);
        }

        private void _ForecourtComm_RequestSent(string request)
        {
            RequestSent?.Invoke(request);
        }

        private void _ForecourtComm_ResponseReceived(string response)
        {
            ResponseReceived?.Invoke(response);
        }

        #endregion

        // Helper method to convert BCD buffer to decimal
        public static decimal BCDBufToDecimal(string bcdBuf, int decimalPointPosition)
        {
            if (string.IsNullOrEmpty(bcdBuf))
                return 0;

            // This is a simplified implementation - the actual BCD conversion would be more complex
            if (decimal.TryParse(bcdBuf, out decimal result))
            {
                return result / (decimal)Math.Pow(10, decimalPointPosition);
            }
            return 0;
        }

        // Helper method to convert BCD buffer to date and time
        public static string BCDBufToDateAndTime(string bcdBuf)
        {
            if (string.IsNullOrEmpty(bcdBuf))
                return string.Empty;

            // This is a simplified implementation - the actual BCD conversion would be more complex
            return bcdBuf;
        }

        private void _AddToFcWorkerQueue(WorkerQueueItem item)
        {
            _fcWorkerQueue.Enqueue(item);
            _newItemInqueueEvent.Set();
        }

        private void _InitVariables()
        {
            _isLoggedOn = false;
            _grades = null;
            _cachedFpStatusResponses.Clear();
            _pendingBackOfficeRecordClear = false;
        }

        // Placeholder methods - these would need to be implemented based on the VB.NET version
        private void _HandleSiteDeliveryStatusChanged(string msg) { }
        private void HandleFcStatusChanged(string msg) { }
        private void _HandleHeartBeat(object state) { }
        private void _FcWorkerThread() { }

        // Public methods
        public void FcLogon(string ipAddress, int posId, string logonString, int volDecimalPointPos, int priceDecimalPointPos, int amountDecimalPointPos, OperationCompletedDelegate callback = null)
        {
            var parameters = new { IPAddress = ipAddress, PosId = posId, LogonString = logonString, VolDecimalPointPos = volDecimalPointPos, PriceDecimalPointPos = priceDecimalPointPos, AmountDecimalPointPos = amountDecimalPointPos };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.FC_LOGON, parameters, callback));
        }

        public void LockTransaction(int fpId, int transSeqNo)
        {
            var parameters = new { FpId = fpId, TransSeqNo = transSeqNo };
            _AddToFcWorkerQueue(new WorkerQueueItem(WorkerQueueItem.Actions.LOCK_FP_TRANS, parameters));
        }

        public string LookUpGradeName(string gradeId)
        {
            if (_grades == null)
            {
                return "???";
            }
            else
            {
                return _grades.ContainsKey(gradeId) ? _grades[gradeId] : "???";
            }
        }
    }
}
