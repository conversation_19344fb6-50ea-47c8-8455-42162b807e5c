﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common;
using Triquestra.Common.GUI;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using Triquestra.Common.PumpEsm.Helpers;

namespace Triquestra.Common.PumpEsm
{
    public class GuiHelper: IGuiHelper
    {
        public void ShowMessage(string text,MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            ShowMessage(text, MessageBoxButtons.OK, icon);
        }

        public DialogResult ShowMessage(string text, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            NLog.LogManager.GetCurrentClassLogger().Debug("Dialog shown: " + text);
            return Dialogs.ShowMessage(Mode.Till, text, "Infinity Point Of Sale", buttons, icon, true);
        }

        public void ShowError(string text, MessageBoxIcon messageBoxIcon)
        {
            Dialogs.ShowMessage(Mode.Till, text + Environment.NewLine + "Please refer to support.", "Infinity Point Of Sale", System.Windows.Forms.MessageBoxButtons.OK, messageBoxIcon,true);
        }

        [DllImport("user32.dll")]
        private extern static IntPtr SetActiveWindow(IntPtr handle);
        [DllImport("user32.dll")]
        static extern int GetWindowThreadProcessId(IntPtr hWnd, IntPtr ProcessId);
        [DllImport("kernel32.dll")]
        static extern int GetCurrentThreadId();
        [DllImport("user32.dll")]
        static extern bool AttachThreadInput(int idAttach, int idAttachTo,
           bool fAttach);
        /// <summary>
        /// Return focus to another window
        /// </summary>
        /// <param name="targetHwnd"></param>
        public void SetWindowActive(IntPtr targetHwnd)
        {
            //https://msdn.microsoft.com/en-us/library/ms810439.aspx
            //http://stackoverflow.com/questions/2671669/is-there-a-reliable-way-to-activate-set-focus-to-a-window-using-c            
            var currentThreadId = GetCurrentThreadId();
            var otherThreadId = GetWindowThreadProcessId(targetHwnd, IntPtr.Zero);
            if (otherThreadId == 0) return;
            if (otherThreadId != currentThreadId)
            {   
#if DEBUG
                var res =
#endif
                AttachThreadInput(currentThreadId, otherThreadId, true);
            }

            SetActiveWindow(targetHwnd);

            if (otherThreadId != currentThreadId)
            {
                AttachThreadInput(currentThreadId, otherThreadId, false);
            }
        }

    }
}
