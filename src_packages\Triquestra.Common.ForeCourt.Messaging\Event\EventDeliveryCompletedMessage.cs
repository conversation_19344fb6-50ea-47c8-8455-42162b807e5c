﻿using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryCompletedMessage : ForecourtEventMessage
    {
        public IDelivery Delivery { get; set; }

        public EventDeliveryCompletedMessage(int sequenceNo, int sourceId, int targetId, IDelivery delivery)
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_COMPLETE)
        {
            Delivery = delivery;
        }

        public static EventDeliveryCompletedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryStartedNode = eventNode.Element("DeliveryComplete");

            if (deliveryStartedNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryComplete> node");
            }
            return
                deliveryStartedNode.Descendants("Delivery")
                    .Select(del => Messaging.Delivery.Parse(del, sourceId))
                    .Select(delivery => new EventDeliveryCompletedMessage(seqNo, sourceId, targetId, delivery))
                    .FirstOrDefault();
        }
    }
}
