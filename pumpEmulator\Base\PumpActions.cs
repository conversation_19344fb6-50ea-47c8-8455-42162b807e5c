﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Base
{
    public enum PumpActions
    {
        Unknown,
        /// <summary>
        /// POS passes its scan box window handle
        /// </summary>
        SetScanBoxHandle = 'W',
        /// <summary>
        /// show pump form
        /// </summary>
        ShowPumpForm = 'S',
        /// <summary>
        /// hide pump form
        /// </summary>
        HidePumpForm = 'H',
        /// <summary>
        /// ???
        /// </summary>
        LogonAllowed = 'L',
        /// <summary>
        /// ????
        /// </summary>
        PumpFormOnTop = 'T'
    }
}
