﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class DeliveryLockResponse : CommandResponse
    {
        public int DeliveryId { get; set; }
        public int DispenserId { get; set; }

        public DeliveryLockResponse(int sequenceNo, int sourceId, int targetId, ForecourtCommandResults commandResult, int deliveryId)
            : base(sequenceNo, sourceId, targetId, commandResult, ForecourtCommandMessageTypes.DELIVERY_LOCK)
        {
            DeliveryId = deliveryId;
            DispenserId = targetId;
        }

        public static DeliveryLockResponse Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
//<FCCMessage>
//  <Header MessageType="COMMAND_RESPONSE" SeqNo="0" SourceID="84" TargetID="2" />
//  <CommandResp Code="DELIVERY_LOCK" Result="DELIVERY_ALREADY_LOCKED" DeliveryID="67" />
//</FCCMessage>
            var deliveryId = int.Parse(respNode.Attribute("DeliveryID").Value);
            return new DeliveryLockResponse(seqNo, sourceId, targetId, result, deliveryId);
        }        
    }
}
