using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class MainForm : Form
    {
        private class TransInfo
        {
            public int TransSeqNo { get; private set; }
            public int LockId { get; private set; }
            public int FpId { get; private set; }

            public TransInfo(int fpId, int transSeqNo, int lockId)
            {
                this.FpId = fpId;
                this.TransSeqNo = transSeqNo;
                this.LockId = lockId;
            }
        }

        #region Member variables
        private Forecourt _forecourt;
        public JplMessagesForm _frmJsonMessageDump;
        public JplConsoleForm _frmJsonConsole;
        #endregion

        #region Constants
        private const string TRANS_PREFIX_TEXT = "TRANS_";
        private const string FP_PREFIX_TEXT = "FP_";
        private const string TG_PREFIX_TEXT = "TG_";
        private const string DELIVERYDATA_NODE_TEXT = "Delivery Data";
        private const string TGDATA_NODE_TEXT = "Tank Gauge Data";

        private const int ONLINE_IMAGEINDEX = 3;
        private const int OFFLINE_IMAGEINDEX = 4;
        private const int TRANS_IMAGEINDEX = 5;
        private const int DELIVERY_DATA_IMAGEINDEX = 6;
        private const int TG_DATA_IMAGEINDEX = 6;
        #endregion

        public MainForm()
        {
            InitializeComponent();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            _forecourt = new Forecourt();

            // Subscribe to forecourt events
            _forecourt.DeviceConfigured += Forecourt_DeviceConfigChanged;
            _forecourt.DeviceRemoved += _Forecourt_DeviceRemoved;
            _forecourt.DeviceStatusChanged += Forecourt_DeviceStatusChanged;
            _forecourt.DeviceError += Forecourt_DeviceError;
            _forecourt.InitFpTransactionList += Forecourt_InitFpTransactionList;
            _forecourt.FpTransaction += _Forecourt_FpTransaction;
            _forecourt.ConnectionClosed += Forecourt_ConnectionClosed;
            _forecourt.OperationCompleted += _Forecourt_OperationCompleted;

            // Initialize TreeView with root nodes
            if (tvForecourt.Nodes.Count == 0)
            {
                tvForecourt.Nodes.Add("Fuelling Points");
                tvForecourt.Nodes.Add("Tank Gauges");
            }
            tvForecourt.ExpandAll();
            btnFCLogon.Enabled = true;

            if (Properties.Settings.Default.AutoLogon)
                btnFCLogon_Click(null, null);
        }

        #region Completion handlers

        private void FcLogonCompleted(bool success, object data, Exception ex)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool, object, Exception>(FcLogonCompleted), success, data, ex);
            }
            else
            {
                if (success)
                {
                    SetButtonState(true);
                }
                else
                {
                    ShowErrorMsgBox("Logon failed.", ex);
                }
            }
        }

        #endregion

        #region Forecourt Event Handlers

        private void _Forecourt_OperationCompleted(bool success, string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool, string>(_Forecourt_OperationCompleted), success, message);
            }
            else
            {
                MessageBox.Show(message, success ? "Information" : "Error", MessageBoxButtons.OK, success ? MessageBoxIcon.Information : MessageBoxIcon.Exclamation);
            }
        }

        private void Forecourt_DeviceError(int deviceType, int deviceId, string errorText, bool isOnline)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int, int, string, bool>(Forecourt_DeviceError), deviceType, deviceId, errorText, isOnline);
            }
            else
            {
                TreeNode node = GetDeviceNode((Forecourt.DeviceTypes)deviceType, deviceId);

                if (node != null)
                {
                    node.Text = string.Format("{0} - {1}", deviceId, errorText);
                    node.ImageIndex = isOnline ? ONLINE_IMAGEINDEX : OFFLINE_IMAGEINDEX;
                    node.SelectedImageIndex = node.ImageIndex;
                    node.BackColor = Color.Red;
                    node.ForeColor = Color.White;
                }
            }
        }

        private void Forecourt_DeviceStatusChanged(Forecourt.DeviceTypes deviceType, int deviceId, string statusText, bool isOnline, object fuellingData)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<Forecourt.DeviceTypes, int, string, bool, object>(Forecourt_DeviceStatusChanged), deviceType, deviceId, statusText, isOnline, fuellingData);
            }
            else
            {
                TreeNode node = GetDeviceNode(deviceType, deviceId);

                if (node != null)
                {
                    if (fuellingData != null)
                    {
                        dynamic data = fuellingData;
                        string s = string.Format("Volume: {0} Money: {1}",
                            FormatNumber(data.Volume_e, Properties.Settings.Default.VolumeDecimalPointPosition),
                            FormatNumber(data.Money_e, Properties.Settings.Default.MoneyDecimalPointPosition));
                        node.Text = string.Format("{0} - {1} ({2})", deviceId, statusText, s);
                    }
                    else
                    {
                        node.Text = string.Format("{0} - {1}", deviceId, statusText);
                    }

                    node.ImageIndex = isOnline ? ONLINE_IMAGEINDEX : OFFLINE_IMAGEINDEX;
                    node.SelectedImageIndex = node.ImageIndex;
                    node.BackColor = Color.White;
                    node.ForeColor = Color.Black;
                }
            }
        }

        private void Forecourt_DeviceConfigChanged(Forecourt.DeviceTypes deviceType, int deviceId)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<Forecourt.DeviceTypes, int>(Forecourt_DeviceConfigChanged), deviceType, deviceId);
            }
            else
            {
                TreeNode deviceTypeNode = GetDeviceRootNode(deviceType);

                if (deviceTypeNode != null)
                {
                    TreeNode deviceNode = deviceTypeNode.Nodes.Add(GetDeviceNodeName(deviceType, deviceId), deviceId.ToString());
                    deviceNode.Tag = deviceId;
                    deviceTypeNode.ExpandAll();
                }
            }
        }

        private void _Forecourt_DeviceRemoved(Forecourt.DeviceTypes deviceType, int deviceId)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<Forecourt.DeviceTypes, int>(_Forecourt_DeviceRemoved), deviceType, deviceId);
            }
            else
            {
                TreeNode deviceNode = GetDeviceNode(deviceType, deviceId);
                if (deviceNode != null)
                    this.tvForecourt.Nodes.Remove(deviceNode);
            }
        }

        private void Forecourt_InitFpTransactionList(int fpId)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int>(Forecourt_InitFpTransactionList), fpId);
            }
            else
            {
                TreeNode node = tvForecourt.SelectedNode;
                if (node != null && node.Name.StartsWith(TRANS_PREFIX_TEXT))
                    tvForecourt.SelectedNode = node.Parent;

                node = GetDeviceNode(Forecourt.DeviceTypes.FUELLING_POINT, fpId);

                if (node != null)
                {
                    node.Nodes.Clear();

                    var itemsToRemove = lvReceipt.Items.Cast<ListViewItem>().Where(lvi => lvi.Name == fpId.ToString()).ToList();
                    foreach (var lvi in itemsToRemove)
                    {
                        lvReceipt.Items.Remove(lvi);
                    }

                    btnSaleOK.Enabled = lvReceipt.Items.Count > 0;
                }
            }
        }

        private void _Forecourt_FpTransaction(int fpId, string gradeName, decimal volume, decimal money, int transSeqNo, decimal unitPrice, int lockId)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int, string, decimal, decimal, int, decimal, int>(_Forecourt_FpTransaction), fpId, gradeName, volume, money, transSeqNo, unitPrice, lockId);
            }
            else
            {
                TreeNode deviceNode = GetDeviceNode(Forecourt.DeviceTypes.FUELLING_POINT, fpId);

                if (deviceNode != null)
                {
                    TreeNode transactionNode;
                    if (volume < 0M)
                    {
                        transactionNode = deviceNode.Nodes.Add(string.Format("{0}{1}", TRANS_PREFIX_TEXT, transSeqNo),
                            string.Format("Money: {0}{1}", FormatNumber(money, Properties.Settings.Default.MoneyDecimalPointPosition),
                            lockId != 0 ? string.Format("  (locked by {0})", lockId) : string.Empty));
                    }
                    else
                    {
                        transactionNode = deviceNode.Nodes.Add(string.Format("{0}{1}", TRANS_PREFIX_TEXT, transSeqNo),
                            string.Format("Volume: {0} Money: {1}{2}",
                            FormatNumber(volume, Properties.Settings.Default.VolumeDecimalPointPosition),
                            FormatNumber(money, Properties.Settings.Default.MoneyDecimalPointPosition),
                            lockId != 0 ? string.Format("  (locked by {0})", lockId) : string.Empty));
                    }

                    TransInfo transInfo = new TransInfo(fpId, transSeqNo, lockId);
                    transactionNode.Tag = transInfo;
                    transactionNode.ImageIndex = TRANS_IMAGEINDEX;
                    transactionNode.SelectedImageIndex = TRANS_IMAGEINDEX;

                    deviceNode.ExpandAll();

                    if (lockId == Properties.Settings.Default.PosId)
                    {
                        ListViewItem lvi = lvReceipt.Items.Add(fpId.ToString());
                        lvi.Name = fpId.ToString();
                        lvi.SubItems.Add(gradeName);
                        lvi.SubItems.Add(FormatNumber(volume, Properties.Settings.Default.VolumeDecimalPointPosition));
                        lvi.SubItems.Add(FormatNumber(unitPrice, Properties.Settings.Default.PriceDecimalPointPosition));
                        lvi.SubItems.Add(FormatNumber(money, Properties.Settings.Default.MoneyDecimalPointPosition));
                        lvi.Tag = transInfo;

                        btnSaleOK.Enabled = true;
                    }
                }
            }
        }

        private void Forecourt_ConnectionClosed(bool wasLost)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<bool>(Forecourt_ConnectionClosed), wasLost);
            }
            else
            {
                DisableButtons();
                SetButtonState(false);

                lvReceipt.Items.Clear();

                TreeNode node = GetDeviceRootNode(Forecourt.DeviceTypes.FUELLING_POINT);
                if (node != null) node.Nodes.Clear();

                node = GetDeviceRootNode(Forecourt.DeviceTypes.TANK_GAUGE);
                if (node != null) node.Nodes.Clear();

                if (wasLost)
                    MessageBox.Show("Connection was lost. Try FcLogon again.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
        }

        #endregion

        #region UI event Handlers

        private void btnFCLogon_Click(object sender, EventArgs e)
        {
            var settings = Properties.Settings.Default;
            if (string.IsNullOrEmpty(settings.IPAddress))
            {
                if (MessageBox.Show("IP address of PSS has not been configured. Do you want to configure it now?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    btnOptions_Click(null, null);
                }
            }

            if (!string.IsNullOrEmpty(settings.IPAddress))
            {
                _forecourt.FcLogon(settings.IPAddress, settings.PosId, settings.FcLogonString, settings.VolumeDecimalPointPosition, settings.PriceDecimalPointPosition, settings.MoneyDecimalPointPosition, FcLogonCompleted);
            }
        }

        #endregion

        #region Helper methods

        private TreeNode GetDeviceNode(Forecourt.DeviceTypes deviceType, int deviceId)
        {
            TreeNode rootNode = GetDeviceRootNode(deviceType);
            if (rootNode != null)
            {
                string nodeName = GetDeviceNodeName(deviceType, deviceId);
                foreach (TreeNode node in rootNode.Nodes)
                {
                    if (node.Name == nodeName)
                        return node;
                }
            }
            return null;
        }

        private TreeNode GetDeviceRootNode(Forecourt.DeviceTypes deviceType)
        {
            if (tvForecourt.Nodes.Count >= 2)
            {
                return deviceType == Forecourt.DeviceTypes.FUELLING_POINT ?
                    tvForecourt.Nodes[0] : tvForecourt.Nodes[1];
            }
            return null;
        }

        private string GetDeviceNodeName(Forecourt.DeviceTypes deviceType, int deviceId)
        {
            return deviceType == Forecourt.DeviceTypes.FUELLING_POINT ? FP_PREFIX_TEXT + deviceId : TG_PREFIX_TEXT + deviceId;
        }

        private void SetButtonState(bool enabled)
        {
            btnchangePrices.Enabled = enabled;
            btnLockTrans.Enabled = enabled;
            btnUnlock.Enabled = enabled;
            btnReadTgData.Enabled = enabled;
            btnAuthorize.Enabled = enabled;
            btnDeauthorize.Enabled = enabled;
            btnClearError.Enabled = enabled;
            btnReadTotals.Enabled = enabled;
            btnReadDeliveries.Enabled = enabled;
            btnFCLogon.Enabled = !enabled;
        }

        private void DisableButtons()
        {
            SetButtonState(false);
            btnSaleOK.Enabled = false;
        }

        private void ShowErrorMsgBox(string message, Exception ex)
        {
            string fullMessage = message;
            if (ex != null)
                fullMessage += "\n\n" + ex.Message;
            MessageBox.Show(fullMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private string FormatNumber(decimal number, int decimalPlaces)
        {
            return number.ToString("F" + decimalPlaces);
        }

        private void btnOptions_Click(object sender, EventArgs e)
        {
            using (var optionsForm = new OptionsForm())
            {
                if (optionsForm.ShowDialog(this) == DialogResult.OK)
                {
                    // Settings have been saved
                }
            }
        }

        #endregion
    }
}
