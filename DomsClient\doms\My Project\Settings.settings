﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="PosId" Type="System.Byte" Scope="User">
      <Value Profile="(Default)">25</Value>
    </Setting>
    <Setting Name="IPAddress" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="VolumeDecimalPointPosition" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="PriceDecimalPointPosition" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">3</Value>
    </Setting>
    <Setting Name="MoneyDecimalPointPosition" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="FcLogonString" Type="System.String" Scope="User">
      <Value Profile="(Default)">POS,RI,UNSO_TRBUFSTA_3,UNSO_INSTSTA_1,UNSO_FPSTA_3:MFDR=01</Value>
    </Setting>
    <Setting Name="ClearDeliveryReports" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ShowHeartBeatMessages" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ShowUnsolicitedMessages" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="CurrentRequest" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="AutoLogon" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="PortNumber" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">8888</Value>
    </Setting>
    <Setting Name="ClearBackOfficeRecords" Type="System.String" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="AutoLockTransactions" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
  </Settings>
</SettingsFile>