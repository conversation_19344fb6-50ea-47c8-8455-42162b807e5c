﻿using System;

namespace DomsIntegration.Core.Models
{
    /// <summary>
    /// Fuelling point main states
    /// </summary>
    public enum FpMainState
    {
        Unconfigured,
        Closed,
        Idle,
        Error,
        Calling,
        PreAuthorized,
        Starting,
        Starting_paused,
        Starting_terminated,
        Fuelling,
        Fuelling_paused,
        Fuelling_terminated,
        Unavailable,
        Unavailable_and_calling
    }

    /// <summary>
    /// Tank gauge main states
    /// </summary>
    public enum TgMainState
    {
        Unconfigured,
        Operative,
        Alarm,
        Error
    }

    /// <summary>
    /// EPT main states
    /// </summary>
    public enum EptMainState
    {
        Unconfigured,
        Closed,
        Error,
        Idle,
        Busy
    }

    /// <summary>
    /// EPT validation states
    /// </summary>
    public enum EptValidationState
    {
        IDLE,
        BUSY,
        REQUEST,
        COMPLETED,
        CANCELLED
    }

    /// <summary>
    /// Preset types for fuelling points
    /// </summary>
    public enum PresetType
    {
        NoPreset,
        VolumePreset,
        MoneyPreset,
        FloorLimitPreset
    }

    /// <summary>
    /// Service mode categories
    /// </summary>
    public enum ServiceModeCategory
    {
        PostPayment = 1,
        PrePayment = 2,
        AttendantPayment = 3,
        NoPayment = 4,
        CardPayment = 5,
        BankNotePayment = 6
    }
}
