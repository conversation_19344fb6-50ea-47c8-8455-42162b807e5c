﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Triquestra.Common.GUI;
using Triquestra.Common.GUI.POSThemes;
using Triquestra.Common.PumpEsm.Controls;
using Triquestra.Common.PumpEsm.Messaging;

namespace WinFormUi.Controls
{
    public partial class StackDeliveriesForm : Form
    {
        private DispenserUi _parentDispenser;
        private List<DeliveryUi> _deliveries;
        private POSThemeSettings _themeSettings;

        public Mode GuiMode { get; set; }

        public StackDeliveriesForm(DispenserUi parent)
        {
            InitializeComponent();
            _themeSettings = new POSThemeSettings();
            _parentDispenser = parent;
            dgv.EnableHeadersVisualStyles = false;
            dgv.ColumnHeadersHeight = 40;
            dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
        }

        public void SetDeliveries(List<DeliveryUi> deliveries)
        {
            _deliveries = deliveries;
            if (deliveries != null)
            {
                var pumpId = deliveries.Select(d => d.DispenserId).FirstOrDefault();
                lblPump.Text = string.Format("Pump {0}", pumpId);
                dgv.DataSource = deliveries.Select(d => new DeliverySimple {
                                                    DeliveryId= d.DeliveryId,
                                                    Amount = Math.Round(d.Amount, 2),
                                                    Volume = Math.Round(d.Volume, 3),
                                                    BlendName= d.BlendName,
                                                    State = d.State }).ToList();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    switch (column.Name)
                    {
                        case "DeliveryId":
                        case "State":
                            column.Visible = false;
                            break;
                        case "Amount":
                            column.HeaderText = "Amount ($)";
                            column.Width = 120;
                            break;
                        case "Volume":
                            column.HeaderText = "Volume (LT)";
                            column.Width = 120;
                            break;
                        case "BlendName":
                            column.HeaderText = "Grade";
                            column.Width = 135;
                            break;
                    }
                }

                RefreshDataColor();
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            if (dgv.Focused && dgv.SelectedRows.Count > 0)
            {
                var selectedDeliveryId = (dgv.SelectedRows[0].DataBoundItem as DeliverySimple).DeliveryId;
                var selectedDelivery = _deliveries.Where(d => d.DeliveryId == selectedDeliveryId).SingleOrDefault();  // we need get delivery from _deliveries as it has latest State
                if (selectedDelivery == null || selectedDelivery.State == DeliveryStates.DELETING || selectedDelivery.State == DeliveryStates.DELIVERY_DELETED)
                {
                    Dialogs.ShowMessage(GuiMode, "The fuel delivery has already been finalized by a station!", "", MessageBoxButtons.OK, MessageBoxIcon.Warning, true);
                    return;
                }

                if (selectedDelivery.State == DeliveryStates.FINALIZING || selectedDelivery.State == DeliveryStates.REFUND_FINALIZING)
                {
                    Dialogs.ShowMessage(GuiMode, "The delivery has already been locked by a station!", "", MessageBoxButtons.OK, MessageBoxIcon.Warning, true);
                    return;
                }

                var delivery = _deliveries.Where(d => d.DeliveryId == selectedDelivery.DeliveryId).SingleOrDefault();
                _parentDispenser.SendDeliveryToSale(delivery);

                this.Close();  // close the form
            }
        }

        private void StackDeliveriesForm_Shown(object sender, EventArgs e)
        {
            ThemeManager.ApplyThemeToForm(this, POSThemeFormType.Customer);
            this.BackColor = this.lblPump.BackColor = Color.Black;
            RefreshDataColor();
        }

        private void RefreshDataColor()
        {
            foreach (DataGridViewRow row in dgv.Rows)
            {
                row.Height = 40;
                var delivery = row.DataBoundItem as DeliverySimple;
                if (delivery.State == DeliveryStates.FINALIZING || delivery.State == DeliveryStates.REFUND_FINALIZING)
                    row.DefaultCellStyle.ForeColor = Color.Red;
            }
        }

        private void StackDeliveriesForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            this.Hide();
            this.Parent = null;
            e.Cancel = true;
        }

        private void dgv_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            dgv.ClearSelection();
        }

        //private void dgv_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        //{
        //    dgv.Rows[e.RowIndex].Height = 40;
        //}
    }

    internal class DeliverySimple
    {
        public int DeliveryId { get; set; }
        public decimal Amount { get; set; }
        public decimal Volume { get; set; }
        public string BlendName { get; set; }
        public DeliveryStates State { get; set; }
    }
}
