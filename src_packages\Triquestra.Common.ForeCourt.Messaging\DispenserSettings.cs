﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public class DispenserSettings:IDispenserSettings
    {

        public int DispenserId { get; set; }

        public int StackSize { get; set; }

        public DispenserModes DispenserMode { get; set; }

        public List<IForecourtBlend> Blends { get; private set; }

        public DispenserSettings(int dispenserId, int stackSize, DispenserModes dispenserMode)
        {
            DispenserId = dispenserId;
            StackSize = stackSize;
            DispenserMode = dispenserMode;
            Blends=new List<IForecourtBlend>();
           
        }

        public static DispenserSettings Deserialise(XElement element)
        {
            //<DispenserSettings DispenserID='2' StackSize='1' Mode='AUTO_AUTH'>
            if (element.Attribute("DispenserID") == null) throw new XmlSchemaException("<DispenserSettings> node does not have <DispenserID> attribute");
            if (element.Attribute("StackSize") == null) throw new XmlSchemaException("<DispenserSettings> node does not have <StackSize> attribute");
            if (element.Attribute("Mode") == null) throw new XmlSchemaException("<DispenserSettings> node does not have <Mode> attribute");
            var s = new DispenserSettings(int.Parse(element.Attribute("DispenserID").Value),
                int.Parse(element.Attribute("StackSize").Value),
                (DispenserModes)Enum.Parse(typeof(DispenserModes), element.Attribute("Mode").Value));
            var nid = 1;
            foreach (var blend in element.Descendants("Blend").Select(ForecourtBlend.Deserialise))
            {
                blend.NozzleId = nid;
                nid++;
                s.Blends.Add(blend);
            }

            return s;
        }
    }
}
