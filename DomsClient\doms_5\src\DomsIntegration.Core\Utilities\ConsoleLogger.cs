﻿using System;

namespace DomsIntegration.Core.Utilities
{
    /// <summary>
    /// Console implementation of ILogger
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        private readonly ConsoleColor _originalColor;

        public ConsoleLogger()
        {
            _originalColor = Console.ForegroundColor;
        }

        public void LogDebug(string message)
        {
            WriteWithColor($"[DEBUG] {DateTime.Now:HH:mm:ss} {message}", ConsoleColor.Gray);
        }

        public void LogInfo(string message)
        {
            WriteWithColor($"[INFO]  {DateTime.Now:HH:mm:ss} {message}", ConsoleColor.White);
        }

        public void LogWarning(string message)
        {
            WriteWithColor($"[WARN]  {DateTime.Now:HH:mm:ss} {message}", ConsoleColor.Yellow);
        }

        public void LogError(string message)
        {
            WriteWithColor($"[ERROR] {DateTime.Now:HH:mm:ss} {message}", ConsoleColor.Red);
        }

        public void LogError(string message, Exception exception)
        {
            WriteWithColor($"[ERROR] {DateTime.Now:HH:mm:ss} {message} - {exception}", ConsoleColor.Red);
        }

        private void WriteWithColor(string message, ConsoleColor color)
        {
            lock (this)
            {
                Console.ForegroundColor = color;
                Console.WriteLine(message);
                Console.ForegroundColor = _originalColor;
            }
        }
    }
}
