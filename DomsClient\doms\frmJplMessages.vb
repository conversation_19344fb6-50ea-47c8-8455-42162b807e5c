﻿Imports Newtonsoft.Json

Public Class frmJplMessages

  Private OkToAdd As Boolean

  Public Class JsonDumpEntry
    Public TimeStamp As Date
    Public JsonMessage As String
    Public Prefix As String

    Public Overrides Function toString() As String
      Dim s As String

      If JsonMessage.Length > 400 Then
        s = JsonMessage.Substring(0, 400) + " ..."
      Else
        s = JsonMessage
      End If

      Return String.Format("{0:T}.{1:000} {2}: {3}", TimeStamp, TimeStamp.Millisecond, Prefix, s)
    End Function

    Public Sub New(Prefix As String, JsonMessage As String)
      Me.Prefix = Prefix
      Me.JsonMessage = JsonMessage
      Me.TimeStamp = Date.Now
    End Sub

  End Class

  Delegate Sub AddToListDelegate(ListEntry As JsonDumpEntry)

  Public Sub AddToList(ListEntry As JsonDumpEntry)
    If Not My.Settings.ShowHeartBeatMessages AndAlso ListEntry.JsonMessage.ToLower.Contains("heartbeat") Then Return
    If Not My.Settings.ShowUnsolicitedMessages AndAlso ListEntry.JsonMessage.Contains("solicited"":false") Then Return

    If InvokeRequired Then
      Invoke(New AddToListDelegate(AddressOf AddToList), New Object() {ListEntry})
    Else
      Dim i As Integer = lbMessages.Items.Add(ListEntry)
      OkToAdd = False
      lbMessages.SetSelected(i, True)
      lbMessages.SetSelected(i, False)
      OkToAdd = True
    End If
  End Sub

  Private Sub lbMessages_SelectedValueChanged(sender As Object, e As EventArgs) Handles lbMessages.SelectedValueChanged
    Dim JsonMsg As String
    Dim Obj As Object

    If OkToAdd AndAlso lbMessages.SelectedItem IsNot Nothing Then
      JsonMsg = DirectCast(lbMessages.SelectedItem, JsonDumpEntry).JsonMessage
      Obj = JsonConvert.DeserializeObject(JsonMsg)
      txtInspect.Text = JsonConvert.SerializeObject(Obj, Formatting.Indented)
    End If
  End Sub

  Private Sub lbMessages_DoubleClick(sender As Object, e As EventArgs) Handles lbMessages.DoubleClick
    If MsgBox("Remove all messages from the list?", MsgBoxStyle.YesNo Or MsgBoxStyle.Question) = MsgBoxResult.Yes Then lbMessages.Items.Clear()
  End Sub

  Private Sub txtInspect_DoubleClick(sender As Object, e As EventArgs) Handles txtInspect.DoubleClick
    Dim f As frmJplConsole = DirectCast(Me.Owner, frmMain)._frmJsonConsole
    If f IsNot Nothing Then f.SetRequest(txtInspect.Text)
  End Sub

  Private Sub txtInspect_KeyDown(sender As Object, e As KeyEventArgs) Handles txtInspect.KeyDown
    If (e.KeyCode And Not Keys.Modifiers) = Keys.A AndAlso e.Modifiers = Keys.Control Then
      DirectCast(sender, TextBox).SelectAll()
      e.Handled = True
    End If
  End Sub

End Class