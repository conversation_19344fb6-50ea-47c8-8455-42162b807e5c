using System;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class JplMessagesForm : Form
    {
        public JplMessagesForm()
        {
            InitializeComponent();
        }

        private void JplMessagesForm_Load(object sender, EventArgs e)
        {
            // Initialize messages display
            txtMessages.Text = "";
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtMessages.Clear();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        public void AddRequest(string request)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AddRequest), request);
            }
            else
            {
                txtMessages.AppendText($"[{DateTime.Now:HH:mm:ss.fff}] REQUEST: {request}\r\n");
                txtMessages.ScrollToCaret();
            }
        }

        public void AddResponse(string response)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(AddResponse), response);
            }
            else
            {
                txtMessages.AppendText($"[{DateTime.Now:HH:mm:ss.fff}] RESPONSE: {response}\r\n");
                txtMessages.ScrollToCaret();
            }
        }
    }
}
