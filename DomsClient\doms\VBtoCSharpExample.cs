using System;
using System.Collections.Generic;

// VB Class:
// Public Class Customer
//     Private _name As String
//     Private _orders As New List(Of String)
//
//     Public Sub New(name As String)
//         _name = name
//     End Sub
//
//     Public Property Name() As String
//         Get
//             Return _name
//         End Get
//         Set(value As String)
//             _name = value
//         End Set
//     End Property
//
//     Public Sub AddOrder(orderNumber As String)
//         _orders.Add(orderNumber)
//     End Sub
//
//     Public Function GetOrderCount() As Integer
//         Return _orders.Count
//     End Function
// End Class

// Converted to C#:
public class Customer
{
    private string _name;
    private List<string> _orders = new List<string>();

    public Customer(string name)
    {
        _name = name;
    }

    public string Name
    {
        get { return _name; }
        set { _name = value; }
    }

    public void AddOrder(string orderNumber)
    {
        _orders.Add(orderNumber);
    }

    public int GetOrderCount()
    {
        return _orders.Count;
    }
}
