﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Models;

namespace DomsIntegration.Communication
{
    /// <summary>
    /// Interface for communication with DOMs controller
    /// </summary>
    public interface IDomsClient : IDisposable
    {
        /// <summary>
        /// Gets the current connection status
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Gets or sets the connection timeout in milliseconds
        /// </summary>
        int ConnectionTimeout { get; set; }

        /// <summary>
        /// Event raised when connection status changes
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// Event raised when a message is received
        /// </summary>
        event EventHandler<MessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// Establishes connection to the DOMs controller
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default);

        /// <summary>
        /// Disconnects from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the disconnection operation</returns>
        Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends a message to the DOMs controller
        /// </summary>
        /// <typeparam name="TResponse">Expected response type</typeparam>
        /// <param name="request">Request message</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response message</returns>
        Task<TResponse> SendMessageAsync<TResponse>(IDomsMessage request, CancellationToken cancellationToken = default)
            where TResponse : class, IDomsMessage;

        /// <summary>
        /// Sends a message without expecting a response
        /// </summary>
        /// <param name="message">Message to send</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the send operation</returns>
        Task SendMessageAsync(IDomsMessage message, CancellationToken cancellationToken = default);
    }
}
