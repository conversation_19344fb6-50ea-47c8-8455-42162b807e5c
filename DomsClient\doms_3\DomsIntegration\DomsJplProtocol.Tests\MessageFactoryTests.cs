﻿using System;
using System.Text.Json;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Utilities;

namespace DomsJplProtocol.Tests
{
    [TestClass]
    public class MessageFactoryTests
    {
        [TestInitialize]
        public void Initialize()
        {
            // Initialize the message factory and register all message types
            MessageRegistry.RegisterAllMessageTypes();
        }

        [TestMethod]
        public void DeserializeMessage_FcLogonResponse_ShouldReturnCorrectType()
        {
            // Arrange
            string json = @"{
                ""name"": ""FcLogon_resp"",
                ""subCode"": ""00H"",
                ""solicited"": true,
                ""data"": {
                    ""CountryCode"": ""0000"",
                    ""FcHwType"": 1,
                    ""FcHwVersionNo"": ""12345678"",
                    ""FcSwType"": 1,
                    ""FcSwVersionNo"": ""12345678"",
                    ""FcSwDate"": ""12345678"",
                    ""FcSwBlocks"": [
                        {
                            ""FcSwMainBlockId"": ""01"",
                            ""FcSwSubBlockId"": ""02"",
                            ""FcSwBlockReleaseNo"": ""1234"",
                            ""FcSwBlockCheckCode"": ""ABCD""
                        }
                    ]
                }
            }";

            // Act
            var message = JplMessageFactory.DeserializeMessage(json);

            // Assert
            Assert.IsNotNull(message);
            Assert.IsInstanceOfType(message, typeof(FcLogonResponse));
            
            var response = message as FcLogonResponse;
            Assert.AreEqual(JplConstants.MessageNames.FC_LOGON_RESP, response.Name);
            Assert.AreEqual(JplConstants.SubCodes.SUBC_00H, response.SubCode);
            Assert.IsTrue(response.Solicited);
        }

        [TestMethod]
        public void DeserializeMessage_UnknownMessage_ShouldReturnGenericType()
        {
            // Arrange
            string json = @"{
                ""name"": ""Unknown_resp"",
                ""subCode"": ""00H"",
                ""solicited"": true,
                ""data"": { }
            }";

            // Act
            var message = JplMessageFactory.DeserializeMessage(json);

            // Assert
            Assert.IsNotNull(message);
            Assert.IsInstanceOfType(message, typeof(JplResponse));
        }
        
        [TestMethod]
        public void CreateRequest_ShouldSetCorrectNameAndSubCode()
        {
            // Act
            var request = JplMessageFactory.CreateRequest<FcStatusRequest>();
            
            // Assert
            Assert.IsNotNull(request);
            Assert.AreEqual("FcStatus_req", request.Name);
            Assert.AreEqual(JplConstants.SubCodes.SUBC_00H, request.SubCode);
        }
    }
}
