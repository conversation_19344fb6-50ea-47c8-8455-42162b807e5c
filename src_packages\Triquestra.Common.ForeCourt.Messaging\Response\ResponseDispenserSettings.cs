﻿using System.Xml.Linq;
using System.Xml.Schema;
using System.Linq;
using System.Collections.Generic;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ResponseDispenserSettings:CommandResponse
    {
        public List<IDispenserSettings> DispenserConfig { get; set; }

        public ResponseDispenserSettings(int sequenceNo, int sourceId, int targetId, ForecourtCommandResults commandResult, IEnumerable<IDispenserSettings> dispenserConfig)
            : base(sequenceNo, sourceId, targetId, commandResult, ForecourtCommandMessageTypes.DISPENSER_CONFIG)
        {
            DispenserConfig = new List<IDispenserSettings>();
            if (dispenserConfig != null)
                DispenserConfig.AddRange(dispenserConfig);
        }

        public static ResponseDispenserSettings Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            var dispenserConfigNode = respNode.Element("DispenserConfig");
            if (dispenserConfigNode == null)
                throw new XmlSchemaException("Command Response does not have <DispenserConfig> node");
            var dispenserSettingsNode = dispenserConfigNode.Descendants("DispenserSettings");
            if (dispenserSettingsNode == null)
                throw new XmlSchemaException("DispenserConfig does not have <DispenserSettings> node");
            var settings = dispenserSettingsNode.Select(DispenserSettings.Deserialise).ToArray();
            return new ResponseDispenserSettings(seqNo, sourceId, targetId, result, settings);

        }
    }
}
