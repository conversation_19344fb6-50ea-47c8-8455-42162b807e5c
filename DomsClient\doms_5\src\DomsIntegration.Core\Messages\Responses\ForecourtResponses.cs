﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Models;

namespace DomsIntegration.Core.Messages.Responses
{
    /// <summary>
    /// FC Status response
    /// </summary>
    public class FcStatusResponse : JplResponse
    {
        public FcStatusResponse() : base("FcStatus_resp") { }

        public new FcStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FC Status response data
    /// </summary>
    public class FcStatusResponseData
    {
        public FcStatus1Flags FcStatus1Flags { get; set; }
        public FcStatus2Flags FcStatus2Flags { get; set; }
        public string FcServiceMsgSeqNo { get; set; }
        public FcDateTime FcMasterResetDateAndTime { get; set; }
        public int FcMasterResetCode { get; set; }
        public FcDateTime FcResetDateAndTime { get; set; }
        public string FcResetCode { get; set; }
    }

    /// <summary>
    /// FC Status 1 flags
    /// </summary>
    public class FcStatus1Flags
    {
        public int Value { get; set; }
        public FcStatus1Bits Bits { get; set; }
    }

    /// <summary>
    /// FC Status 1 bits
    /// </summary>
    public class FcStatus1Bits
    {
        public int PumpTotalsReady { get; set; }
        public int InstallationDataReceived { get; set; }
        public int FallbackMode { get; set; }
        public int FallbackTotalsNonZero { get; set; }
        public int RamErrorDetectedInFc { get; set; }
        public int OpWithStoredTransDisabled { get; set; }
        public int OptSaleDisabled { get; set; }
        public int CurrencyCodeIsEuro { get; set; }
    }

    /// <summary>
    /// FC Status 2 flags
    /// </summary>
    public class FcStatus2Flags
    {
        public int Value { get; set; }
        public FcStatus2Bits Bits { get; set; }
    }

    /// <summary>
    /// FC Status 2 bits
    /// </summary>
    public class FcStatus2Bits
    {
        public int ServiceMsgReady { get; set; }
        public int UnsolicitedStatusUpdateOn { get; set; }
        public int HwSwIncompatibilityWithinFc { get; set; }
        public int RtcError { get; set; }
        public int NoAdditionalParametersAssignedToGrades { get; set; }
        public int BackOfficeRecordExists { get; set; }
    }

    /// <summary>
    /// Change FC date and time response
    /// </summary>
    public class ChangeFcDateTimeResponse : JplResponse
    {
        public ChangeFcDateTimeResponse() : base("change_FcDateAndTime_resp")
        {
            Data = new { };
        }
    }

    /// <summary>
    /// FC date and time response
    /// </summary>
    public class FcDateTimeResponse : JplResponse
    {
        public FcDateTimeResponse() : base("FcDateAndTime_resp") { }

        public new FcDateTimeResponseData Data { get; set; }
    }

    /// <summary>
    /// FC date and time response data
    /// </summary>
    public class FcDateTimeResponseData
    {
        public FcDateTime FcDateAndTime { get; set; }
        public FcDateTime LastDateAndTimeSetting { get; set; }
    }

    /// <summary>
    /// Change FC operation mode response
    /// </summary>
    public class ChangeFcOperationModeResponse : JplResponse
    {
        public ChangeFcOperationModeResponse() : base("change_FcOperationModeNo_resp")
        {
            Data = new { };
        }
    }

    /// <summary>
    /// Change FC price set response
    /// </summary>
    public class ChangeFcPriceSetResponse : JplResponse
    {
        public ChangeFcPriceSetResponse() : base("change_FcPriceSet_resp", "02H") { }

        public new ChangeFcPriceSetResponseData Data { get; set; }
    }

    /// <summary>
    /// Change FC price set response data
    /// </summary>
    public class ChangeFcPriceSetResponseData
    {
        public string FcPriceSetId { get; set; }
    }

    /// <summary>
    /// FC installation status response
    /// </summary>
    public class FcInstallStatusResponse : JplResponse
    {
        public FcInstallStatusResponse() : base("FcInstallStatus_resp") { }

        public new FcInstallStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FC installation status response data
    /// </summary>
    public class FcInstallStatusResponseData
    {
        public InstalledFcDeviceGroup[] InstalledFcDeviceGroups { get; set; }
    }

    /// <summary>
    /// Installed FC device group
    /// </summary>
    public class InstalledFcDeviceGroup
    {
        public string InstallMsgCode { get; set; }
        public string ExtendedInstallMsgCode { get; set; }
        public string[] FcDeviceId { get; set; }
    }
}
