﻿using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.DispenseControl
{
    public class FpStatusRequest : JplRequest
    {
        public class FpStatusRequestData
        {
            [JsonPropertyName("FpId")]
            public string FpId { get; set; }
        }
    }

    public class FpStatusResponse : JplResponse
    {
        public class FpStatusResponseData
        {
            [JsonPropertyName("FpId")]
            public string FpId { get; set; }

            [JsonPropertyName("SmId")]
            public string SmId { get; set; }

            [JsonPropertyName("FpMainState")]
            public EnumValue<string> FpMainState { get; set; }

            [JsonPropertyName("FpSubStates")]
            public BitFlags FpSubStates { get; set; }

            [JsonPropertyName("FpLockId")]
            public string FpLockId { get; set; }
        }
    }
}
