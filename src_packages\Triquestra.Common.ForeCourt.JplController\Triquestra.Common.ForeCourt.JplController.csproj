<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net461;net48</TargetFrameworks>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Authors>Triquestra</Authors>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <title>Triquestra.Common.ForeCourt.JplController</title>
    <description>JPL-based implementation of IPumpController for Triquestra forecourt systems</description>
    <RootNamespace>Triquestra.Common.PumpEsm.RegulusInterface</RootNamespace>
    <AssemblyName>Triquestra.Common.ForeCourt.JplController</AssemblyName>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="JplPumpController.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.Common.ForeCourt.Comms\Triquestra.Common.ForeCourt.Comms.csproj" />
    <ProjectReference Include="..\Triquestra.Common.ForeCourt.Messaging\Triquestra.Common.ForeCourt.Messaging.csproj" />
    <ProjectReference Include="..\Triquestra.Common.ForeCourt.RegulusInterface\Triquestra.Common.Forecourt.csproj" />
    <ProjectReference Include="..\..\DomsClient\doms_5\src\DomsIntegration.Core\DomsIntegration.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="NLog" Version="5.2.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
