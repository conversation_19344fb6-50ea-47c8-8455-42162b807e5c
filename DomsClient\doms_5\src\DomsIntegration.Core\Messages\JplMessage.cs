﻿using System;
using Newtonsoft.Json;

namespace DomsIntegration.Core.Messages
{
    /// <summary>
    /// Base class for all JPL messages
    /// </summary>
    public class JplMessage
    {
        /// <summary>
        /// Name of the message
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// Sub-code identifying message variant
        /// </summary>
        [JsonProperty("subCode")]
        public string SubCode { get; set; }

        /// <summary>
        /// Data associated with the message
        /// </summary>
        [JsonProperty("data")]
        public object Data { get; set; }

        /// <summary>
        /// Indicates if message is solicited (response only)
        /// </summary>
        [JsonProperty("solicited", NullValueHandling = NullValueHandling.Ignore)]
        public bool? Solicited { get; set; }

        /// <summary>
        /// Optional correlation ID for request/response matching
        /// </summary>
        [JsonProperty("correlationId", NullValueHandling = NullValueHandling.Ignore)]
        public string CorrelationId { get; set; }
    }

    /// <summary>
    /// Base class for request messages
    /// </summary>
    public abstract class JplRequest : JplMessage
    {
        protected JplRequest(string name, string subCode = "00H")
        {
            Name = name;
            SubCode = subCode;
        }
    }

    /// <summary>
    /// Base class for response messages
    /// </summary>
    public abstract class JplResponse : JplMessage
    {
        protected JplResponse(string name, string subCode = "00H")
        {
            Name = name;
            SubCode = subCode;
        }
    }
}
