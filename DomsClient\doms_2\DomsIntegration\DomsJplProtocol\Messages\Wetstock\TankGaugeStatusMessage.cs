﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.Wetstock
{
    public class TankGaugeStatusRequest : JplRequest
    {
        public class TankGaugeStatusRequestData
        {
            [JsonPropertyName("TankId")]
            public string TankId { get; set; }
        }
    }

    public class TankGaugeStatusResponse : JplResponse
    {
        public class TankGaugeStatusResponseData
        {
            [JsonPropertyName("TankGaugeSystem")]
            public TankGaugeSystem TankGaugeSystem { get; set; }
            
            [JsonPropertyName("Tanks")]
            public List<Tank> Tanks { get; set; }
        }
        
        public class TankGaugeSystem
        {
            [JsonPropertyName("TankGaugeSystemStatus")]
            public BitFlags TankGaugeSystemStatus { get; set; }
            
            [JsonPropertyName("TankGaugeSystemAlarms")]
            public BitFlags TankGaugeSystemAlarms { get; set; }
        }
        
        public class Tank
        {
            [JsonPropertyName("TankId")]
            public string TankId { get; set; }
            
            [JsonPropertyName("TankStatus")]
            public BitFlags TankStatus { get; set; }
            
            [JsonPropertyName("TankAlarms")]
            public BitFlags TankAlarms { get; set; }
        }
    }
}
