﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace DomsPosProtocol.Models
{
    // Base message class
    public abstract class JplMessage
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("subCode")]
        public string SubCode { get; set; }

        [JsonPropertyName("correlationId")]
        public object CorrelationId { get; set; }
    }

    // Request message base
    public abstract class JplRequest : JplMessage
    {
        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Response message base
    public abstract class JplResponse : JplMessage
    {
        [JsonPropertyName("solicited")]
        public bool Solicited { get; set; }

        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Enumeration wrapper
    public class EnumValue<T>
    {
        [JsonPropertyName("enum")]
        public Dictionary<string, string> Enum { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }
    }

    // Bit flags wrapper
    public class BitFlags
    {
        [JsonPropertyName("value")]
        public int Value { get; set; }

        [JsonPropertyName("bits")]
        public Dictionary<string, int> Bits { get; set; }
    }

    // Multi-message container
    public class MultiMessageResponse : JplResponse
    {
        public class MultiMessageData
        {
            [JsonPropertyName("messages")]
            public List<JplResponse> Messages { get; set; }
        }
    }

    // Reject message
    public class RejectMessageResponse : JplResponse
    {
        public class RejectData
        {
            [JsonPropertyName("RejectedExtendedMsgCode")]
            public string RejectedExtendedMsgCode { get; set; }

            [JsonPropertyName("RejectedMsgSubc")]
            public string RejectedMsgSubc { get; set; }

            [JsonPropertyName("RejectCode")]
            public EnumValue<string> RejectCode { get; set; }

            [JsonPropertyName("RejectInfo")]
            public string RejectInfo { get; set; }

            [JsonPropertyName("RejectInfoText")]
            public string RejectInfoText { get; set; }
        }
    }
}
