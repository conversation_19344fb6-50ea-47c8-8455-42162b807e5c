﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <!--To inherit the global NuGet package sources remove the <clear/> line below -->
    <clear />
    <add key="api.nuget.org" value="https://api.nuget.org/v3/index.json" />
    <add key="Triquestra_External" value="http://tqnuget.triquestra.com/nuget" />
    <add key="Nuget.org" value="https://www.nuget.org/api/v2/" />
    <add key="DotNet-Core" value="https://www.myget.org/F/dotnet-core/api/v3/index.json" />
  </packageSources>
  <packageSourceCredentials>
    <Triquestra_External>
      <add key="Username" value="tqnuget" />
      <add key="ClearTextPassword" value="TqNuget556" />
    </Triquestra_External>
  </packageSourceCredentials>
</configuration>
