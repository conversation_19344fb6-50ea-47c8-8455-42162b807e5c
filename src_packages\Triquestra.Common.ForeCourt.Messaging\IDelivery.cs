﻿using System;
using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public interface IDelivery
    {
        int DeliveryId { get; set; }
        int DispenserId { get; set; }
        int NozzleId { get; set; }
        int BlendId { get; set; }
        int TankId { get; set; }
        AuthModes Mode { get; set; }
        DeliveryStates State { get; set; }
        LimitTypes LimitType { get; set; }
        decimal Limit { get; set; }
        decimal Volume { get; set; }
        decimal Amount { get; set; }
        decimal Price { get; set; }
        int PriceId { get; set; }
        int PriceLevel { get; set; }
        DateTime Timestamp { get; set; }
        bool IsCurrent { get; set; }
        int? Owner { get; set; }
        DeliveryExceptions Exception { get; set; }

    }

    public class Delivery:IDelivery
    {
        public int DeliveryId { get; set; }
        public int DispenserId { get; set; }
        public int NozzleId { get; set; }
        public int BlendId { get; set; }
        public int TankId { get; set; }
        public AuthModes Mode { get; set; }
        public DeliveryStates State { get; set; }
        public LimitTypes LimitType { get; set; }
        public decimal Limit { get; set; }
        public decimal Volume { get; set; }
        public decimal Amount { get; set; }
        public decimal Price { get; set; }
        public int PriceId { get; set; }
        public int PriceLevel { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsCurrent { get; set; }
        public int? Owner { get; set; }
        public DeliveryExceptions Exception { get; set; }

        public static Delivery Parse(XElement del, int sourceId)
        {
            /*
                <FCCMessage>
                  <Header MessageType="EVENT" SourceID="4" />
                  <Event Code="DELIVERY_STARTED">
                    <DeliveryStarted>
                      <Delivery ID="98" NozzleID="3" BlendID="3009" TankID="3" Mode="POSTPAY">
                        <Totals Limit="0.000" LimitType="NONE" Volume="0.000" Amount="0.000" Price="1.012" Level="0" PriceID="0" />
                        <Status State="AUTHORIZED" Timestamp="2016-02-16T22:32:05" IsCurrent="True" />
                      </Delivery>
                    </DeliveryStarted>
                  </Event>
                </FCCMessage>
          */
            var deliveryId = int.Parse(del.Attribute("ID").Value);
            var blendId = int.Parse(del.Attribute("BlendID").Value);
            var tankId =del.Attribute("TankID")==null ? 0 : int.Parse(del.Attribute("TankID").Value);
            var nozzle = int.Parse(del.Attribute("NozzleID").Value);
            var mode = (AuthModes)Enum.Parse(typeof(AuthModes), del.Attribute("Mode").Value);
            var delivery = new Delivery
            {
                DeliveryId = deliveryId,
                BlendId = blendId,
                TankId = tankId,
                DispenserId = sourceId,
                NozzleId = nozzle,
                Mode = mode
            };

            var exception = del.Attribute("Exception");
            if (exception != null)
            {
                delivery.Exception =
                    (DeliveryExceptions)Enum.Parse(typeof(DeliveryExceptions), exception.Value);
            }
            else
                delivery.Exception = DeliveryExceptions.NONE;

            var totals = del.Element("Totals");
            if (totals != null)
            {
                delivery.Limit = decimal.Parse(totals.Attribute("Limit").Value);
                delivery.LimitType =
                    (LimitTypes)Enum.Parse(typeof(LimitTypes), totals.Attribute("LimitType").Value);
                delivery.Volume = decimal.Parse(totals.Attribute("Volume").Value);
                delivery.Amount = decimal.Parse(totals.Attribute("Amount").Value);
                delivery.Price = decimal.Parse(totals.Attribute("Price").Value);
                delivery.PriceLevel = int.Parse(totals.Attribute("Level").Value);
                delivery.PriceId = int.Parse(totals.Attribute("PriceID").Value);
            }

            var status = del.Element("Status");
            if (status != null)
            {
                delivery.State = (DeliveryStates)Enum.Parse(typeof(DeliveryStates), status.Attribute("State").Value);
                delivery.Timestamp = DateTime.Parse(status.Attribute("Timestamp").Value);
                delivery.IsCurrent = status.Attribute("IsCurrent").Value == "True" ||
                                     status.Attribute("IsCurrent").Value == "1";
                if (status.Attribute("Owner") != null)
                    delivery.Owner = int.Parse(status.Attribute("Owner").Value);
            }

            return delivery;
        }
    }

}
