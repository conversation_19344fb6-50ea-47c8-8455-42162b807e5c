﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Messages.Requests;
using DomsIntegration.Core.Messages.Responses;
using DomsIntegration.Core.Utilities;

namespace DomsIntegration.Core.Services
{
    /// <summary>
    /// Service for handling forecourt controller logon operations
    /// </summary>
    public class LogonService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the LogonService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public LogonService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Performs logon to the forecourt controller
        /// </summary>
        /// <param name=""accessCode"">Access code (default: ""POS"")</param>
        /// <param name=""countryCode"">Country code</param>
        /// <param name=""posVersionId"">POS version identifier</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Logon response</returns>
        public async Task<FcLogonResponse> LogonAsync(
            string accessCode = "POS",
            string countryCode = "0000",
            string posVersionId = "DomsPosClient.1.0",
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo("Performing logon to forecourt controller");

                var request = new FcLogonRequest
                {
                    Data = new FcLogonRequestData
                    {
                        FcAccessCode = accessCode,
                        CountryCode = countryCode,
                        PosVersionId = posVersionId
                    }
                };

                var response = await _client.SendMessageAsync<FcLogonResponse>(request, cancellationToken: cancellationToken);

                if (response != null)
                {
                    _logger.LogInfo($"Logon successful - Controller: HW{response.Data.FcHwType} SW{response.Data.FcSwType}");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Logon failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Performs extended logon with unsolicited message configuration
        /// </summary>
        /// <param name=""accessCode"">Access code</param>
        /// <param name=""countryCode"">Country code</param>
        /// <param name=""posVersionId"">POS version identifier</param>
        /// <param name=""unsolicitedMessages"">List of unsolicited messages to enable</param>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>Extended logon response</returns>
        public async Task<FcLogonExtendedResponse> LogonExtendedAsync(
            string accessCode = "POS",
            string countryCode = "0000",
            string posVersionId = "DomsPosClient.1.0",
            UnsolicitedMessage[] unsolicitedMessages = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo("Performing extended logon to forecourt controller");

                var request = new FcLogonExtendedRequest
                {
                    Data = new FcLogonExtendedRequestData
                    {
                        FcAccessCode = accessCode,
                        CountryCode = countryCode,
                        PosVersionId = posVersionId,
                        FcLogonPars = new FcLogonParameters
                        {
                            UnsolMsgList = unsolicitedMessages ?? new UnsolicitedMessage[0]
                        }
                    }
                };

                var response = await _client.SendMessageAsync<FcLogonExtendedResponse>(request, cancellationToken: cancellationToken);

                if (response != null)
                {
                    _logger.LogInfo($"Extended logon successful - {response.Data.UnsolMessages?.Length ?? 0} unsolicited messages configured");
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Extended logon failed: {ex.Message}");
                throw;
            }
        }
    }
}
