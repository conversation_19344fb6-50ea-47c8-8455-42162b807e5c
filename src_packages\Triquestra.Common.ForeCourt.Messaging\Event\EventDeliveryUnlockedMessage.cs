﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryUnlockedMessage : ForecourtEventMessage
    {
        public int DeliveryId { get; set; }
        public EventDeliveryUnlockedMessage(int sequenceNo, int sourceId, int targetId, int deliveryId) : 
            base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_UNLOCKED)
        {
            DeliveryId = deliveryId;
        }

        public static EventDeliveryUnlockedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryUnLocked = eventNode.Element("DeliveryUnLocked");
            if (deliveryUnLocked == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryUnLocked> node");
            }
            var deliveryId = int.Parse(deliveryUnLocked.Attribute("DeliveryID").Value);

            return new EventDeliveryUnlockedMessage(seqNo, sourceId, targetId, deliveryId);
        }
    }
}
