﻿using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Messages.DispenseControl;
using DomsJplProtocol.Messages.Wetstock;
using DomsJplProtocol.Messages.PriceDisplay;

namespace DomsJplProtocol.Utilities
{
    /// <summary>
    /// Registry for JPL message types
    /// </summary>
    public static class MessageRegistry
    {
        /// <summary>
        /// Registers all known message types with the factory
        /// </summary>
        public static void RegisterAllMessageTypes()
        {
            // Initialize the factory
            JplMessageFactory.Initialize();
            
            // Register requests
            RegisterRequestTypes();
            
            // Register responses
            RegisterResponseTypes();
        }
        
        /// <summary>
        /// Registers all known request message types
        /// </summary>
        private static void RegisterRequestTypes()
        {
            // General Forecourt Controller Messages
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.FC_LOGON_REQ, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcLogonRequest));
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.FC_STATUS_REQ, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcStatusRequest));
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.FC_DATE_TIME_REQ, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcDateTimeRequest));
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.CHANGE_FC_DATE_TIME_REQ, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcDateTimeRequest));
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.CHANGE_FC_STATUS_UPDATE_MODE_REQ, 
                JplConstants.SubCodes.SUBC_00H, typeof(ChangeFcStatusUpdateModeRequest));
                
            // Dispense Control Messages
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.FP_STATUS_REQ,
                JplConstants.SubCodes.SUBC_00H, typeof(FpStatusRequest));
                
            // Wetstock Messages
            JplMessageFactory.RegisterRequestType(JplConstants.MessageNames.TANK_GAUGE_STATUS_REQ,
                JplConstants.SubCodes.SUBC_00H, typeof(TankGaugeStatusRequest));
                
            // Price Display Messages
            JplMessageFactory.RegisterRequestType("PriceDisplayStatus_req",
                JplConstants.SubCodes.SUBC_00H, typeof(PriceDisplayStatusRequest));
        }
        
        /// <summary>
        /// Registers all known response message types
        /// </summary>
        private static void RegisterResponseTypes()
        {
            // General Forecourt Controller Messages
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.FC_LOGON_RESP, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcLogonResponse));
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.FC_STATUS_RESP, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcStatusResponse));
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.FC_DATE_TIME_RESP, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcDateTimeResponse));
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.CHANGE_FC_DATE_TIME_RESP, 
                JplConstants.SubCodes.SUBC_00H, typeof(FcDateTimeResponse));
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.CHANGE_FC_STATUS_UPDATE_MODE_RESP, 
                JplConstants.SubCodes.SUBC_00H, typeof(ChangeFcStatusUpdateModeResponse));
                
            // Dispense Control Messages
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.FP_STATUS_RESP,
                JplConstants.SubCodes.SUBC_00H, typeof(FpStatusResponse));
                
            // Wetstock Messages
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.TANK_GAUGE_STATUS_RESP,
                JplConstants.SubCodes.SUBC_00H, typeof(TankGaugeStatusResponse));
                
            // Price Display Messages
            JplMessageFactory.RegisterResponseType("PriceDisplayStatus_resp",
                JplConstants.SubCodes.SUBC_00H, typeof(PriceDisplayStatusResponse));
                
            // Core/Utility Messages
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.REJECT_MESSAGE_RESP,
                JplConstants.SubCodes.SUBC_00H, typeof(RejectMessageResponse));
            JplMessageFactory.RegisterResponseType(JplConstants.MessageNames.MULTI_MESSAGE_RESP,
                JplConstants.SubCodes.SUBC_00H, typeof(MultiMessageResponse));
        }
    }
}
