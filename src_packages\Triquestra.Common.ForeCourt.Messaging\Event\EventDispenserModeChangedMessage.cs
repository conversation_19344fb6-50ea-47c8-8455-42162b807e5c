﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDispenserModeChangedMessage : ForecourtEventMessage
    {
        public DispenserModes Mode { get; set; }

        public EventDispenserModeChangedMessage(int sequenceNo, int sourceId, int targetId, DispenserModes mode)
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.DISPENSER_MODE_CHANGE)
        {
            Mode = mode;
        }

        public static EventDispenserModeChangedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var dispModeChange = eventNode.Element("DispenserModeChange");
            if (dispModeChange == null)
            {
                throw new XmlSchemaException("eventNode does not have <DispenserModeChange> node");
            }
            var mode =
                (DispenserModes)
                    Enum.Parse(typeof(DispenserModes),
                        dispModeChange.Attribute("Mode").Value);
            return new EventDispenserModeChangedMessage(seqNo, sourceId, targetId, mode);
        }
    }
}
