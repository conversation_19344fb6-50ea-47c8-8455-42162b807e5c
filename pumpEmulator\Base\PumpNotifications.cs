﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Base
{
    public enum PumpNotifications
    {
        Unknown,
        /// <summary>
        ///  petrol item was deleted at POS
        /// </summary>
        ItemDeleted = 'D',
        /// <summary>
        /// sale voided, all petrol items should be cancelled
        /// </summary>
        SaleVoided = 'V',
        /// <summary>
        /// sale completed, all petrol items should be confirmed
        /// </summary>
        SaleCompleted = 'C',
        /// <summary>
        /// enable petrol sales
        /// </summary>
        EnablePetrolSales = 'Y',
        /// <summary>
        /// disable petrol sales
        /// </summary>
        DisablePetrolSales = 'N',
        /// <summary>
        /// sale is about to be completed, confirm all is OK
        /// </summary>
        CheckAllOk = 'A'
    }

}
