﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ReserveResponse: CommandResponse
    {
        public int DispenserId { get { return TargetId; } set { TargetId = value; } }
        public ReserveResponse(int sequenceNo, int sourceId, int targetId, ForecourtCommandResults result)
            : base(sequenceNo, sourceId, targetId, result, ForecourtCommandMessageTypes.RESERVE)
        { }

    }
}
