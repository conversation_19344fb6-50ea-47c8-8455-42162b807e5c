﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Utilities;

namespace DomsIntegration.Tests
{
    [TestClass]
    public class JplClientTests
    {
        [TestMethod]
        public void JplClient_Constructor_ShouldInitializeWithDefaultLogger()
        {
            // Arrange & Act
            var client = new JplClient();

            // Assert
            Assert.IsNotNull(client);
            Assert.IsFalse(client.IsConnected);
        }

        [TestMethod]
        public void JplClient_Constructor_ShouldInitializeWithCustomLogger()
        {
            // Arrange
            var logger = new ConsoleLogger();

            // Act
            var client = new JplClient(logger);

            // Assert
            Assert.IsNotNull(client);
            Assert.IsFalse(client.IsConnected);
        }

        [TestMethod]
        public void JplClient_Dispose_ShouldCleanupResources()
        {
            // Arrange
            var client = new JplClient();

            // Act & Assert - Should not throw
            client.Dispose();
        }
    }
}
