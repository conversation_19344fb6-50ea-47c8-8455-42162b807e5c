﻿using System;
using System.Collections.Generic;
using Triquestra.Common.PumpEsm.Comms;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public interface IPumpController:IForecourtConnection
    {
        IEnumerable<IDispenserSettings> Dispensers { get; }

        int WorkstationId { get; }

        string Version { get; }

        /// <summary>
        /// Reserve (take ownership of) the dispenser prior to authorizing for a preauth or prepay delivery. 
        /// </summary>
        /// <param name="sourceId">Workstation Id</param>
        /// <param name="targetId">id of the dispenser</param>
        /// <param name="limit">Amount</param>
        /// <param name="blend">Fuel Grade</param>
        void Reserve(int sourceId, int targetId, decimal limit, IForecourtBlend blend);

        /// <summary>
        /// Authorize a dispenser for delivery. 
        /// </summary>
        /// <param name="targetId">id of the dispenser</param>
        void Authorize(int targetId);

        /// <summary>
        /// Authorize dispenser for a prepay delivery
        /// </summary>
        /// <param name="targetId"></param>
        /// <param name="limit">dollar value of prepay</param>
        /// <param name="blend">selected blend for the prepay delivery</param>
        void Authorize(int targetId, decimal limit, IForecourtBlend blend);

        /// <summary>
        /// Abort a reservation, authorization, or delivery (depending on the state of the dispenser). 
        /// </summary>
        /// <param name="targetId">id of the delivery</param>
        void Abort(int targetId);

        /// <summary>
        /// Abort a reservation, authorization, or delivery (depending on the state of the dispenser) on a specific station. 
        /// </summary>
        /// <param name="sourceId">id of the station</param>
        /// <param name="targetId">id of the delivery</param>
        void Abort(int sourceId, int targetId);

        /// <summary>
        /// Lock a completed delivery for finalization. If successful, the POS device, “owns” the delivery and no other external device can lock it.
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        /// <param name="deliveryId"></param>
        void LockDelivery(int targetId, int deliveryId);

        /// <summary>
        /// Unlock a completed delivery that was previously locked. 
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        /// <param name="deliveryId"></param>
        void UnlockDelivery(int targetId, int deliveryId);

        /// <summary>
        /// Unlock a completed delivery that was previously locked by a POS. 
        /// </summary>
        /// <param name="sourceId">POS station id</param>
        /// <param name="targetId">dispenser id</param>
        /// <param name="deliveryId"></param>
        void UnlockDelivery(int sourceId, int targetId, int deliveryId);

        /// <summary>
        /// Finalize a delivery and remove it from the stack 
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        /// <param name="deliveryId"></param>
        void ClearDelivery(int targetId, int deliveryId);

        /// <summary>
        /// Suspend all dispenser that is currently operational
        /// </summary>
        void SuspendAll();

        /// <summary>
        /// Suspend a dispenser that is currently operational
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        void Suspend(int targetId);

        /// <summary>
        /// Resume all dispensers that is currently suspended
        /// </summary>
        void ResumeAll();

        /// <summary>
        /// Resume a dispenser that is currently suspended
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        void Resume(int targetId);

        /// <summary>
        /// Performs a state reset on the specified dispenser. A delivery in progress is aborted and available for finalization. 
        /// A dispenser price update is initiated. Dispenser state returns to IDLE (locked) following price change.
        /// It should be used only when the Dispenser Control software appears to be in a hung state and needs to be reset to allow for further deliveries. 
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        void ResetDispenser(int targetId);

        /// <summary>
        /// Clears any exceptions from a dispenser.
        /// </summary>
        /// <param name="targetId">dispenser id</param>
        void ClearDispenserException(int targetId);


        /// <summary>
        /// Stop the specified driver.
        /// </summary>
        /// <param name="targetId"></param>
        void StopDriver(int targetId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="targetId"></param>
        void StartDriver(int targetId);

        void ShutdownDriver(int targetId);

        void TerminateController();

        /// <summary>
        /// Performs a state reset on all dispensers. 
        /// </summary>
        void ResetAllDispensers();

        /// <summary>
        /// Request electronic totals from a dispenser
        /// </summary>
        /// <param name="targetId">Dispenser Id</param>
        void GetElectronicTotals(int targetId);

        void GetProfiles();

        void SetProfile(int profileIndex);
        [Obsolete("Do not use this method. Use ISystemManager.ChangeFuelPrice instead.", true)]
        void ChangeFuelPrice(int blendId, int priceId, decimal amount);

        void GetDeliveryData(int targetId, int deliveryId);

        void GetDispenserData(int targetId);

        /// <summary>
        /// Signalled when the dispenser list is received
        /// </summary>
        event EventHandler Heartbeat;

        event EventHandler DeliveryStarted;
        /// <summary>
        /// Signalled periodically during the course of a delivery to convey delivery progress totals.
        /// Dispenser Id, Delivery Id, Amount, Volume
        /// </summary>
        event EventHandler DeliveryInProgress;

        /// <summary>
        /// Signalled when a delivery is completed due to nozzle replacement or timeout.
        /// Completed POSTPAY deliveries, and PREPAY deliveries with refunds due, will remain in FCC memory for finalization; all others will be “auto-finalized” and deleted from memory. 
        /// Dispenser Id, Delivery Id, NozzleId, BlendId, Type (PREAUTH e.g.), Price and Price Level Id 
        /// </summary>
        event EventHandler DeliveryCompleted;

        /// <summary>
        /// Signalled any time the state of a delivery changes.
        /// Delivey Id, State (DeliveryStates enum)
        /// </summary>
        event EventHandler DeliveryStateChanged;

        /// <summary>
        /// Signalled when a delivery times out. DeliveryTimeoutTypes 
        /// Dispenser Id, Delivery Id, State (DeliveryTimeoutTypes)
        /// </summary>
        event EventHandler DeliveryTimeout;

        /// <summary>
        /// Signalled when a delivery is finalized by a POS (owner). Delivery deletion follows.
        /// Dispenser Id, Delivery Id, Exception, Finalize Method (FinalizeMethods), Volume, Amount, Price, Price Level Id 
        /// </summary>
        event EventHandler DeliveryCleared;

        /// <summary>
        /// Event is signalled when a delivery is locked by a POS (owner).
        /// Dispenser Id, Delivery Id, owner
        /// </summary>
        event EventHandler DeliveryLocked;


        /// <summary>
        /// Event is signalled when a locked delivery is unlocked by its owner.
        /// Dispenser Id, Delivery Id, owner
        /// </summary>
        event EventHandler DeliveryUnlocked;


        /// <summary>
        /// Event Signalled when a delivery is deleted from the FCC.
        /// Dispenser Id, Delivery Id, Exception
        /// </summary>
        event EventHandler DeliveryDeleted;

        /// <summary>
        /// Event Signalled when the current delivery is moved to the stack.
        /// Dispenser Id
        /// </summary>
        event EventHandler DeliveryStacked;


        /// <summary>
        /// Signalled when the dispenser mode changes.
        /// Dispenser Id, Mode (DispenserModes)
        /// </summary>
        event EventHandler DispenserModeChanged;

        /// <summary>
        /// Signalled when the dispenser state changes.
        /// Dispenser Id, State(DispenserStates)
        /// </summary>
        event EventHandler DispenserStateChanged;

        /// <summary>
        /// Signalled when the dispenser state changes.
        /// Dispenser Id, Reason(ForecourtCommandResults)
        /// </summary>
        event EventHandler DispenserClearRequired;

        /// <summary>
        /// Signalled when the dispenser state changes.
        /// This is currently signalled only when a dispenser’s stack size changes.
        /// Dispenser Id, Attribute, Value
        /// </summary>
        event EventHandler DispenserConfigChanged;

        /// <summary>
        /// Signalled when the state of a nozzle on a dispenser changes
        /// Dispenser Id, Nozzle Id, State(NozzleStates)
        /// </summary>
        event EventHandler NozzleStateChanged;

        /// <summary>
        /// Signalled when an electronic totals are received.
        /// Dispenser Id, Total Type, Nozzle Id, Volume, Amount, Price, Blend Id, Delivery Count -not all pump types support this
        /// </summary>
        event EventHandler ElectronicTotalsReceived;

        /// <summary>
        /// Signalled when dispensers are updated.
        /// </summary>
        event EventHandler DispensersUpdated;

        /// <summary>
        /// Signalled when Regulus returns auth mode profiles, a request needs to be sent first.
        /// </summary>
        event EventHandler ProfilesReceived;

        /// <summary>
        /// Signalled when initial dispenser data received
        /// Dispenser Id, State(DispenserStates)
        /// </summary>
        event EventHandler DispenserDataReceived;
        /// <summary>
        /// Signalled when initial delivery data received        
        /// </summary>
        event EventHandler DeliveryDataReceived;
        /// <summary>
        /// Signalled when a LockDelivery is executed
        /// </summary>
        event EventHandler DeliveryLockReceived;
        /// <summary>
        /// Signalled when a Reserve is executed
        /// </summary>
        event EventHandler ReserveReceived;
        /// <summary>
        /// Signalled when the dispenser list is received
        /// </summary>
        event EventHandler DispenserListReceived;
    }
}
