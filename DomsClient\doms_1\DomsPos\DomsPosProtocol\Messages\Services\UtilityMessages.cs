﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.Services
{
    // Status Update Mode
    public class ChangeFcStatusUpdateModeRequest : JplRequest
    {
        public class ChangeFcStatusUpdateModeData
        {
            [JsonPropertyName("StatusUpdateCode")]
            public int StatusUpdateCode { get; set; }
        }
    }

    public class ChangeFcStatusUpdateModeResponse : JplResponse
    {
        public class ChangeFcStatusUpdateModeResponseData
        {
            // Empty response
        }
    }

    // Echo Command
    public class EchoCommandRequest : JplRequest
    {
        public class EchoCommandData
        {
            [JsonPropertyName("EchoData")]
            public string EchoData { get; set; }
        }
    }

    public class EchoCommandResponse : JplResponse
    {
        public class EchoCommandResponseData
        {
            [JsonPropertyName("EchoData")]
            public string EchoData { get; set; }
        }
    }

    // Heartbeat
    public class HeartbeatMessage : JplRequest
    {
        public class HeartbeatData
        {
            // Empty data object
        }
    }
}
