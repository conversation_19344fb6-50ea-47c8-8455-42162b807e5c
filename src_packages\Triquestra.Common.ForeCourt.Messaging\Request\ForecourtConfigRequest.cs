﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class ForecourtConfigRequest : ForecourtMessage
    {
        public ForecourtConfigRequest( ForecourtConfigurationTypes configType) 
            : base(ForecourtMessageClasses.CONFIG_REQUEST, 0)
        {
            ConfigType = configType;
        }

        public ForecourtConfigurationTypes ConfigType { get; set; }

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            xdoc.Root.Add(new XElement("ConfigReq", new XAttribute("Type", ConfigType)));
            return xdoc;
        }

        public static ForecourtConfigRequest Create(ForecourtConfigurationTypes configType)
        {
            return new ForecourtConfigRequest(configType);
        }
    }
}
