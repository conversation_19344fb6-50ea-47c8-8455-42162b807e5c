﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Net;
using System.Net.Sockets;

namespace udpPackage
{
    public partial class Form1 : Form
    {
        private string _udpHost;
        private int _udpPort;
        private UdpClient _udpClient;
        public Form1()
        {
            InitializeComponent();
        }


        private void button1_Click(object sender, EventArgs e)
        {
            if (_udpClient != null)
            {
                var uC = _udpClient;
                _udpClient = null;
                uC.DropMulticastGroup(IPAddress.Parse(_udpHost));
                uC.Close();
                button1.Text = "Connect";
            }
            else
            {

                try
                {
                    var adapters = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();
                    foreach(var adapter in adapters)
                    {
                        textBox3.AppendText(string.Format("Name: {0},Description: {1}, NetworkInterfaceType: {2}, SupportsMulticast: {3} \r\n", adapter.Name,adapter.Description, adapter.NetworkInterfaceType,adapter.SupportsMulticast));                        
                    }
                    _udpHost = textBox1.Text;
                    _udpPort = int.Parse(textBox2.Text);
                    var udpEndPoint = new IPEndPoint(IPAddress.Any, _udpPort);
                    textBox3.AppendText("Instantiating an UdpClient"+System.Environment.NewLine);
                    var udpClient = new UdpClient();
                    textBox3.AppendText("Setting up Socket options"+System.Environment.NewLine);
                    udpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, 1);
                    textBox3.AppendText("Binding to a port "+_udpPort+System.Environment.NewLine);
                    udpClient.Client.Bind(udpEndPoint);
                    textBox3.AppendText("Joining a multicast group "+_udpHost+System.Environment.NewLine);
                    udpClient.JoinMulticastGroup(IPAddress.Parse(_udpHost), 3);
                    textBox3.AppendText("Enabling broadcast "+System.Environment.NewLine);                    
                    udpClient.EnableBroadcast = true;
                    textBox3.AppendText("Ready to receive packets."+System.Environment.NewLine);     
                    button1.Text = "Disconnect";
                    _udpClient = udpClient;
                }
                catch(Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
        }

        private void UdpReceive()
        {
            if (null == _udpClient)
                return;
            //_receiveMessages
            //while (_receiveMessages)
            {
                try
                {
                    var ipEp = new IPEndPoint(IPAddress.Any, _udpPort);

                    var data = _udpClient.Receive(ref ipEp);
                    var stringData = Encoding.ASCII.GetString(data, 0, data.Length);

                    this.Invoke((MethodInvoker)(() => { textBox3.AppendText(DateTime.Now.ToString() + ": " + ipEp + ": " + stringData + System.Environment.NewLine); }));
                }
                catch (Exception x)
                {
                    throw new Exception("UDP receive error encountered", x);
                }
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (_udpClient != null)
                UdpReceive();
        }
    }
}
