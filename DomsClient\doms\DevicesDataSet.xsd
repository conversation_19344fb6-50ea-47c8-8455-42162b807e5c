﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DevicesDataSet" targetNamespace="http://tempuri.org/DevicesDataSet.xsd" xmlns:mstns="http://tempuri.org/DevicesDataSet.xsd" xmlns="http://tempuri.org/DevicesDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DevicesDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DevicesDataSet" msprop:Generator_UserDSName="DevicesDataSet" msprop:EnableTableAdapterManager="true">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Devices" msprop:Generator_UserTableName="Devices" msprop:Generator_RowDeletedName="DevicesRowDeleted" msprop:Generator_TableClassName="DevicesDataTable" msprop:Generator_RowChangedName="DevicesRowChanged" msprop:Generator_RowClassName="DevicesRow" msprop:Generator_RowChangingName="DevicesRowChanging" msprop:Generator_RowEvArgName="DevicesRowChangeEvent" msprop:Generator_RowEvHandlerName="DevicesRowChangeEventHandler" msprop:Generator_TablePropName="Devices" msprop:Generator_TableVarName="tableDevices" msprop:Generator_RowDeletingName="DevicesRowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DeviceID" msprop:Generator_UserColumnName="DeviceID" msprop:Generator_ColumnPropNameInRow="DeviceID" msprop:Generator_ColumnVarNameInTable="columnDeviceID" msprop:Generator_ColumnPropNameInTable="DeviceIDColumn" type="xs:unsignedByte" />
              <xs:element name="Configured" msprop:Generator_UserColumnName="Configured" msprop:Generator_ColumnPropNameInRow="Configured" msprop:Generator_ColumnVarNameInTable="columnConfigured" msprop:Generator_ColumnPropNameInTable="ConfiguredColumn" type="xs:boolean" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Devices" />
      <xs:field xpath="mstns:DeviceID" />
    </xs:unique>
  </xs:element>
</xs:schema>