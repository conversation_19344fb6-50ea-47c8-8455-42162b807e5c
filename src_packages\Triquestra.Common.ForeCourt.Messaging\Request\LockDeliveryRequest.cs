﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class LockDeliveryRequest : ForecourtCommandRequest
    {
        public int DeliveryId { get; set; }

        public LockDeliveryRequest(int sequenceNo, int sourceId, int targetId, int deliveryId)
            : base(sequenceNo, sourceId, targetId, ForecourtCommandMessageTypes.DELIVERY_LOCK)
        {
            DeliveryId = deliveryId;
        }

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            var req = xdoc.Root.Element("CommandReq");
            if (req == null) return xdoc;
            req.Add(new XAttribute("DeliveryID", DeliveryId));
            return xdoc;
        }
    }
}
