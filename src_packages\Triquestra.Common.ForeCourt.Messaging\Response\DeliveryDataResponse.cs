﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class DeliveryDataResponse:CommandResponse
    {
        public List<IDelivery> Delivery { get; set; }

        public DeliveryDataResponse(int sequenceNo, int sourceId, int targetId, DispenserStates state, IEnumerable<IDelivery> delivery)
            : base(sequenceNo, sourceId, targetId, ForecourtCommandResults.DELIVERY_DATA, ForecourtCommandMessageTypes.DELIVERY_DATA)
        {
            DispenserState = state;
            Delivery = new List<IDelivery>();
            if (delivery != null)
                Delivery.AddRange(delivery);
        }

        public static DeliveryDataResponse Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            var configNode = respNode.Element("DeliveryData");
            if (configNode == null)
                throw new XmlSchemaException("Command Response does not have <ControllerConfig> node");
            
            var dispNode = configNode.Element("Dispenser");
            DispenserStates state =
                (DispenserStates)
                    Enum.Parse(typeof(DispenserStates),
                        dispNode.Attribute("State").Value);
            return
                new DeliveryDataResponse(seqNo, sourceId, targetId,state,
                respNode.Descendants("Delivery")
                    .Select(del => Messaging.Delivery.Parse(del, targetId))
                    .ToArray()
                    );
        }

        public DispenserStates DispenserState { get; set; }
    }
}
