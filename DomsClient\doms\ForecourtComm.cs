using System;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading;
using Newtonsoft.Json;

namespace JPL_Demo_POS_CSharp
{
    public class ForecourtComm
    {
        private Socket _socket = null;

        private const char START_FRAMING_CHAR = (char)2;
        private const char END_FRAMING_CHAR = (char)3;

        private byte[] _rxBuf = new byte[2000];
        private StringBuilder _sb = new StringBuilder();
        private ManualResetEventSlim _responseReady = new ManualResetEventSlim();
        private Response _response;
        private int _msgTimeout = 10000;
        private bool _waitingForResponse = false;
        private DateTime _lastRxTime;

        public event Action ConnectionLost;
        public event Action<string> RequestSent;
        public event Action<string> ResponseReceived;
        public event Action<string, string> UnsolicitedMsgReceived;

        public class Response
        {
            public string ResponseName { get; set; }
            public string Json { get; set; }
            public bool IsReject { get; set; }

            public Response(string responseName, string json)
            {
                this.IsReject = responseName == "RejectMessage_resp";
                this.ResponseName = responseName;
                this.Json = json;
            }
        }

        public bool IsConnected()
        {
            if (_socket != null && _socket.Connected)
            {
                if (DateTime.Now.Subtract(_lastRxTime).TotalSeconds > 30)
                {
                    this.Disconnect();
                    _FireConnectionLost();
                    return false;
                }
                else
                {
                    return true;
                }
            }
            else
            {
                return false;
            }
        }

        public void Connect(string host)
        {
            _waitingForResponse = false;
            _responseReady.Reset();

            _socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            _socket.Connect(IPAddress.Parse(host), Properties.Settings.Default.PortNumber);
            _socket.BeginReceive(_rxBuf, 0, _rxBuf.Length, SocketFlags.None, new AsyncCallback(_ReceiveCallBack), null);
            _lastRxTime = DateTime.Now;
        }

        public void Disconnect()
        {
            try
            {
                if (_socket != null)
                {
                    if (_socket.Connected) _socket.Close();
                    _socket.Dispose();
                    _socket = null;

                    _sb.Clear();
                }
            }
            catch (Exception)
            {
                // Ignore exceptions during disconnect
            }
        }

        public Response SendMessage(object request, bool waitForResponse = true)
        {
            JsonSerializerSettings serializerSettings = new JsonSerializerSettings();
            serializerSettings.NullValueHandling = NullValueHandling.Ignore;

            if (!(request is DomsPosBaseType))
                throw new Exception("Request must be of type DomsPosBaseType");

            string req = JsonConvert.SerializeObject(request, serializerSettings);
            return _InternalSendMessage(req, waitForResponse);
        }

        public Response SendRawMessage(string request)
        {
            return _InternalSendMessage(request, true);
        }

        private void _FireConnectionLost()
        {
            try
            {
                ConnectionLost?.Invoke();
            }
            catch (Exception)
            {
                // Ignore exceptions in event handlers
            }
        }

        private void _ReceiveCallBack(IAsyncResult ar)
        {
            int bytesRead;
            string[] messages;
            string msg;
            DomsPosResponseType dppMessage;

            string incompleteMsg = string.Empty;

            try
            {
                if (_socket != null)
                {
                    bytesRead = _socket.EndReceive(ar);

                    if (bytesRead > 0)
                    {
                        _sb.Append(Encoding.ASCII.GetString(_rxBuf, 0, bytesRead));

                        if (_sb.ToString().Contains(END_FRAMING_CHAR))
                        {
                            messages = _sb.ToString().Split(START_FRAMING_CHAR);

                            for (int x = 1; x < messages.Length; x++)
                            {
                                msg = messages[x];

                                if (msg.EndsWith(END_FRAMING_CHAR.ToString()))
                                {
                                    _lastRxTime = DateTime.Now;
                                    msg = msg.Substring(0, msg.Length - 1);

                                    try
                                    {
                                        ResponseReceived?.Invoke(msg);
                                    }
                                    catch (Exception)
                                    {
                                        // Ignore exceptions in event handlers
                                    }

                                    dppMessage = JsonConvert.DeserializeObject<DomsPosResponseType>(msg);

                                    if (dppMessage.solicited)
                                    {
                                        _response = new Response(dppMessage.name, msg);
                                        _responseReady.Set();
                                    }
                                    else
                                    {
                                        if (dppMessage.name != "heartbeat")
                                        {
                                            try
                                            {
                                                UnsolicitedMsgReceived?.Invoke(dppMessage.name, msg);
                                            }
                                            catch (Exception)
                                            {
                                                // Ignore exceptions in event handlers
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    incompleteMsg = msg;
                                }
                            }

                            _sb.Clear();
                            _sb.AppendFormat("{0}{1}", START_FRAMING_CHAR, incompleteMsg);
                        }
                    }

                    _socket.BeginReceive(_rxBuf, 0, _rxBuf.Length, SocketFlags.None, new AsyncCallback(_ReceiveCallBack), null);
                }
            }
            catch (Exception)
            {
                Disconnect();
                _FireConnectionLost();
            }
        }

        private Response _InternalSendMessage(string request, bool waitForResponse = true)
        {
            bool waitResult;

            if (_socket != null && _socket.Connected)
            {
                if (_waitingForResponse)
                    throw new Exception("There is already a pending request");

                _waitingForResponse = true;
                _response = null;
                _responseReady.Reset();

                _socket.Send(Encoding.ASCII.GetBytes(string.Format("{0}{1}{2}", START_FRAMING_CHAR, request, END_FRAMING_CHAR)));

                try
                {
                    RequestSent?.Invoke(request);
                }
                catch (Exception)
                {
                    // Ignore exceptions in event handlers
                }

                if (!waitForResponse)
                {
                    _waitingForResponse = false;
                    return null;
                }

                waitResult = _responseReady.Wait(_msgTimeout);
                _waitingForResponse = false;

                if (waitResult)
                {
                    if (_response != null)
                    {
                        return _response;
                    }
                    else
                    {
                        throw new Exception("Empty response");
                    }
                }
                else
                {
                    throw new Exception("Message timeout");
                }
            }
            else
            {
                throw new Exception("Socket not connected");
            }
        }
    }
}
