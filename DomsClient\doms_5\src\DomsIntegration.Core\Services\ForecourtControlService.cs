﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Messages.Requests;
using DomsIntegration.Core.Messages.Responses;
using DomsIntegration.Core.Models;
using DomsIntegration.Core.Utilities;

namespace DomsIntegration.Core.Services
{
    /// <summary>
    /// Service for general forecourt controller functions
    /// </summary>
    public class ForecourtControlService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the ForecourtControlService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public ForecourtControlService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Gets the current forecourt controller status
        /// </summary>
        /// <param name=""cancellationToken"">Cancellation token</param>
        /// <returns>FC status response</returns>
        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Requesting forecourt controller status");
                var request = new FcStatusRequest();
                return await _client.SendMessageAsync<FcStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to get FC status: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets the forecourt date and time
        /// </summary>
        /// <param name="dateTime">Date and time to set</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Setting forecourt date/time to {dateTime}");

                var request = new ChangeFcDateTimeRequest
                {
                    Data = new ChangeFcDateTimeRequestData
                    {
                        FcDateAndTime = new FcDateTime(dateTime)
                    }
                };

                return await _client.SendMessageAsync<ChangeFcDateTimeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to set FC date/time: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets the current forecourt date and time
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Date and time response</returns>
        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Requesting forecourt date/time");
                var request = new FcDateTimeRequest();
                return await _client.SendMessageAsync<FcDateTimeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to get FC date/time: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Changes the forecourt operation mode
        /// </summary>
        /// <param name="operationModeNo">Operation mode number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcOperationModeResponse> ChangeOperationModeAsync(int operationModeNo, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Changing forecourt operation mode to {operationModeNo}");

                var request = new ChangeFcOperationModeRequest
                {
                    Data = new ChangeFcOperationModeRequestData
                    {
                        FcOperationModeNo = operationModeNo
                    }
                };

                return await _client.SendMessageAsync<ChangeFcOperationModeResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to change FC operation mode: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets the price set for the forecourt
        /// </summary>
        /// <param name="priceSetId">Price set ID</param>
        /// <param name="priceGroupIds">Price group IDs</param>
        /// <param name="gradeIds">Grade IDs</param>
        /// <param name="priceGroups">Price groups data</param>
        /// <param name="activationDateTime">Optional activation date/time</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response</returns>
        public async Task<ChangeFcPriceSetResponse> SetPriceSetAsync(
            string priceSetId,
            string[] priceGroupIds,
            string[] gradeIds,
            string[][] priceGroups,
            DateTime? activationDateTime = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Setting price set {priceSetId}");

                var request = new ChangeFcPriceSetRequest
                {
                    Data = new ChangeFcPriceSetRequestData
                    {
                        FcPriceSetId = priceSetId,
                        FcPriceGroupId = priceGroupIds,
                        FcGradeId = gradeIds,
                        FcPriceGroups = priceGroups,
                        PriceSetActivationDateAndTime = activationDateTime?.ToString("yyyyMMddHHmmss") ?? "00000000000000"
                    }
                };

                return await _client.SendMessageAsync<ChangeFcPriceSetResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to set price set: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets the installation status of the forecourt
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Installation status response</returns>
        public async Task<FcInstallStatusResponse> GetInstallationStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Requesting forecourt installation status");
                var request = new FcInstallStatusRequest();
                return await _client.SendMessageAsync<FcInstallStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to get installation status: {ex.Message}");
                throw;
            }
        }
    }
}
