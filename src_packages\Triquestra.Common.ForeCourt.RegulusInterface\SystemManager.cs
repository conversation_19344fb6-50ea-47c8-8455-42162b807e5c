﻿using System;
using System.Collections.Generic;
using Triquestra.Common.PumpEsm.Comms;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.PumpEsm.Messaging.Request;


namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public class SystemManager:ForecourtConnection,ISystemManager
    {
        public SystemManager(string tcpHost, int tcpPort, string udpHost, int udpPort) : base(tcpHost, tcpPort, udpHost, udpPort)
        {

        }

        public void GetStatus(string applicationName)
        {
            SendSystemRequest(ForecourtCommandMessageTypes.APPLICATION_STATUS, applicationName);
        }

        public void StartApplication(string applicationName)
        {
            SendSystemRequest(ForecourtCommandMessageTypes.APPLICATION_START, applicationName);
        }

        public void StopApplication(string applicationName)
        {
            SendSystemRequest(ForecourtCommandMessageTypes.APPLICATION_STOP, applicationName);
        }

        public override void OnMessageReceived(List<IForecourtMessage> sender, EventArgs e)
        {
            base.OnMessageReceived(sender, e);
            Log("OnMessageReceived");
            if (sender == null)
            {
                Log("OnMessageReceived sender is null");
            }
        }

        public event EventHandler ApplicationStarted;

        public event EventHandler ApplicationStopped;

        public void ChangeFuelPrice(int blendId, int priceId, decimal newPrice)
        {
            SendCommand(new ForecourtPriceChangeRequest(blendId, priceId, newPrice));
        }

        public void ChangeFuelPrice(List<PriceChangeRequestModel> priceChanges)
        {
            SendCommand(new ForecourtPriceChangeRequest(priceChanges));
        }
    }
}
