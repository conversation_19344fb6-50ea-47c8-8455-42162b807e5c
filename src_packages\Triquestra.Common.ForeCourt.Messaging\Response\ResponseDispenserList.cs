﻿using System.Collections.Generic;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ResponseDispenserList:CommandResponse
    {
        public List<int> DispenserList { get; set; }

        public ResponseDispenserList(int sequenceNo, int sourceId, int targetId, ForecourtCommandResults commandResult, List<int> dispenserList) :
            base(sequenceNo, sourceId, targetId, commandResult, ForecourtCommandMessageTypes.DISPENSER_LIST)
        {
            DispenserList = dispenserList;
        }

        public ResponseDispenserList(int sequenceNo, int sourceId, int targetId,
            ForecourtCommandResults commandResult) :
                this(sequenceNo, sourceId, targetId, commandResult, new List<int>())
        {

        }

        public static ResponseDispenserList Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            var dispenserList = new List<int>();
            if (result == ForecourtCommandResults.SUCCESS)
            {
                var dispListNode = respNode.Element("DispenserList");
                if (dispListNode == null)
                    throw new XmlSchemaException("Command Response does not have <DispenserList> node");
                foreach (var disp in dispListNode.Descendants("Dispenser"))
                {
                    int id;
                    if (int.TryParse(disp.Attribute("ID").Value, out id))
                    {
                        dispenserList.Add(id);
                    }
                }
            }
            return new ResponseDispenserList(seqNo, sourceId, targetId, result, dispenserList);            
        }
    }
}
