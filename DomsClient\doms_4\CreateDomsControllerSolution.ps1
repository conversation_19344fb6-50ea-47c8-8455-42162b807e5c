# PowerShell Script to Generate DOMs Client Library Solution
# File: CreateDomsControllerSolution.ps1

param(
    [string]$SolutionName = "DomsClientLibrary",
    [string]$OutputPath = "C:\Source\DomsClientLibrary"
)

# Create directory structure
Write-Host "Creating solution structure..." -ForegroundColor Green

$directories = @(
    "$OutputPath",
    "$OutputPath\$SolutionName",
    "$OutputPath\$SolutionName\Properties",
    "$OutputPath\$SolutionName\Communication",
    "$OutputPath\$SolutionName\Authentication",
    "$OutputPath\$SolutionName\Messaging",
    "$OutputPath\$SolutionName\Functions",
    "$OutputPath\$SolutionName\Models",
    "$OutputPath\$SolutionName\Exceptions",
    "$OutputPath\$SolutionName\Utilities",
    "$OutputPath\$SolutionName.Tests",
    "$OutputPath\$SolutionName.Tests\Properties",
    "$OutputPath\$SolutionName.Examples",
    "$OutputPath\$SolutionName.Examples\Properties"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        Write-Host "Creating directory: $dir" -ForegroundColor Gray
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Generate Solution File
Write-Host "Generating solution file..." -ForegroundColor Green
$solutionContent = @"
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30114.105
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "$SolutionName", "$SolutionName\$SolutionName.csproj", "{A1B2C3D4-E5F6-7890-ABCD-123456789012}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "$SolutionName.Tests", "$SolutionName.Tests\$SolutionName.Tests.csproj", "{B2C3D4E5-F6G7-8901-BCDE-************}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "$SolutionName.Examples", "$SolutionName.Examples\$SolutionName.Examples.csproj", "{C3D4E5F6-G7H8-9012-CDEF-************}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-123456789012}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789012}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789012}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-123456789012}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2C3D4E5-F6G7-8901-BCDE-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3D4E5F6-G7H8-9012-CDEF-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
"@

try {
    $solutionContent | Out-File -FilePath "$OutputPath\$SolutionName.sln" -Encoding UTF8
    Write-Host "Solution file created successfully" -ForegroundColor Green
}
catch {
    Write-Error "Failed to create solution file: $($_.Exception.Message)"
    exit 1
}

# Generate Main Project File
Write-Host "Generating main project file..." -ForegroundColor Green
$mainProjectContent = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '`$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '`$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-123456789012}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>$SolutionName</RootNamespace>
    <AssemblyName>$SolutionName</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Configuration" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Authentication\DomsAuthenticationService.cs" />
    <Compile Include="Communication\DomsClient.cs" />
    <Compile Include="Communication\IDomsClient.cs" />
    <Compile Include="DomsClientFacade.cs" />
    <Compile Include="Exceptions\DomsExceptions.cs" />
    <Compile Include="Functions\DomsServices.cs" />
    <Compile Include="Models\DomsModels.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\Logging.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="`$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
"@

try {
    $mainProjectContent | Out-File -FilePath "$OutputPath\$SolutionName\$SolutionName.csproj" -Encoding UTF8
    Write-Host "Main project file created successfully" -ForegroundColor Green
}
catch {
    Write-Error "Failed to create main project file: $($_.Exception.Message)"
    exit 1
}

# Generate packages.config
Write-Host "Generating packages.config..." -ForegroundColor Green
$packagesConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
</packages>
"@

$packagesConfig | Out-File -FilePath "$OutputPath\$SolutionName\packages.config" -Encoding UTF8

# Generate AssemblyInfo.cs for main project
Write-Host "Generating AssemblyInfo.cs..." -ForegroundColor Green
$assemblyInfo = @"
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("$SolutionName")]
[assembly: AssemblyDescription("DOMs Controller Client Library using JPL")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("$SolutionName")]
[assembly: AssemblyCopyright("Copyright © 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("a1b2c3d4-e5f6-7890-abcd-123456789012")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
"@

try {
    $assemblyInfo | Out-File -FilePath "$OutputPath\$SolutionName\Properties\AssemblyInfo.cs" -Encoding UTF8
    Write-Host "AssemblyInfo.cs created successfully" -ForegroundColor Green
}
catch {
    Write-Error "Failed to create AssemblyInfo.cs: $($_.Exception.Message)"
    exit 1
}

# Generate Communication Interface
Write-Host "Generating communication interface..." -ForegroundColor Green
$communicationInterface = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Models;

namespace $SolutionName.Communication
{
    /// <summary>
    /// Interface for communication with DOMs controller
    /// </summary>
    public interface IDomsClient : IDisposable
    {
        /// <summary>
        /// Gets the current connection status
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// Gets or sets the connection timeout in milliseconds
        /// </summary>
        int ConnectionTimeout { get; set; }

        /// <summary>
        /// Event raised when connection status changes
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <summary>
        /// Event raised when a message is received
        /// </summary>
        event EventHandler<MessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// Establishes connection to the DOMs controller
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default);

        /// <summary>
        /// Disconnects from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the disconnection operation</returns>
        Task DisconnectAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Sends a message to the DOMs controller
        /// </summary>
        /// <typeparam name="TResponse">Expected response type</typeparam>
        /// <param name="request">Request message</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response message</returns>
        Task<TResponse> SendMessageAsync<TResponse>(IDomsMessage request, CancellationToken cancellationToken = default)
            where TResponse : class, IDomsMessage;

        /// <summary>
        /// Sends a message without expecting a response
        /// </summary>
        /// <param name="message">Message to send</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the send operation</returns>
        Task SendMessageAsync(IDomsMessage message, CancellationToken cancellationToken = default);
    }
}
"@

$communicationInterface | Out-File -FilePath "$OutputPath\$SolutionName\Communication\IDomsClient.cs" -Encoding UTF8

# Generate DOMs Client Implementation
Write-Host "Generating DOMs client implementation..." -ForegroundColor Green
$domsClient = @"
using System;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using Newtonsoft.Json;
using $SolutionName.Models;
using $SolutionName.Exceptions;
using $SolutionName.Utilities;

namespace $SolutionName.Communication
{
    /// <summary>
    /// Implementation of DOMs controller client
    /// </summary>
    public class DomsClient : IDomsClient
    {
        private TcpClient _tcpClient;
        private NetworkStream _stream;
        private bool _disposed;
        private readonly SemaphoreSlim _sendSemaphore;
        private readonly ILogger _logger;

        /// <inheritdoc />
        public bool IsConnected => _tcpClient?.Connected ?? false;

        /// <inheritdoc />
        public int ConnectionTimeout { get; set; } = 30000;

        /// <inheritdoc />
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <inheritdoc />
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsClient"/> class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public DomsClient(ILogger logger = null)
        {
            _logger = logger ?? new NullLogger();
            _sendSemaphore = new SemaphoreSlim(1, 1);
        }

        /// <inheritdoc />
        public async Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default)
        {
            if (endpoint == null)
                throw new ArgumentNullException(nameof(endpoint));

            try
            {
                _logger.LogInfo(`$"Connecting to DOMs controller at {endpoint.Host}:{endpoint.Port}");

                _tcpClient = new TcpClient();
                
                var connectTask = _tcpClient.ConnectAsync(endpoint.Host, endpoint.Port);
                var timeoutTask = Task.Delay(ConnectionTimeout, cancellationToken);
                
                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                    throw new DomsConnectionException("Connection timeout");

                await connectTask;

                _stream = _tcpClient.GetStream();
                _stream.ReadTimeout = ConnectionTimeout;
                _stream.WriteTimeout = ConnectionTimeout;

                OnConnectionStatusChanged(true);
                _logger.LogInfo("Successfully connected to DOMs controller");

                // Start listening for incoming messages
                _ = Task.Run(() => ListenForMessages(cancellationToken), cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(`$"Failed to connect to DOMs controller: {ex.Message}");
                await DisconnectAsync(cancellationToken);
                throw new DomsConnectionException("Failed to connect to DOMs controller", ex);
            }
        }

        /// <inheritdoc />
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo("Disconnecting from DOMs controller");

                _stream?.Close();
                _tcpClient?.Close();

                OnConnectionStatusChanged(false);
                _logger.LogInfo("Disconnected from DOMs controller");
            }
            catch (Exception ex)
            {
                _logger.LogError(`$"Error during disconnection: {ex.Message}");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<TResponse> SendMessageAsync<TResponse>(IDomsMessage request, CancellationToken cancellationToken = default)
            where TResponse : class, IDomsMessage
        {
            if (!IsConnected)
                throw new DomsConnectionException("Not connected to DOMs controller");

            await _sendSemaphore.WaitAsync(cancellationToken);
            try
            {
                var requestJson = JsonConvert.SerializeObject(request);
                var requestBytes = Encoding.UTF8.GetBytes(requestJson + "\n");

                _logger.LogDebug(`$"Sending message: {requestJson}");

                await _stream.WriteAsync(requestBytes, 0, requestBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);

                // Wait for response (simplified - in real implementation you'd need proper message correlation)
                var responseBuffer = new byte[4096];
                var bytesRead = await _stream.ReadAsync(responseBuffer, 0, responseBuffer.Length, cancellationToken);
                
                var responseJson = Encoding.UTF8.GetString(responseBuffer, 0, bytesRead).Trim();
                _logger.LogDebug(`$"Received response: {responseJson}");

                return JsonConvert.DeserializeObject<TResponse>(responseJson);
            }
            finally
            {
                _sendSemaphore.Release();
            }
        }

        /// <inheritdoc />
        public async Task SendMessageAsync(IDomsMessage message, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new DomsConnectionException("Not connected to DOMs controller");

            await _sendSemaphore.WaitAsync(cancellationToken);
            try
            {
                var messageJson = JsonConvert.SerializeObject(message);
                var messageBytes = Encoding.UTF8.GetBytes(messageJson + "\n");

                _logger.LogDebug(`$"Sending message: {messageJson}");

                await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);
            }
            finally
            {
                _sendSemaphore.Release();
            }
        }

        private async Task ListenForMessages(CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];
            
            while (IsConnected && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        var messageJson = Encoding.UTF8.GetString(buffer, 0, bytesRead).Trim();
                        OnMessageReceived(messageJson);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(`$"Error listening for messages: {ex.Message}");
                    break;
                }
            }
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs(isConnected));
        }

        private void OnMessageReceived(string messageJson)
        {
            MessageReceived?.Invoke(this, new MessageReceivedEventArgs(messageJson));
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait(5000);
                _sendSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
"@

$domsClient | Out-File -FilePath "$OutputPath\$SolutionName\Communication\DomsClient.cs" -Encoding UTF8

# Generate Models
Write-Host "Generating models..." -ForegroundColor Green
$models = @"
using System;
using System.Collections.Generic;

namespace $SolutionName.Models
{
    /// <summary>
    /// Base interface for all DOMs messages
    /// </summary>
    public interface IDomsMessage
    {
        /// <summary>
        /// Message type identifier
        /// </summary>
        string MessageType { get; }

        /// <summary>
        /// Message identifier for correlation
        /// </summary>
        string MessageId { get; set; }

        /// <summary>
        /// Timestamp when message was created
        /// </summary>
        DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// DOMs controller endpoint configuration
    /// </summary>
    public class DomsEndpoint
    {
        /// <summary>
        /// Host address
        /// </summary>
        public string Host { get; set; }

        /// <summary>
        /// Port number
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// Use SSL/TLS encryption
        /// </summary>
        public bool UseSsl { get; set; }

        /// <summary>
        /// Connection timeout in milliseconds
        /// </summary>
        public int TimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// Connection status changed event arguments
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; }

        public ConnectionStatusChangedEventArgs(bool isConnected)
        {
            IsConnected = isConnected;
        }
    }

    /// <summary>
    /// Message received event arguments
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public string MessageJson { get; }

        public MessageReceivedEventArgs(string messageJson)
        {
            MessageJson = messageJson;
        }
    }

    /// <summary>
    /// Base DOMs message implementation
    /// </summary>
    public abstract class DomsMessageBase : IDomsMessage
    {
        /// <inheritdoc />
        public abstract string MessageType { get; }

        /// <inheritdoc />
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        /// <inheritdoc />
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Logon request message
    /// </summary>
    public class LogonRequest : DomsMessageBase
    {
        public override string MessageType => "LogonRequest";

        /// <summary>
        /// Username for authentication
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// Password for authentication
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// Client application identifier
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// Client version
        /// </summary>
        public string ClientVersion { get; set; }
    }

    /// <summary>
    /// Logon response message
    /// </summary>
    public class LogonResponse : DomsMessageBase
    {
        public override string MessageType => "LogonResponse";

        /// <summary>
        /// Indicates if logon was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Session token for subsequent requests
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Error message if logon failed
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Session expiry time
        /// </summary>
        public DateTime? ExpiryTime { get; set; }
    }

    /// <summary>
    /// Forecourt status request
    /// </summary>
    public class ForecourtStatusRequest : DomsMessageBase
    {
        public override string MessageType => "ForecourtStatusRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }
    }

    /// <summary>
    /// Forecourt status response
    /// </summary>
    public class ForecourtStatusResponse : DomsMessageBase
    {
        public override string MessageType => "ForecourtStatusResponse";

        /// <summary>
        /// List of dispensers and their status
        /// </summary>
        public List<DispenserStatus> Dispensers { get; set; } = new List<DispenserStatus>();

        /// <summary>
        /// Overall forecourt status
        /// </summary>
        public ForecourtState Status { get; set; }
    }

    /// <summary>
    /// Individual dispenser status
    /// </summary>
    public class DispenserStatus
    {
        /// <summary>
        /// Dispenser identifier
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Current dispenser state
        /// </summary>
        public DispenserState State { get; set; }

        /// <summary>
        /// Nozzles on this dispenser
        /// </summary>
        public List<NozzleStatus> Nozzles { get; set; } = new List<NozzleStatus>();

        /// <summary>
        /// Current transaction if any
        /// </summary>
        public TransactionInfo CurrentTransaction { get; set; }
    }

    /// <summary>
    /// Nozzle status information
    /// </summary>
    public class NozzleStatus
    {
        /// <summary>
        /// Nozzle identifier
        /// </summary>
        public int NozzleId { get; set; }

        /// <summary>
        /// Current nozzle state
        /// </summary>
        public NozzleState State { get; set; }

        /// <summary>
        /// Product type
        /// </summary>
        public ProductType Product { get; set; }

        /// <summary>
        /// Current price per unit
        /// </summary>
        public decimal Price { get; set; }
    }

    /// <summary>
    /// Transaction information
    /// </summary>
    public class TransactionInfo
    {
        /// <summary>
        /// Transaction identifier
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Volume dispensed
        /// </summary>
        public decimal Volume { get; set; }

        /// <summary>
        /// Amount charged
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Price per unit
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Product dispensed
        /// </summary>
        public ProductType Product { get; set; }

        /// <summary>
        /// Transaction start time
        /// </summary>
        public DateTime StartTime { get; set; }
    }

    /// <summary>
    /// Authorize dispense request
    /// </summary>
    public class AuthorizeDispenseRequest : DomsMessageBase
    {
        public override string MessageType => "AuthorizeDispenseRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Dispenser to authorize
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Nozzle to authorize
        /// </summary>
        public int NozzleId { get; set; }

        /// <summary>
        /// Maximum amount to authorize
        /// </summary>
        public decimal? MaxAmount { get; set; }

        /// <summary>
        /// Maximum volume to authorize
        /// </summary>
        public decimal? MaxVolume { get; set; }

        /// <summary>
        /// Price override
        /// </summary>
        public decimal? PriceOverride { get; set; }
    }

    /// <summary>
    /// Authorize dispense response
    /// </summary>
    public class AuthorizeDispenseResponse : DomsMessageBase
    {
        public override string MessageType => "AuthorizeDispenseResponse";

        /// <summary>
        /// Indicates if authorization was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Authorization identifier
        /// </summary>
        public string AuthorizationId { get; set; }

        /// <summary>
        /// Error message if authorization failed
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Stop dispense request
    /// </summary>
    public class StopDispenseRequest : DomsMessageBase
    {
        public override string MessageType => "StopDispenseRequest";

        /// <summary>
        /// Session token
        /// </summary>
        public string SessionToken { get; set; }

        /// <summary>
        /// Dispenser to stop
        /// </summary>
        public int DispenserId { get; set; }

        /// <summary>
        /// Authorization ID to stop
        /// </summary>
        public string AuthorizationId { get; set; }
    }

    /// <summary>
    /// Stop dispense response
    /// </summary>
    public class StopDispenseResponse : DomsMessageBase
    {
        public override string MessageType => "StopDispenseResponse";

        /// <summary>
        /// Indicates if stop was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Final transaction details
        /// </summary>
        public TransactionInfo FinalTransaction { get; set; }

        /// <summary>
        /// Error message if stop failed
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Forecourt state enumeration
    /// </summary>
    public enum ForecourtState
    {
        Unknown = 0,
        Normal = 1,
        Emergency = 2,
        Maintenance = 3,
        Offline = 4
    }

    /// <summary>
    /// Dispenser state enumeration
    /// </summary>
    public enum DispenserState
    {
        Unknown = 0,
        Idle = 1,
        Authorized = 2,
        Dispensing = 3,
        Stopped = 4,
        OutOfOrder = 5,
        Emergency = 6
    }

    /// <summary>
    /// Nozzle state enumeration
    /// </summary>
    public enum NozzleState
    {
        Unknown = 0,
        Idle = 1,
        Authorized = 2,
        Lifted = 3,
        Dispensing = 4,
        Stopped = 5,
        OutOfOrder = 6
    }

    /// <summary>
    /// Product type enumeration
    /// </summary>
    public enum ProductType
    {
        Unknown = 0,
        Gasoline87 = 1,
        Gasoline89 = 2,
        Gasoline91 = 3,
        Gasoline93 = 4,
        Diesel = 5,
        Premium = 6,
        Kerosene = 7
    }
}
"@

$models | Out-File -FilePath "$OutputPath\$SolutionName\Models\DomsModels.cs" -Encoding UTF8

# Generate Authentication Service
Write-Host "Generating authentication service..." -ForegroundColor Green
$authService = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Communication;
using $SolutionName.Models;
using $SolutionName.Exceptions;

namespace $SolutionName.Authentication
{
    /// <summary>
    /// Interface for DOMs authentication operations
    /// </summary>
    public interface IDomsAuthenticationService
    {
        /// <summary>
        /// Current session token
        /// </summary>
        string SessionToken { get; }

        /// <summary>
        /// Indicates if currently authenticated
        /// </summary>
        bool IsAuthenticated { get; }

        /// <summary>
        /// Session expiry time
        /// </summary>
        DateTime? SessionExpiry { get; }

        /// <summary>
        /// Authenticates with the DOMs controller
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="clientId">Client identifier</param>
        /// <param name="clientVersion">Client version</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authentication result</returns>
        Task<bool> AuthenticateAsync(string username, string password, string clientId, string clientVersion, CancellationToken cancellationToken = default);

        /// <summary>
        /// Logs out from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the logout operation</returns>
        Task LogoutAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Refreshes the current session
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if refresh was successful</returns>
        Task<bool> RefreshSessionAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Implementation of DOMs authentication service
    /// </summary>
    public class DomsAuthenticationService : IDomsAuthenticationService
    {
        private readonly IDomsClient _client;
        private string _sessionToken;
        private DateTime? _sessionExpiry;

        /// <inheritdoc />
        public string SessionToken => _sessionToken;

        /// <inheritdoc />
        public bool IsAuthenticated => !string.IsNullOrEmpty(_sessionToken) && 
                                     (_sessionExpiry == null || _sessionExpiry > DateTime.UtcNow);

        /// <inheritdoc />
        public DateTime? SessionExpiry => _sessionExpiry;

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        public DomsAuthenticationService(IDomsClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        /// <inheritdoc />
        public async Task<bool> AuthenticateAsync(string username, string password, string clientId, string clientVersion, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrEmpty(username))
                throw new ArgumentException("Username cannot be null or empty", nameof(username));
            
            if (string.IsNullOrEmpty(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            var request = new LogonRequest
            {
                Username = username,
                Password = password,
                ClientId = clientId ?? "DomsClientLibrary",
                ClientVersion = clientVersion ?? "1.0.0"
            };

            try
            {
                var response = await _client.SendMessageAsync<LogonResponse>(request, cancellationToken);
                
                if (response.Success)
                {
                    _sessionToken = response.SessionToken;
                    _sessionExpiry = response.ExpiryTime;
                    return true;
                }
                else
                {
                    throw new DomsAuthenticationException(response.ErrorMessage ?? "Authentication failed");
                }
            }
            catch (DomsAuthenticationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new DomsAuthenticationException("Authentication failed due to communication error", ex);
            }
        }

        /// <inheritdoc />
        public async Task LogoutAsync(CancellationToken cancellationToken = default)
        {
            // Implementation would send logout message
            _sessionToken = null;
            _sessionExpiry = null;
            
            // In a real implementation, you would send a logout message to the server
            await Task.CompletedTask;
        }

        /// <inheritdoc />
        public async Task<bool> RefreshSessionAsync(CancellationToken cancellationToken = default)
        {
            // Implementation would send session refresh message
            // For now, just return current authentication status
            return await Task.FromResult(IsAuthenticated);
        }
    }
}
"@

$authService | Out-File -FilePath "$OutputPath\$SolutionName\Authentication\DomsAuthenticationService.cs" -Encoding UTF8

# Generate Functions Services
Write-Host "Generating function services..." -ForegroundColor Green
$functionsService = @"
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Communication;
using $SolutionName.Models;
using $SolutionName.Authentication;
using $SolutionName.Exceptions;

namespace $SolutionName.Functions
{
    /// <summary>
    /// Interface for general forecourt controller functions
    /// </summary>
    public interface IForecourtService
    {
        /// <summary>
        /// Gets the current status of all dispensers
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Forecourt status information</returns>
        Task<ForecourtStatusResponse> GetForecourtStatusAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of a specific dispenser
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dispenser status information</returns>
        Task<DispenserStatus> GetDispenserStatusAsync(int dispenserId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets emergency stop for the entire forecourt
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if emergency stop was successful</returns>
        Task<bool> EmergencyStopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Clears emergency stop for the forecourt
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if emergency clear was successful</returns>
        Task<bool> ClearEmergencyAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Interface for dispense control functions
    /// </summary>
    public interface IDispenseService
    {
        /// <summary>
        /// Authorizes dispensing for a specific nozzle
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="nozzleId">Nozzle identifier</param>
        /// <param name="maxAmount">Maximum amount to authorize</param>
        /// <param name="maxVolume">Maximum volume to authorize</param>
        /// <param name="priceOverride">Price override if applicable</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authorization response</returns>
        Task<AuthorizeDispenseResponse> AuthorizeDispenseAsync(
            int dispenserId, 
            int nozzleId, 
            decimal? maxAmount = null, 
            decimal? maxVolume = null, 
            decimal? priceOverride = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops dispensing for a specific authorization
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="authorizationId">Authorization identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Stop dispense response</returns>
        Task<StopDispenseResponse> StopDispenseAsync(
            int dispenserId, 
            string authorizationId, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Suspends dispensing (temporary stop)
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if suspend was successful</returns>
        Task<bool> SuspendDispenseAsync(int dispenserId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Resumes suspended dispensing
        /// </summary>
        /// <param name="dispenserId">Dispenser identifier</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if resume was successful</returns>
        Task<bool> ResumeDispenseAsync(int dispenserId, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Implementation of forecourt service
    /// </summary>
    public class ForecourtService : IForecourtService
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ForecourtService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        /// <param name="authService">Authentication service</param>
        public ForecourtService(IDomsClient client, IDomsAuthenticationService authService)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        /// <inheritdoc />
        public async Task<ForecourtStatusResponse> GetForecourtStatusAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            var request = new ForecourtStatusRequest
            {
                SessionToken = _authService.SessionToken
            };

            return await _client.SendMessageAsync<ForecourtStatusResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<DispenserStatus> GetDispenserStatusAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            var forecourtStatus = await GetForecourtStatusAsync(cancellationToken);
            
            var dispenser = forecourtStatus.Dispensers.Find(d => d.DispenserId == dispenserId);
            if (dispenser == null)
                throw new DomsOperationException(`$"Dispenser {dispenserId} not found");

            return dispenser;
        }

        /// <inheritdoc />
        public async Task<bool> EmergencyStopAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send emergency stop message
            return await Task.FromResult(true);
        }

        /// <inheritdoc />
        public async Task<bool> ClearEmergencyAsync(CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send clear emergency message
            return await Task.FromResult(true);
        }

        private void EnsureAuthenticated()
        {
            if (!_authService.IsAuthenticated)
                throw new DomsAuthenticationException("Not authenticated. Please login first.");
        }
    }

    /// <summary>
    /// Implementation of dispense service
    /// </summary>
    public class DispenseService : IDispenseService
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DispenseService"/> class
        /// </summary>
        /// <param name="client">DOMs client instance</param>
        /// <param name="authService">Authentication service</param>
        public DispenseService(IDomsClient client, IDomsAuthenticationService authService)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        }

        /// <inheritdoc />
        public async Task<AuthorizeDispenseResponse> AuthorizeDispenseAsync(
            int dispenserId, 
            int nozzleId, 
            decimal? maxAmount = null, 
            decimal? maxVolume = null, 
            decimal? priceOverride = null,
            CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            var request = new AuthorizeDispenseRequest
            {
                SessionToken = _authService.SessionToken,
                DispenserId = dispenserId,
                NozzleId = nozzleId,
                MaxAmount = maxAmount,
                MaxVolume = maxVolume,
                PriceOverride = priceOverride
            };

            return await _client.SendMessageAsync<AuthorizeDispenseResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<StopDispenseResponse> StopDispenseAsync(
            int dispenserId, 
            string authorizationId, 
            CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();

            if (string.IsNullOrEmpty(authorizationId))
                throw new ArgumentException("Authorization ID cannot be null or empty", nameof(authorizationId));

            var request = new StopDispenseRequest
            {
                SessionToken = _authService.SessionToken,
                DispenserId = dispenserId,
                AuthorizationId = authorizationId
            };

            return await _client.SendMessageAsync<StopDispenseResponse>(request, cancellationToken);
        }

        /// <inheritdoc />
        public async Task<bool> SuspendDispenseAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send suspend message
            return await Task.FromResult(true);
        }

        /// <inheritdoc />
        public async Task<bool> ResumeDispenseAsync(int dispenserId, CancellationToken cancellationToken = default)
        {
            EnsureAuthenticated();
            // Implementation would send resume message
            return await Task.FromResult(true);
        }

        private void EnsureAuthenticated()
        {
            if (!_authService.IsAuthenticated)
                throw new DomsAuthenticationException("Not authenticated. Please login first.");
        }
    }
}
"@

$functionsService | Out-File -FilePath "$OutputPath\$SolutionName\Functions\DomsServices.cs" -Encoding UTF8

# Generate Exceptions
Write-Host "Generating exceptions..." -ForegroundColor Green
$exceptions = @"
using System;

namespace $SolutionName.Exceptions
{
    /// <summary>
    /// Base exception for all DOMs-related errors
    /// </summary>
    public class DomsException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        public DomsException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when connection-related errors occur
    /// </summary>
    public class DomsConnectionException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        public DomsConnectionException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsConnectionException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsConnectionException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when authentication-related errors occur
    /// </summary>
    public class DomsAuthenticationException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        public DomsAuthenticationException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsAuthenticationException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsAuthenticationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when operation-related errors occur
    /// </summary>
    public class DomsOperationException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        public DomsOperationException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsOperationException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsOperationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
"@

$exceptions | Out-File -FilePath "$OutputPath\$SolutionName\Exceptions\DomsExceptions.cs" -Encoding UTF8

# Generate Utilities
Write-Host "Generating utilities..." -ForegroundColor Green
$utilities = @"
using System;

namespace $SolutionName.Utilities
{
    /// <summary>
    /// Interface for logging operations
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// Logs an informational message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogInfo(string message);

        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogDebug(string message);

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogWarning(string message);

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Message to log</param>
        void LogError(string message);

        /// <summary>
        /// Logs an error message with exception
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Exception details</param>
        void LogError(string message, Exception exception);
    }

    /// <summary>
    /// Null logger implementation that does nothing
    /// </summary>
    public class NullLogger : ILogger
    {
        /// <inheritdoc />
        public void LogInfo(string message) { }

        /// <inheritdoc />
        public void LogDebug(string message) { }

        /// <inheritdoc />
        public void LogWarning(string message) { }

        /// <inheritdoc />
        public void LogError(string message) { }

        /// <inheritdoc />
        public void LogError(string message, Exception exception) { }
    }

    /// <summary>
    /// Console logger implementation
    /// </summary>
    public class ConsoleLogger : ILogger
    {
        /// <inheritdoc />
        public void LogInfo(string message)
        {
            Console.WriteLine(`$"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogDebug(string message)
        {
            Console.WriteLine(`$"[DEBUG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogWarning(string message)
        {
            Console.WriteLine(`$"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogError(string message)
        {
            Console.WriteLine(`$"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
        }

        /// <inheritdoc />
        public void LogError(string message, Exception exception)
        {
            Console.WriteLine(`$"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            Console.WriteLine(`$"Exception: {exception}");
        }
    }
}
"@

$utilities | Out-File -FilePath "$OutputPath\$SolutionName\Utilities\Logging.cs" -Encoding UTF8

# Generate Main Client Class
Write-Host "Generating main client facade..." -ForegroundColor Green
$mainClient = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using $SolutionName.Communication;
using $SolutionName.Authentication;
using $SolutionName.Functions;
using $SolutionName.Models;
using $SolutionName.Utilities;

namespace $SolutionName
{
    /// <summary>
    /// Main DOMs client that provides access to all functionality
    /// </summary>
    public class DomsClientFacade : IDisposable
    {
        private readonly IDomsClient _client;
        private readonly IDomsAuthenticationService _authService;
        private readonly IForecourtService _forecourtService;
        private readonly IDispenseService _dispenseService;
        private bool _disposed;

        /// <summary>
        /// Gets the authentication service
        /// </summary>
        public IDomsAuthenticationService Authentication => _authService;

        /// <summary>
        /// Gets the forecourt service
        /// </summary>
        public IForecourtService Forecourt => _forecourtService;

        /// <summary>
        /// Gets the dispense service
        /// </summary>
        public IDispenseService Dispense => _dispenseService;

        /// <summary>
        /// Gets the connection status
        /// </summary>
        public bool IsConnected => _client.IsConnected;

        /// <summary>
        /// Gets the authentication status
        /// </summary>
        public bool IsAuthenticated => _authService.IsAuthenticated;

        /// <summary>
        /// Event raised when connection status changes
        /// </summary>
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged
        {
            add => _client.ConnectionStatusChanged += value;
            remove => _client.ConnectionStatusChanged -= value;
        }

        /// <summary>
        /// Event raised when a message is received
        /// </summary>
        public event EventHandler<MessageReceivedEventArgs> MessageReceived
        {
            add => _client.MessageReceived += value;
            remove => _client.MessageReceived -= value;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsClientFacade"/> class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public DomsClientFacade(ILogger logger = null)
        {
            _client = new DomsClient(logger);
            _authService = new DomsAuthenticationService(_client);
            _forecourtService = new ForecourtService(_client, _authService);
            _dispenseService = new DispenseService(_client, _authService);
        }

        /// <summary>
        /// Connects to the DOMs controller
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        public async Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default)
        {
            await _client.ConnectAsync(endpoint, cancellationToken);
        }

        /// <summary>
        /// Connects and authenticates in one operation
        /// </summary>
        /// <param name="endpoint">Connection endpoint</param>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="clientId">Client identifier</param>
        /// <param name="clientVersion">Client version</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if both connection and authentication were successful</returns>
        public async Task<bool> ConnectAndAuthenticateAsync(
            DomsEndpoint endpoint, 
            string username, 
            string password, 
            string clientId = null, 
            string clientVersion = null,
            CancellationToken cancellationToken = default)
        {
            await ConnectAsync(endpoint, cancellationToken);
            return await _authService.AuthenticateAsync(username, password, clientId, clientVersion, cancellationToken);
        }

        /// <summary>
        /// Disconnects from the DOMs controller
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the disconnection operation</returns>
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            if (_authService.IsAuthenticated)
            {
                await _authService.LogoutAsync(cancellationToken);
            }
            
            await _client.DisconnectAsync(cancellationToken);
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait(5000);
                _client?.Dispose();
                _disposed = true;
            }
        }
    }
}
"@

$mainClient | Out-File -FilePath "$OutputPath\$SolutionName\DomsClientFacade.cs" -Encoding UTF8

# Generate Test Project
Write-Host "Generating test project..." -ForegroundColor Green
$testProject = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '`$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '`$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B2C3D4E5-F6G7-8901-BCDE-************}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>$SolutionName.Tests</RootNamespace>
    <AssemblyName>$SolutionName.Tests</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="nunit.framework, Version=3.13.3.0, Culture=neutral, PublicKeyToken=2638cd05610744eb">
      <HintPath>..\packages\NUnit.3.13.3\lib\net45\nunit.framework.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\$SolutionName\$SolutionName.csproj">
      <Project>{A1B2C3D4-E5F6-7890-ABCD-123456789012}</Project>
      <Name>$SolutionName</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="`$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
"@

$testProject | Out-File -FilePath "$OutputPath\$SolutionName.Tests\$SolutionName.Tests.csproj" -Encoding UTF8

# Generate Test packages.config
$testPackagesConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="NUnit" version="3.13.3" targetFramework="net48" />
</packages>
"@

$testPackagesConfig | Out-File -FilePath "$OutputPath\$SolutionName.Tests\packages.config" -Encoding UTF8

# Generate Test AssemblyInfo
$testAssemblyInfo = @"
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("$SolutionName.Tests")]
[assembly: AssemblyDescription("Unit tests for $SolutionName")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("$SolutionName.Tests")]
[assembly: AssemblyCopyright("Copyright © 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("b2c3d4e5-f6g7-8901-bcde-************")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
"@

$testAssemblyInfo | Out-File -FilePath "$OutputPath\$SolutionName.Tests\Properties\AssemblyInfo.cs" -Encoding UTF8

# Generate Example Project
Write-Host "Generating example project..." -ForegroundColor Green
$exampleProject = @"
<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('`$(MSBuildExtensionsPath)\`$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '`$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '`$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C3D4E5F6-G7H8-9012-CDEF-************}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>$SolutionName.Examples</RootNamespace>
    <AssemblyName>$SolutionName.Examples</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '`$(Configuration)|`$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="Microsoft.CSharp" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\$SolutionName\$SolutionName.csproj">
      <Project>{A1B2C3D4-E5F6-7890-ABCD-123456789012}</Project>
      <Name>$SolutionName</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="`$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
"@

$exampleProject | Out-File -FilePath "$OutputPath\$SolutionName.Examples\$SolutionName.Examples.csproj" -Encoding UTF8

# Generate Example AssemblyInfo
$exampleAssemblyInfo = @"
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyTitle("$SolutionName.Examples")]
[assembly: AssemblyDescription("Examples for $SolutionName")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("$SolutionName.Examples")]
[assembly: AssemblyCopyright("Copyright © 2024")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: ComVisible(false)]
[assembly: Guid("c3d4e5f6-g7h8-9012-cdef-************")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
"@

$exampleAssemblyInfo | Out-File -FilePath "$OutputPath\$SolutionName.Examples\Properties\AssemblyInfo.cs" -Encoding UTF8

# Generate Example Program
Write-Host "Generating example program..." -ForegroundColor Green
$exampleProgram = @"
using System;
using System.Threading.Tasks;
using $SolutionName;
using $SolutionName.Models;
using $SolutionName.Utilities;

namespace $SolutionName.Examples
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var logger = new ConsoleLogger();
            
            using (var client = new DomsClientFacade(logger))
            {
                try
                {
                    // Configure endpoint
                    var endpoint = new DomsEndpoint
                    {
                        Host = "localhost",
                        Port = 8080,
                        UseSsl = false,
                        TimeoutMs = 30000
                    };

                    // Connect and authenticate
                    Console.WriteLine("Connecting to DOMs controller...");
                    var success = await client.ConnectAndAuthenticateAsync(
                        endpoint, 
                        "admin", 
                        "password", 
                        "ExampleClient", 
                        "1.0.0");

                    if (success)
                    {
                        Console.WriteLine("Successfully connected and authenticated!");

                        // Get forecourt status
                        Console.WriteLine("Getting forecourt status...");
                        var status = await client.Forecourt.GetForecourtStatusAsync();
                        Console.WriteLine(`$"Forecourt status: {status.Status}");
                        Console.WriteLine(`$"Number of dispensers: {status.Dispensers.Count}");

                        // Authorize dispense on dispenser 1, nozzle 1
                        if (status.Dispensers.Count > 0)
                        {
                            Console.WriteLine("Authorizing dispense...");
                            var authResponse = await client.Dispense.AuthorizeDispenseAsync(
                                1, 1, maxAmount: 50.00m);

                            if (authResponse.Success)
                            {
                                Console.WriteLine(`$"Dispense authorized: {authResponse.AuthorizationId}");
                                
                                // Wait a bit then stop
                                await Task.Delay(5000);
                                
                                Console.WriteLine("Stopping dispense...");
                                var stopResponse = await client.Dispense.StopDispenseAsync(
                                    1, authResponse.AuthorizationId);
                                    
                                if (stopResponse.Success)
                                {
                                    Console.WriteLine("Dispense stopped successfully");
                                    if (stopResponse.FinalTransaction != null)
                                    {
                                        Console.WriteLine(`$"Final amount: {stopResponse.FinalTransaction.Amount:C}");
                                        Console.WriteLine(`$"Final volume: {stopResponse.FinalTransaction.Volume:F2}");
                                    }
                                }
                            }
                            else
                            {
                                Console.WriteLine(`$"Failed to authorize dispense: {authResponse.ErrorMessage}");
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed to authenticate");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(`$"Error: {ex.Message}");
                }
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}
"@

$exampleProgram | Out-File -FilePath "$OutputPath\$SolutionName.Examples\Program.cs" -Encoding UTF8

