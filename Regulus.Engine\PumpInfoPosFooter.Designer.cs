﻿namespace Triquestra.Common.PumpEsm
{
    partial class PumpInfoPosFooter
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
			this.components = new System.ComponentModel.Container();
			this.PanelDispensers = new System.Windows.Forms.FlowLayoutPanel();
			this.pnlButtons = new System.Windows.Forms.FlowLayoutPanel();
			this.BtnStop = new Triquestra.Common.GUI.OnScreenStateButton();
			this.BtnStopAll = new Triquestra.Common.GUI.OnScreenButton();
			this.BtnPlay = new Triquestra.Common.GUI.OnScreenStateButton();
			this.BtnPlayAll = new Triquestra.Common.GUI.OnScreenButton();
			this.BtnPrepay = new Triquestra.Common.GUI.OnScreenStateButton();
			this.BtnToggle = new Triquestra.Common.GUI.OnScreenButton();
			this.tmrWakeUp = new System.Windows.Forms.Timer(this.components);
			this._blinkingTimer = new System.Windows.Forms.Timer(this.components);
			this.pnlButtons.SuspendLayout();
			this.SuspendLayout();
			// 
			// PanelDispensers
			// 
			this.PanelDispensers.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.PanelDispensers.BackColor = System.Drawing.Color.White;
			this.PanelDispensers.Location = new System.Drawing.Point(0, 0);
			this.PanelDispensers.Name = "PanelDispensers";
			this.PanelDispensers.Size = new System.Drawing.Size(974, 185);
			this.PanelDispensers.TabIndex = 1;
			this.PanelDispensers.BackColorChanged += new System.EventHandler(this.PanelDispensers_BackColorChanged);
			this.PanelDispensers.Click += new System.EventHandler(this.PanelDispensers_Click);
			// 
			// pnlButtons
			// 
			this.pnlButtons.BackColor = System.Drawing.Color.Silver;
			this.pnlButtons.Controls.Add(this.BtnStop);
			this.pnlButtons.Controls.Add(this.BtnStopAll);
			this.pnlButtons.Controls.Add(this.BtnPlay);
			this.pnlButtons.Controls.Add(this.BtnPlayAll);
			this.pnlButtons.Controls.Add(this.BtnPrepay);
			this.pnlButtons.Controls.Add(this.BtnToggle);
			this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Right;
			this.pnlButtons.Location = new System.Drawing.Point(977, 0);
			this.pnlButtons.Name = "pnlButtons";
			this.pnlButtons.Size = new System.Drawing.Size(158, 185);
			this.pnlButtons.TabIndex = 2;
			// 
			// BtnStop
			// 
			this.BtnStop.AutoSize = true;
			this.BtnStop.BackColor = System.Drawing.Color.Linen;
			this.BtnStop.BorderColor = System.Drawing.Color.Black;
			this.BtnStop.DisplayValue = "Stop";
			this.BtnStop.Down = false;
			this.BtnStop.DownColour = System.Drawing.Color.LightGray;
			//this.BtnStop.DrawRoundButtons = false;
			this.BtnStop.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnStop.Location = new System.Drawing.Point(0, 0);
			this.BtnStop.Margin = new System.Windows.Forms.Padding(0);
			this.BtnStop.Name = "BtnStop";
			this.BtnStop.SendValue = null;
			this.BtnStop.Size = new System.Drawing.Size(78, 61);
			this.BtnStop.TabIndex = 0;
			this.BtnStop.TargetControl = null;
			// 
			// BtnStopAll
			// 
			this.BtnStopAll.AutoSize = true;
			this.BtnStopAll.BackColor = System.Drawing.Color.Linen;
			this.BtnStopAll.BorderColor = System.Drawing.Color.Black;
			this.BtnStopAll.DisplayValue = "Stop All";
			this.BtnStopAll.DownColour = System.Drawing.Color.LightGray;
			//this.BtnStopAll.DrawRoundButtons = false;
			this.BtnStopAll.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnStopAll.Location = new System.Drawing.Point(78, 0);
			this.BtnStopAll.Margin = new System.Windows.Forms.Padding(0);
			this.BtnStopAll.Name = "BtnStopAll";
			this.BtnStopAll.SendValue = null;
			this.BtnStopAll.Size = new System.Drawing.Size(78, 61);
			this.BtnStopAll.TabIndex = 1;
			this.BtnStopAll.TargetControl = null;
			// 
			// BtnPlay
			// 
			this.BtnPlay.AutoSize = true;
			this.BtnPlay.BackColor = System.Drawing.Color.Linen;
			this.BtnPlay.BorderColor = System.Drawing.Color.Black;
			this.BtnPlay.DisplayValue = "Play";
			this.BtnPlay.Down = false;
			this.BtnPlay.DownColour = System.Drawing.Color.LightGray;
			//this.BtnPlay.DrawRoundButtons = false;
			this.BtnPlay.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnPlay.Location = new System.Drawing.Point(0, 61);
			this.BtnPlay.Margin = new System.Windows.Forms.Padding(0);
			this.BtnPlay.Name = "BtnPlay";
			this.BtnPlay.SendValue = null;
			this.BtnPlay.Size = new System.Drawing.Size(78, 61);
			this.BtnPlay.TabIndex = 2;
			this.BtnPlay.TargetControl = null;
			// 
			// BtnPlayAll
			// 
			this.BtnPlayAll.AutoSize = true;
			this.BtnPlayAll.BackColor = System.Drawing.Color.Linen;
			this.BtnPlayAll.BorderColor = System.Drawing.Color.Black;
			this.BtnPlayAll.DisplayValue = "Play All";
			this.BtnPlayAll.DownColour = System.Drawing.Color.LightGray;
			//this.BtnPlayAll.DrawRoundButtons = false;
			this.BtnPlayAll.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnPlayAll.Location = new System.Drawing.Point(78, 61);
			this.BtnPlayAll.Margin = new System.Windows.Forms.Padding(0);
			this.BtnPlayAll.Name = "BtnPlayAll";
			this.BtnPlayAll.SendValue = null;
			this.BtnPlayAll.Size = new System.Drawing.Size(78, 61);
			this.BtnPlayAll.TabIndex = 3;
			this.BtnPlayAll.TargetControl = null;
			// 
			// BtnPrepay
			// 
			this.BtnPrepay.AutoSize = true;
			this.BtnPrepay.BackColor = System.Drawing.Color.Linen;
			this.BtnPrepay.BorderColor = System.Drawing.Color.Black;
			this.BtnPrepay.DisplayValue = "Prepay";
			this.BtnPrepay.Down = false;
			this.BtnPrepay.DownColour = System.Drawing.Color.LightGray;
			//this.BtnPrepay.DrawRoundButtons = false;
			this.BtnPrepay.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnPrepay.Location = new System.Drawing.Point(0, 122);
			this.BtnPrepay.Margin = new System.Windows.Forms.Padding(0);
			this.BtnPrepay.Name = "BtnPrepay";
			this.BtnPrepay.SendValue = null;
			this.BtnPrepay.Size = new System.Drawing.Size(78, 61);
			this.BtnPrepay.TabIndex = 4;
			this.BtnPrepay.TargetControl = null;
			// 
			// BtnToggle
			// 
			this.BtnToggle.AutoSize = true;
			this.BtnToggle.BackColor = System.Drawing.Color.Linen;
			this.BtnToggle.BorderColor = System.Drawing.Color.Black;
			this.BtnToggle.DisplayValue = "=>";
			this.BtnToggle.DownColour = System.Drawing.Color.LightGray;
			//this.BtnToggle.DrawRoundButtons = false;
			this.BtnToggle.Font = new System.Drawing.Font("Verdana", 8.25F);
			this.BtnToggle.Location = new System.Drawing.Point(78, 122);
			this.BtnToggle.Margin = new System.Windows.Forms.Padding(0);
			this.BtnToggle.Name = "BtnToggle";
			this.BtnToggle.SendValue = null;
			this.BtnToggle.Size = new System.Drawing.Size(78, 61);
			this.BtnToggle.TabIndex = 5;
			this.BtnToggle.TargetControl = null;
			// 
			// tmrWakeUp
			// 
			this.tmrWakeUp.Enabled = true;
			this.tmrWakeUp.Interval = 200;
			this.tmrWakeUp.Tick += new System.EventHandler(this.tmrWakeUp_Tick);
			// 
			// _blinkingTimer
			// 
			this._blinkingTimer.Enabled = true;
			this._blinkingTimer.Interval = 600;
			this._blinkingTimer.Tick += new System.EventHandler(this._blinkingTimer_Tick);
			// 
			// PumpInfoPosFooter
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.CausesValidation = false;
			this.ClientSize = new System.Drawing.Size(1135, 185);
			this.ControlBox = false;
			this.Controls.Add(this.pnlButtons);
			this.Controls.Add(this.PanelDispensers);
			this.Font = new System.Drawing.Font("Verdana", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "PumpInfoPosFooter";
			this.ShowIcon = false;
			this.ShowInTaskbar = false;
			this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
			this.TopMost = true;
			this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.PumpInfoPosFooter_FormClosing);
			this.pnlButtons.ResumeLayout(false);
			this.pnlButtons.PerformLayout();
			this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.FlowLayoutPanel pnlButtons;
        private Triquestra.Common.GUI.OnScreenButton BtnStopAll;
        private Triquestra.Common.GUI.OnScreenButton BtnPlayAll;
        private Triquestra.Common.GUI.OnScreenButton BtnToggle;
        private System.Windows.Forms.Timer tmrWakeUp;
        private System.Windows.Forms.Timer _blinkingTimer;
        internal Common.GUI.OnScreenStateButton BtnStop;
        internal Common.GUI.OnScreenStateButton BtnPlay;
        internal System.Windows.Forms.FlowLayoutPanel PanelDispensers;
        internal Common.GUI.OnScreenStateButton BtnPrepay;
    }
}