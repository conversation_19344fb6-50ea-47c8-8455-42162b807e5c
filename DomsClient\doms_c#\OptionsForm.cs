using System;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class OptionsForm : Form
    {
        public OptionsForm()
        {
            InitializeComponent();
        }

        private void OptionsForm_Load(object sender, EventArgs e)
        {
            // Load settings into form controls
            LoadSettings();
        }

        private void LoadSettings()
        {
            var settings = Properties.Settings.Default;
            
            // Load settings into controls (placeholder implementation)
            // txtIPAddress.Text = settings.IPAddress;
            // numPosId.Value = settings.PosId;
            // etc.
        }

        private void SaveSettings()
        {
            var settings = Properties.Settings.Default;
            
            // Save settings from controls (placeholder implementation)
            // settings.IPAddress = txtIPAddress.Text;
            // settings.PosId = (int)numPosId.Value;
            // etc.
            
            settings.Save();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            SaveSettings();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
