﻿using System;
using System.IO;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Newtonsoft.Json;
using DomsIntegration.Core.Exceptions;
using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Utilities;

namespace DomsIntegration.Core.Communication
{
    /// <summary>
    /// Main client for communicating with Doms PSS Forecourt Controller using JSON Presentation Layer (JPL)
    /// </summary>
    public class JplClient : IDisposable
    {
        private const byte STX = 0x02;
        private const byte ETX = 0x03;
        private const int DefaultPort = 8888;
        private const int DefaultSecurePort = 8889;
        private const int HeartbeatInterval = 20000; // 20 seconds
        private const int ReceiveTimeout = 30000; // 30 seconds

        private readonly ILogger _logger;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplMessage>> _pendingRequests;
        private TcpClient _tcpClient;
        private Stream _stream;
        private Timer _heartbeatTimer;
        private Timer _receiveTimeoutTimer;
        private DateTime _lastMessageReceived;
        private bool _disposed;
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// Event fired when an unsolicited message is received
        /// </summary>
        public event EventHandler<JplMessage> UnsolicitedMessageReceived;

        /// <summary>
        /// Event fired when connection state changes
        /// </summary>
        public event EventHandler<bool> ConnectionStateChanged;

        /// <summary>
        /// Gets whether the client is currently connected
        /// </summary>
        public bool IsConnected => _tcpClient?.Connected == true;

        /// <summary>
        /// Initializes a new instance of the JplClient
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public JplClient(ILogger logger = null)
        {
            _logger = logger ?? new ConsoleLogger();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplMessage>>();
            _lastMessageReceived = DateTime.UtcNow;
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// Connects to the PSS controller
        /// </summary>
        /// <param name="hostname">Controller hostname or IP address</param>
        /// <param name="port">Port number (default: 8888 for unencrypted, 8889 for TLS)</param>
        /// <param name="useTls">Whether to use TLS encryption</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the connection operation</returns>
        public async Task ConnectAsync(string hostname, int? port = null, bool useTls = false, CancellationToken cancellationToken = default)
        {
            try
            {
                if (IsConnected)
                    throw new InvalidOperationException("Client is already connected");

                int actualPort = port ?? (useTls ? DefaultSecurePort : DefaultPort);

                _logger.LogInfo($"Connecting to {hostname}:{actualPort} (TLS: {useTls})");

                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(hostname, actualPort);

                if (useTls)
                {
                    var sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                    await sslStream.AuthenticateAsClientAsync(hostname);
                    _stream = sslStream;
                }
                else
                {
                    _stream = _tcpClient.GetStream();
                }

                _logger.LogInfo("Connected successfully");

                // Start heartbeat and receive timeout timers
                StartTimers();

                // Start listening for messages
                _ = Task.Run(() => MessageReceiveLoop(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                ConnectionStateChanged?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Connection failed: {ex.Message}");
                Cleanup();
                throw new DomsCommunicationException($"Failed to connect to {hostname}:{port}", ex);
            }
        }

        /// <summary>
        /// Sends a message and waits for response
        /// </summary>
        /// <typeparam name="TResponse">Expected response type</typeparam>
        /// <param name="request">Request message</param>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <param name="correlationId">Optional correlation ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Response message</returns>
        public async Task<TResponse> SendMessageAsync<TResponse>(JplMessage request, int timeoutMs = 30000, string correlationId = null, CancellationToken cancellationToken = default)
            where TResponse : class
        {
            if (!IsConnected)
                throw new InvalidOperationException("Client is not connected");

            try
            {
                // Generate correlation ID if not provided
                if (string.IsNullOrEmpty(correlationId))
                {
                    correlationId = Guid.NewGuid().ToString();
                }

                request.CorrelationId = correlationId;

                _logger.LogDebug($"Sending message: {request.Name}");

                // Create task completion source for response
                var tcs = new TaskCompletionSource<JplMessage>();
                _pendingRequests[correlationId] = tcs;

                try
                {
                    // Serialize and frame the message
                    string jsonData = JsonConvert.SerializeObject(request, Formatting.None);
                    byte[] messageBytes = FrameMessage(jsonData);

                    // Send the message
                    await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                    await _stream.FlushAsync(cancellationToken);

                    // Wait for response with timeout
                    using (var timeoutCts = new CancellationTokenSource(timeoutMs))
                    using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
                    {
                        var response = await tcs.Task.ConfigureAwait(false);
                        return JsonConvert.DeserializeObject<TResponse>(JsonConvert.SerializeObject(response));
                    }
                }
                finally
                {
                    _pendingRequests.TryRemove(correlationId, out _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to send message: {ex.Message}");
                throw new DomsCommunicationException("Failed to send message", ex);
            }
        }

        /// <summary>
        /// Sends a heartbeat message
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the heartbeat operation</returns>
        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
        {
            if (!IsConnected) return;

            try
            {
                var heartbeat = new JplMessage
                {
                    Name = "heartbeat",
                    SubCode = "00H",
                    Data = new { }
                };

                string jsonData = JsonConvert.SerializeObject(heartbeat, Formatting.None);
                byte[] messageBytes = FrameMessage(jsonData);

                await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);

                _logger.LogDebug("Heartbeat sent");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to send heartbeat: {ex.Message}");
            }
        }

        /// <summary>
        /// Disconnects from the PSS controller
        /// </summary>
        public void Disconnect()
        {
            _logger.LogInfo("Disconnecting...");
            _cancellationTokenSource?.Cancel();
            Cleanup();
            ConnectionStateChanged?.Invoke(this, false);
        }

        private static byte[] FrameMessage(string jsonData)
        {
            byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
            byte[] framedMessage = new byte[jsonBytes.Length + 2];
            framedMessage[0] = STX;
            Array.Copy(jsonBytes, 0, framedMessage, 1, jsonBytes.Length);
            framedMessage[framedMessage.Length - 1] = ETX;
            return framedMessage;
        }

        private async Task MessageReceiveLoop(CancellationToken cancellationToken)
        {
            byte[] buffer = new byte[4096];
            var messageBuffer = new MemoryStream();

            try
            {
                while (IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    int bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);

                    if (bytesRead == 0)
                    {
                        _logger.LogWarning("Connection closed by remote host");
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    // Process received bytes
                    for (int i = 0; i < bytesRead; i++)
                    {
                        byte b = buffer[i];

                        if (b == STX)
                        {
                            messageBuffer.SetLength(0);
                        }
                        else if (b == ETX)
                        {
                            // Complete message received
                            string jsonData = Encoding.UTF8.GetString(messageBuffer.ToArray());
                            ProcessReceivedMessage(jsonData);
                            messageBuffer.SetLength(0);
                        }
                        else
                        {
                            messageBuffer.WriteByte(b);
                        }
                    }
                }
            }
            catch (Exception ex) when (!(ex is OperationCanceledException))
            {
                _logger.LogError($"Error in message receive loop: {ex.Message}");
            }
            finally
            {
                messageBuffer.Dispose();
            }
        }

        private void ProcessReceivedMessage(string jsonData)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<JplMessage>(jsonData);

                _logger.LogDebug($"Received message: {message.Name}");

                // Check if this is a response to a pending request
                if (!string.IsNullOrEmpty(message.CorrelationId) && _pendingRequests.TryRemove(message.CorrelationId, out var tcs))
                {
                    tcs.SetResult(message);
                }
                // Check if this is an unsolicited message
                else if (message.Solicited == false)
                {
                    UnsolicitedMessageReceived?.Invoke(this, message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to process received message: {ex.Message}");
            }
        }

        private void StartTimers()
        {
            // Heartbeat timer
            _heartbeatTimer = new Timer(async _ => await SendHeartbeatAsync(), null, HeartbeatInterval, HeartbeatInterval);

            // Receive timeout timer
            _receiveTimeoutTimer = new Timer(CheckReceiveTimeout, null, ReceiveTimeout, ReceiveTimeout);
        }

        private void CheckReceiveTimeout(object state)
        {
            if ((DateTime.UtcNow - _lastMessageReceived).TotalMilliseconds > ReceiveTimeout)
            {
                _logger.LogWarning("Receive timeout - closing connection");
                Disconnect();
            }
        }

        private static bool ValidateServerCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            // In production, implement proper certificate validation
            return true;
        }

        private void Cleanup()
        {
            _heartbeatTimer?.Dispose();
            _receiveTimeoutTimer?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Close();
            _tcpClient?.Dispose();

            _heartbeatTimer = null;
            _receiveTimeoutTimer = null;
            _stream = null;
            _tcpClient = null;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                Cleanup();
                _disposed = true;
            }
        }
    }
}
