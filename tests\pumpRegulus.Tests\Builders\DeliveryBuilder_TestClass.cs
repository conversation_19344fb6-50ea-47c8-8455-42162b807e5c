﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Triquestra.Common.PumpEsm.Builders;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.Database;

namespace pumpRegulus.Tests.Builders
{
    [TestClass]
    public class DeliveryBuilder_TestClass
    {
        [TestMethod]
        public void DeliveryBuilder_TestClass_Build_TankIdIsNull()
        {
            int? tankId = null;

            Prepay prepay = new Prepay()
            {
                Amount = 4,
                DispenserId = 1,
                Blend = new ForecourtBlend(2, "fuel") { BlendId = 2 },

            };

            Items item = new Items()
            {
                Price1 = 3m
            };

            var actual = DeliveryBuilder.Build(tankId, prepay, item);

            Assert.AreEqual(AuthModes.PREPAY, actual.Mode, "Invalid mode assigned");

            Assert.AreEqual(0, actual.DeliveryId, "Invalid delivery ID");

            Assert.AreEqual(prepay.DispenserId, actual.DispenserId, "Invalid dispenserID assigned");

            Assert.AreEqual(prepay.Blend.NozzleId, actual.NozzleId, "Invalid NozzleID");

            Assert.AreEqual(prepay.Blend.BlendId, actual.BlendId, "Invalid BlendID");

            Assert.AreEqual(tankId ?? 0, actual.TankId, "Invalid tankID");

            Assert.AreEqual(item.Price1, actual.Price, "Invalid price");

            Assert.AreEqual(prepay.Amount, actual.Amount, "Invalid amount");

            Assert.AreEqual(Math.Round(prepay.Amount / item.Price1, 4), actual.Volume, "Invalid volume");

            Assert.AreEqual(DeliveryStates.RESERVED, actual.State, "Invalid State");

            Assert.AreEqual(0m, actual.Limit, "Invalid limit");

            Assert.AreEqual(LimitTypes.VALUE, actual.LimitType, "Invalid limit type");

        }
    }
}
