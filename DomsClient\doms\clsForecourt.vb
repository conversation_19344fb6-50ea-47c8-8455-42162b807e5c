﻿Imports System.Threading
Imports System.Collections
Imports Newtonsoft.Json

Public Class clsForecourt

  Public Enum DeviceTypes
    FUELLING_POINT
    TANK_GAUGE
  End Enum

  Delegate Sub OperationCompletedDelegate(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)

  Private Class clsWorkerQueueItem

    Enum Actions
      FC_LOGON
      HEARTBEAT

      READ_PRICES
      SET_PRICES

      AUTH_FP
      DEAUTH_FP
      LOCK_FP_TRANS
      UNLOCK_FP_TRANS
      CLEAR_FP_TRANS
      READ_FP_ERROR
      CLEAR_FP_ERROR
      READ_FP_TOTALS
      READ_FUELLING_DATA

      HANDLE_BACKOFFICE_RECORDS

      READ_TG_DATA
      READ_TANK_DELIVERY_DATA
      CLEAR_DELIVERY_REPORT

      SEND_USER_DEFINED_REQUEST
    End Enum

    Public ReadOnly Action As Actions                                        'What action should be carried out?
    Public ReadOnly Params As Object                                         'Parameters for the action.
    Public ReadOnly OperationCompletedCallBack As OperationCompletedDelegate 'Callback when the action has completed with success/failure

    Public Sub New(ByVal Action As Actions, ByVal Params As Object, Optional ByVal OperationCompletedCallBack As OperationCompletedDelegate = Nothing)
      Me.Action = Action
      Me.Params = Params
      Me.OperationCompletedCallBack = OperationCompletedCallBack
    End Sub

  End Class

  Private Const INTERVAL_BETWEEN_FUELLINGDATA_READINGS As Integer = 1000 'Interval between reading fuelling data when a fuelling point is in state "fuelling"

#Region "Member Variables"
  Private WithEvents _ForecourtComm As New clsForecourtComm
  Private _FcWorkerQueue As System.Collections.Queue      'The worker queue which holds instances of the clsWorkerQueueItem
  Private _FcLowPriorityWorkerQueue As System.Collections.Queue 'The low priority worker queue which holds instances of the clsWorkerQueueItem
  Private _NewItemInqueueEvent As AutoResetEvent          'Event that is set to signalled when new items have been posted to worker queue
  Private _DefinedFuellingPoints As New DevicesDataSet    'List of defined fuelling points
  Private _DefinedTankGauges As New DevicesDataSet        'List of defined tank gauges
  Private _IsLoggedOn As Boolean                          'Is set to True when logged on
  Private _Grades As System.Collections.Generic.Dictionary(Of String, String)
  Private _CachedFpStatusResponses As New System.Collections.Generic.Dictionary(Of Integer, FpStatusRespType)
  Private _PendingBackOfficeRecordClear As Boolean        'Flag indicating that a HANDLE_BACKOFFICE_RECORDS operation is pending
  Private _Timer As System.Threading.Timer                'Timer for handling hearbeats
#End Region

#Region "Events definitions"
  'This event is fired if a previously defined device is no longer defined. Client should remove device from user interface.
  Public Event DeviceRemoved(ByVal DeviceType As DeviceTypes, ByVal DeviceId As Integer)

  'This event is fired when a new device has been defined. Client should add device to user interface.
  Public Event DeviceConfigured(ByVal DeviceType As DeviceTypes, ByVal DeviceId As Integer)

  'Fired when the client should reset its list of transactions for a fuelling point.
  Public Event InitFpTransactionList(ByVal FpId As Integer)

  'Fired once for each transaction on each fuelling point when the transaction is created or when information about transaction changes (e.g. lock/unlock).
  Public Event FpTransaction(ByVal FpId As Integer, ByVal GradeName As String, ByVal Volume As Decimal, ByVal Money As Decimal, ByVal TransSeqNo As Integer, ByVal UnitPrice As Decimal, ByVal LockId As Integer)

  'Fired when the status of a device changes.
  Public Event DeviceStatusChanged(ByVal DeviceType As DeviceTypes, ByVal DeviceId As Integer, ByVal StatusText As String, ByVal IsOnline As Boolean, ByVal FuellingData As Object)

  'Fired when a device is in error state.
  Public Event DeviceError(ByVal DeviceType As Integer, ByVal DeviceId As Integer, ByVal ErrorText As String, ByVal IsOnline As Boolean)

  'Fired when the existing connection is closed or lost.
  Public Event ConnectionClosed(ByVal WasLost As Boolean)

  Public Event ConnectionEstablished()

  'Fired when an operation, which was not initiated from the user interface, completed
  Public Event OperationCompleted(ByVal Success As Boolean, Message As String)

  Public Event RequestSent(Request As String)
  Public Event ResponseReceived(Response As String)
#End Region

  Public Sub New()
    _Timer = New System.Threading.Timer(New System.Threading.TimerCallback(AddressOf _HandleHeartBeat), Nothing, 20000, 20000)

    Dim t As New Thread(AddressOf _FcWorkerThread)                                'Create the forecourt worker thread.

    _FcWorkerQueue = Queue.Synchronized(New System.Collections.Queue)            'Create the forecourt worker queue.
    _FcLowPriorityWorkerQueue = Queue.Synchronized(New System.Collections.Queue)

    _NewItemInqueueEvent = New AutoResetEvent(False)                             'Create the event which will signal when there's work.

    t.IsBackground = True                                                        'Worker thread is a background thread meaning it will be stopped by the run-time when process exits.
    t.Name = "FcThread"                                                          'Name of worker thread.
    t.Start()                                                                    'Start the worker thread.
  End Sub

#Region "Forecourt Event handlers"

  Private Sub _HandleFcInstallStatus(ByVal Msg As String)
    Dim FcInstallStatusResponse As FcInstallStatusRespType = JsonConvert.DeserializeObject(Of FcInstallStatusRespType)(Msg)
    Dim DeviceList As New List(Of Integer)

    For Each DeviceGroup As FcInstallStatusRespType.InstalledFcDeviceGroupsType In FcInstallStatusResponse.data.InstalledFcDeviceGroups

      If DeviceGroup.ExtendedInstallMsgCode = "0010H" OrElse DeviceGroup.ExtendedInstallMsgCode = "0040H" Then
        DeviceList.Clear()

        For Each FcDeviceId As String In DeviceGroup.FcDeviceId
          DeviceList.Add(Integer.Parse(FcDeviceId))
        Next

        If DeviceGroup.ExtendedInstallMsgCode = "0010H" Then
          _CheckDeviceConfiguration(DeviceTypes.FUELLING_POINT, _DefinedFuellingPoints, DeviceList)
        Else
          _CheckDeviceConfiguration(DeviceTypes.TANK_GAUGE, _DefinedTankGauges, DeviceList)
        End If
      End If
    Next
  End Sub

  'This method takes care of detecting when devices (fuelling points or tank gauges) have been added or removed.
  Private Sub _CheckDeviceConfiguration(ByVal DeviceType As DeviceTypes, ByVal DeviceDataset As DevicesDataSet, ByVal DeviceList As List(Of Integer))
    Dim DeviceRow As DevicesDataSet.DevicesRow

    'Run through our list of devices and reset the configured flag.
    For Each DeviceRow In DeviceDataset.Devices.Rows
      DeviceRow.Configured = False
    Next

    'Run through all devices there are currently configured in the PSS.
    For Each DeviceID As Integer In DeviceList
      DeviceRow = DeviceDataset.Devices.FindByDeviceID(DeviceID)

      If DeviceRow Is Nothing Then                           'If we couldn't find it in our list, then it's a new device.
        DeviceDataset.Devices.AddDevicesRow(DeviceID, True) 'Add the new device to the list.
        RaiseEvent DeviceConfigured(DeviceType, DeviceID)   'Tell the client that there's a new device.
      Else
        DeviceRow.Configured = True 'Set a flag that the device is known both in our list and in the PSS meaning no changes.
      End If
    Next

    'Run through our list of devices again.
    For Each DeviceRow In DeviceDataset.Devices.Rows
      If Not DeviceRow.Configured Then                           'The device is unknown in the PSS. It must have been removed then.
        RaiseEvent DeviceRemoved(DeviceType, DeviceRow.DeviceID) 'Tell the client that the device has been removed.
        DeviceDataset.Devices.Rows.Remove(DeviceRow)             'Remove the device from our list.
      End If
    Next

    DeviceDataset.Devices.AcceptChanges()                        'Commit changes to our list.
  End Sub

  Private Sub _HandleFpStatusChanged(Msg As String)
    Dim FpStatusResponse As FpStatusRespType = JsonConvert.DeserializeObject(Of FpStatusRespType)(Msg)
    Dim IsOnline As Boolean = FpStatusResponse.data.FpSubStates.bits.IsOnline 'Detect whether or not the device is online.
    Dim FpId As Integer = Integer.Parse(FpStatusResponse.data.FpId)

    SyncLock _CachedFpStatusResponses
      _CachedFpStatusResponses.Item(FpId) = FpStatusResponse
    End SyncLock

    If FpStatusResponse.data.FpSubStates.bits.IsInErrorState Then
      'If the device is in an error state then post a READ_ERROR message to the worker queue.
      _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_FP_ERROR, New With {FpId, IsOnline}))
    Else
      If String.IsNullOrEmpty(FpStatusResponse.data.FpMainState.enum.Fuelling) Then 'During fuelling, the CheckFuellingData method will take care of updating the FpStatus
        RaiseEvent DeviceStatusChanged(DeviceTypes.FUELLING_POINT, FpId, FpStatusResponse.MainStateText, IsOnline, Nothing)
      End If
    End If
  End Sub

  Private Sub _HandleTgStatusChanged(Msg As String)
    Dim TgStatusResponse As TgStatusRespType = JsonConvert.DeserializeObject(Of TgStatusRespType)(Msg)
    RaiseEvent DeviceStatusChanged(DeviceTypes.TANK_GAUGE, TgStatusResponse.data.TgId, TgStatusResponse.MainStateText, TgStatusResponse.data.TgSubStates.bits.TankGaugeOnline, Nothing)
  End Sub

  Private Sub _HandleFpTransBufChanged(Msg As String)
    Dim FpSupTransBufStatusResponse As FpSupTransBufStatusRespType = JsonConvert.DeserializeObject(Of FpSupTransBufStatusRespType)(Msg)
    Dim FpId As Integer = Integer.Parse(FpSupTransBufStatusResponse.data.FpId)
    Dim GradeName As String = "?????"
    Dim Volume, Money, UnitPrice As Decimal
    Dim FpStatusResponse As FpStatusRespType = _CachedFpStatusResponses.Item(FpId)

    RaiseEvent DeviceStatusChanged(DeviceTypes.FUELLING_POINT, FpId, FpStatusResponse.MainStateText, FpStatusResponse.data.FpSubStates.bits.IsOnline, Nothing)
    RaiseEvent InitFpTransactionList(FpId) 'Tell client to reset its list of transactions for this fuelling point.

    For Each TransBufEntry As FpSupTransBufStatusRespType.FpSupTransBufStatusRespDataType.TransType In FpSupTransBufStatusResponse.data.TransInSupBuffer
      Money = BCDBufToDecimal(TransBufEntry.MoneyDue_e, My.Settings.MoneyDecimalPointPosition)

      Volume = BCDBufToDecimal(TransBufEntry.Vol_e, My.Settings.VolumeDecimalPointPosition)
      If Volume > 0D Then UnitPrice = Money / Volume

      'Find the grade name if possible.
      If _Grades IsNot Nothing Then GradeName = _Grades.Item(TransBufEntry.FcGradeId)

      'Tell client information about the transaction.
      Dim TransLockId As Integer = Integer.Parse(TransBufEntry.TransLockId)
      Dim TransSeqNo As Integer = Integer.Parse(TransBufEntry.TransSeqNo)

      RaiseEvent FpTransaction(FpId, GradeName, Volume, Money, TransSeqNo, UnitPrice, TransLockId)
      If TransLockId = 0 AndAlso My.Settings.AutoLockTransactions Then LockTransaction(FpId, TransSeqNo)
    Next
  End Sub

  Private Sub _HandleSiteDeliveryStatusChanged(Msg As String)
    Dim SiteDeliveryStatusResponse As SiteDeliveryStatusRespType = JsonConvert.DeserializeObject(Of SiteDeliveryStatusRespType)(Msg)

    If My.Settings.ClearDeliveryReports AndAlso SiteDeliveryStatusResponse.data.DeliveryStatusFlags.bits.SiteDeliveryDataIsReady Then
      _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.CLEAR_DELIVERY_REPORT, New With {SiteDeliveryStatusResponse.data.DeliveryReportSeqNo, SiteDeliveryStatusResponse.data.TgId}, Nothing))
    End If
  End Sub

  Private Sub HandleFcStatusChanged(Msg)
    Dim FcStatusReponse As FcStatusRespType = JsonConvert.DeserializeObject(Of FcStatusRespType)(Msg)

    If My.Settings.ClearBackOfficeRecords AndAlso Not _PendingBackOfficeRecordClear AndAlso FcStatusReponse.data.FcStatus2Flags.bits.BackOfficeRecordExists Then
      _PendingBackOfficeRecordClear = True
      _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.HANDLE_BACKOFFICE_RECORDS, Nothing, Nothing))
    End If
  End Sub

  Private Sub _ForecourtComm_UnsolMsgReceived(MsgName As String, Msg As String) Handles _ForecourtComm.UnsolicitedMsgReceived
    Select Case MsgName
      Case "MultiMessage_resp"
        Dim MultiMsgResponse As MultiMessageRespType = JsonConvert.DeserializeObject(Of MultiMessageRespType)(Msg)

        For Each MultiMsgResponseEntry As DomsPosResponseType In MultiMsgResponse.data.messages
          _ForecourtComm_UnsolMsgReceived(MultiMsgResponseEntry.name, JsonConvert.SerializeObject(MultiMsgResponseEntry))
        Next

      Case "FcInstallStatus_resp"
        _HandleFcInstallStatus(Msg)
      Case "FpStatus_resp"
        _HandleFpStatusChanged(Msg)
      Case "TgStatus_resp"
        _HandleTgStatusChanged(Msg)
      Case "FpSupTransBufStatus_resp"
        _HandleFpTransBufChanged(Msg)
      Case "SiteDeliveryStatus_resp"
        _HandleSiteDeliveryStatusChanged(Msg)
      Case "FcStatus_resp"
        HandleFcStatusChanged(Msg)
    End Select
  End Sub

  Private Sub _ForecourtComm_ConnectionLost()
    _InitVariables()
    RaiseEvent ConnectionClosed(True)
  End Sub

  Private Sub _ForecourtComm_RequestSent(Request As String) Handles _ForecourtComm.RequestSent
    RaiseEvent RequestSent(Request)
  End Sub

  Private Sub _ForecourtComm_ResponseReceived(Response As String) Handles _ForecourtComm.ResponseReceived
    RaiseEvent ResponseReceived(Response)
  End Sub

#End Region

  'This is the forecourt worker thread which carries out all forecourt operations.
  Private Sub _FcWorkerThread()
    While True                                                    'Loop forever (until application terminates)
      If _FcWorkerQueue.Count = 0 And _FcLowPriorityWorkerQueue.Count = 0 Then
        _NewItemInqueueEvent.WaitOne(500, False)                  'Wait for something to do
      End If

      While _FcWorkerQueue.Count > 0                              'Keep working until queue is empty
        _DoWork(_FcWorkerQueue.Dequeue)                           'Get next worker queue item.
      End While

      While _FcLowPriorityWorkerQueue.Count > 0                   'Keep working until queue is empty
        _DoWork(_FcLowPriorityWorkerQueue.Dequeue)                'Get next worker queue item.
        If _FcWorkerQueue.Count > 0 Then Exit While               'Exit loop if there is something more important to do
      End While
    End While
  End Sub

  Private Sub _CheckResponse(Response As clsForecourtComm.clsResponse)
    Dim RejectMessageResponse As RejectMessageRespType

    If Response.IsReject Then
      RejectMessageResponse = JsonConvert.DeserializeObject(Of RejectMessageRespType)(Response.Json)
      Throw New Exception(RejectMessageResponse.data.RejectInfo)
    End If
  End Sub

  Private Sub _DoWork(ByVal WorkerQueueItem As clsWorkerQueueItem)

    With WorkerQueueItem
      Try
        If Not _IsLoggedOn AndAlso .Action <> clsWorkerQueueItem.Actions.FC_LOGON Then Return 'Skip all items except FC_LOGON if we're not logged in.

        Select Case .Action
          Case clsWorkerQueueItem.Actions.FC_LOGON             'Forecourt logon.
            _FcLogon(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.READ_PRICES          'Read prices.
            _ReadPrices(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.SET_PRICES           'Set prices.
            _SetPrices(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.AUTH_FP              'Authorize fuelling point.
            _CheckResponse(_ForecourtComm.SendMessage(New FpAuthReqType(.Params, My.Settings.PosId)))
            _CallCompletedHandler(WorkerQueueItem)              'Tell that we have completed the operation with success.

          Case clsWorkerQueueItem.Actions.DEAUTH_FP            'Deauthorize fuelling point.
            _CheckResponse(_ForecourtComm.SendMessage(New FpCancelAuthReqType(.Params, My.Settings.PosId)))
            _CallCompletedHandler(WorkerQueueItem)              'Tell that we have completed the operation with success.

          Case clsWorkerQueueItem.Actions.READ_TG_DATA          'Read tank gauge data.
            _ReadTgData(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.READ_TANK_DELIVERY_DATA      'Read tank deliveries.
            _ReadTankDeliveryData(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.CLEAR_DELIVERY_REPORT      'Clear delivery report
            _ClearDeliveryReport(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.READ_FP_ERROR           'Read error message from device.
            _ReadFpError(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.CLEAR_FP_ERROR       'Clear fuelling point error.
            _ClearFpError(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.READ_FUELLING_DATA   'Read fuelling data.
            _ReadFuellingData(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.READ_FP_TOTALS          'Read totals from fuelling point.
            _ReadTotals(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.LOCK_FP_TRANS           'Lock transaction.
            _CheckResponse(_ForecourtComm.SendMessage(New FpSupTransReqType(.Params.FpId, .Params.TransSeqNo, My.Settings.PosId)))
            _CallCompletedHandler(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.UNLOCK_FP_TRANS         'Unlock transaction.
            _CheckResponse(_ForecourtComm.SendMessage(New FpUnLockSupTransReqType(.Params.FpId, .Params.TransSeqNo, My.Settings.PosId)))
            _CallCompletedHandler(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.CLEAR_FP_TRANS          'Clear transaction.
            _ClearTransAction(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.HANDLE_BACKOFFICE_RECORDS
            _HandleBackOfficeRecords(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.SEND_USER_DEFINED_REQUEST
            _SendUserDefinedRequest(WorkerQueueItem)

          Case clsWorkerQueueItem.Actions.HEARTBEAT
            _HandleHeartBeat(WorkerQueueItem)

        End Select

      Catch ex As Exception
        If .OperationCompletedCallBack IsNot Nothing Then .OperationCompletedCallBack(False, .Params, ex)
      End Try
    End With
  End Sub

  'Helper sub that calls the callback (if defined) in the WorkerQueueItem.
  'Success parameter is set to True and the parameters in the WorkerQueueItem are returned in the callback.
  Private Sub _CallCompletedHandler(ByVal WorkerQueueItem As clsWorkerQueueItem)
    If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then WorkerQueueItem.OperationCompletedCallBack(True, WorkerQueueItem.Params, Nothing)
  End Sub

  'Helper function to read a transaction with a specific transaction sequence number from a specific fuelling point.
  Private Function _GetTransaction(ByVal FpId As Byte, ByVal TransSeqNo As Integer) As Object
    Return Nothing
  End Function

  'Reads totals from a fuelling point.
  Private Sub _ReadTotals(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim FpId As Integer = WorkerQueueItem.Params                                    'Parameter was the FpId.

    Try
      Dim Reponse1 As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FpGradeTotalsReqType(FpId))
      Dim Reponse2 As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New PumpGradeTotalsReqType(FpId))

      _CheckResponse(Reponse1)

      'Call the callback supplying FpId, FpTotals, and PumpTotals.
      If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then
        Dim FpGradeTotalsResponse As FpGradeTotalsRespType = Nothing
        Dim PumpGradeTotalsResponse As PumpGradeTotalsRespType = Nothing

        FpGradeTotalsResponse = JsonConvert.DeserializeObject(Of FpGradeTotalsRespType)(Reponse1.Json)
        If Not Reponse2.IsReject Then PumpGradeTotalsResponse = JsonConvert.DeserializeObject(Of PumpGradeTotalsRespType)(Reponse2.Json)

        WorkerQueueItem.OperationCompletedCallBack(True, New With {FpId, FpGradeTotalsResponse, PumpGradeTotalsResponse}, Nothing)
      End If
    Catch ex As Exception
      WorkerQueueItem.OperationCompletedCallBack(False, New With {FpId}, ex)
    End Try
  End Sub

  Private Sub _HandleHeartBeat(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Try
      If _IsLoggedOn Then _ForecourtComm.SendMessage(New HearBeatRespType, False)
    Catch ex As Exception
    End Try
  End Sub

  'Reads fuelling data from a fuelling point.
  Private Sub _ReadFuellingData(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim FpId As Integer = WorkerQueueItem.Params.FpId                                       'Parameter was the FpId.
    Dim FpStatusResponse As FpStatusRespType = _CachedFpStatusResponses.Item(FpId)

    If Not String.IsNullOrEmpty(FpStatusResponse.data.FpMainState.enum.Fuelling) Then
      Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FpFuellingDataReqType(FpId))

      If Not Response.IsReject Then
        Dim FuellingDataResponse As FpFuellingDataRespType = JsonConvert.DeserializeObject(Of FpFuellingDataRespType)(Response.Json)

        'Tell client what the status is including the fuelling data.
        RaiseEvent DeviceStatusChanged(DeviceTypes.FUELLING_POINT, FpId, FpStatusResponse.MainStateText, FpStatusResponse.data.FpSubStates.bits.IsOnline, New With {.Volume_e = BCDBufToDecimal(FuellingDataResponse.data.Vol_e, My.Settings.VolumeDecimalPointPosition), .Money_e = BCDBufToDecimal(FuellingDataResponse.data.Money_e, My.Settings.MoneyDecimalPointPosition)})
      End If
    End If
  End Sub

  Private Sub _SendUserDefinedRequest(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendRawMessage(WorkerQueueItem.Params)
    WorkerQueueItem.OperationCompletedCallBack(True, New With {Response}, Nothing)
  End Sub

  Private Sub _ClearTransAction(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim TransParIdList As New List(Of String)({FpSupTransReqType.TransParIdType.Money_e, FpSupTransReqType.TransParIdType.Vol_e})
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FpSupTransReqType(WorkerQueueItem.Params.FpId, WorkerQueueItem.Params.TransSeqNo, "0", TransParIdList))
    Dim FpSupTransResponse As FpSupTransRespType = JsonConvert.DeserializeObject(Of FpSupTransRespType)(Response.Json)

    _CheckResponse(_ForecourtComm.SendMessage(New FpClrSupTransBufReqType(WorkerQueueItem.Params.FpId, WorkerQueueItem.Params.TransSeqNo, FpSupTransResponse.data.TransPars.Vol_e, FpSupTransResponse.data.TransPars.Money_e, My.Settings.PosId)))
  End Sub

  Private Sub _HandleBackOfficeRecords(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim Response As clsForecourtComm.clsResponse
    Dim BackOfficeRecordResponse As BackOfficeRecordRespType
    Dim ClearBackOfficeRecordResponse As ClearBackOfficeRecordRespType
    Dim MoreBackOfficeRecordExists As Boolean = False

    Try
      Do
        Response = _ForecourtComm.SendMessage(New BackOfficeRecordReqType())

        If Not Response.IsReject Then
          BackOfficeRecordResponse = JsonConvert.DeserializeObject(Of BackOfficeRecordRespType)(Response.Json)
          Response = _ForecourtComm.SendMessage(New ClearBackOfficeRecordReqType(BackOfficeRecordResponse.data.BorSeqNo))

          If Not Response.IsReject Then
            ClearBackOfficeRecordResponse = JsonConvert.DeserializeObject(Of ClearBackOfficeRecordRespType)(Response.Json)
            MoreBackOfficeRecordExists = ClearBackOfficeRecordResponse.data.BorBufferStatus.bits.BufferNotEmpty
          End If
        End If
      Loop While MoreBackOfficeRecordExists
    Catch ex As Exception
      'We might fail if we cannot deserialize BorData into a string. This might happen with some exotic PSS versions
    Finally
      _PendingBackOfficeRecordClear = False
    End Try
  End Sub

  Private Sub _ClearFpError(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim FpId As Integer = WorkerQueueItem.Params
    Dim ErrorText As String = String.Empty
    Dim ErrorCode As String = String.Empty

    If _CachedFpStatusResponses(FpId).data.FpSubStates.bits.IsInErrorState Then
      _ReadFpError(FpId, ErrorCode, ErrorText)
      If ErrorCode <> "00" Then _ForecourtComm.SendMessage(New FpClearErrorReqType(FpId, ErrorCode))
    End If

    _CallCompletedHandler(WorkerQueueItem)                                          'Tell client that we're done.
  End Sub

  'Log onto the PSS 5000
  Private Sub _FcLogon(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Try
      RemoveHandler _ForecourtComm.ConnectionLost, AddressOf _ForecourtComm_ConnectionLost 'Supress connection lost events because we are disconnecting on purpose

      With WorkerQueueItem.Params
        If _IsLoggedOn Then                                           'Were we already connected?
          _ForecourtComm.Disconnect()
          RaiseEvent ConnectionClosed(False)                        ' and tell the client that we disconnected.
        End If

        _InitVariables()                                             'Intialize variables.

        _ForecourtComm.Connect(.IPAddress)

        Dim UnsolMsgList As List(Of FcLogonReqType.FcLogonReqDataType.UnsolgMsgType) = Nothing

        If Not DirectCast(.LogonString, String).Contains("MFDR") Then
          UnsolMsgList = New List(Of FcLogonReqType.FcLogonReqDataType.UnsolgMsgType)({
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8006H", "00H"), 'FcStatus_resp
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8007H", "01H"), 'FcInstallStatus_resp
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8015H", "03H"), 'FpStatus_resp
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8016H", "03H"), 'FpSupTransBufStatus_resp
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8042H", "00H"), 'TgStatus_resp
                                                                                       New FcLogonReqType.FcLogonReqDataType.UnsolgMsgType("8046H", "00H")  'SiteDeliveryStatus_resp
                                                                                    })

        End If

        Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FcLogonReqType(.LogonString, "0045", "1234", {2}, UnsolMsgList))
        Dim RejectResponse As RejectMessageRespType

        If Not Response.IsReject Then
          _IsLoggedOn = True                                            'Set flag telling that we're logged on.
          _CallCompletedHandler(WorkerQueueItem)                         'Tell client that we're done.
          RaiseEvent ConnectionEstablished()

          If Not Response.IsReject Then _ReadGradeNames()
        Else
          _ForecourtComm.Disconnect()
          RejectResponse = JsonConvert.DeserializeObject(Of RejectMessageRespType)(Response.Json)

          If RejectResponse.data.RejectInfoText IsNot Nothing Then
            Throw New Exception(String.Format("Forecourt logon failed - {0}", RejectResponse.data.RejectInfoText))
          Else
            Throw New Exception("Forecourt logon failed")
          End If
        End If
      End With
    Finally
      AddHandler _ForecourtComm.ConnectionLost, AddressOf _ForecourtComm_ConnectionLost
    End Try
  End Sub

  Private Sub _ReadGradeNames()
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New PassthroughReqType({&HFF, &HB, &H0, &H0, &H4, &H1, &H0}))
    Dim PassThroughResponse As PassThroughRespType
    Dim BinaryReponse() As Byte
    Dim MsgCode As UInt16
    Dim Offset As Integer
    Dim GradeName, GradeId As String
    Dim TextEncoding As System.Text.Encoding

    _Grades = New System.Collections.Generic.Dictionary(Of String, String)

    If Not Response.IsReject Then
      PassThroughResponse = JsonConvert.DeserializeObject(Of PassThroughRespType)(Response.Json)
      BinaryReponse = PassThroughResponse.ToByteArray()
      MsgCode = System.BitConverter.ToUInt16(BinaryReponse, 1)

      If MsgCode = &H800B Then
        TextEncoding = System.Text.Encoding.GetEncoding("ibm850") 'Grade names are using code page 850 so we need to convert them

        For x As Integer = 0 To BinaryReponse(6) - 1 'Loop through the grades
          Offset = 7 + x * 13

          GradeId = String.Format("{0:x2}", BinaryReponse(Offset))  'Convert GradeId from BCD to decimal
          GradeName = TextEncoding.GetString(BinaryReponse, Offset + 1, 12).TrimEnd()

          _Grades.Add(GradeId, GradeName)
        Next
      End If
    End If
  End Sub

  'Read tank gauge data
  Private Sub _ReadTgData(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim TgId As Integer = WorkerQueueItem.Params 'Parameter was TgId.
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New TgDataReqType(TgId, New List(Of String)({
                                                                                                                      TgDataReqType.TankDataItemIdType.TankProductLevel,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankWaterLevel,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankWaterVol,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankAvailableRoom,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankGrossObservedVol,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankGrossStdVol,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankAverageTemp,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankMaxSafeFillCapacity,
                                                                                                                      TgDataReqType.TankDataItemIdType.TankDataLastUpdateDateAndTime
                                                                                                                      })))
    _CheckResponse(Response)
    Dim TgDataResponse As TgDataRespType = JsonConvert.DeserializeObject(Of TgDataRespType)(Response.Json)

    'Call callback supplying TgId and TgData
    If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then WorkerQueueItem.OperationCompletedCallBack(True, New With {TgDataResponse}, Nothing)
  End Sub

  'Reads delivery data from a tank gauge.
  Private Sub _ReadTankDeliveryData(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim TgId As Integer = WorkerQueueItem.Params 'Parameter was TgId.
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New TankDeliveryDatareqType(TgId, "0", New List(Of String)({
                                                                                                                                         TankDeliveryDatareqType.TankDeliveryDataItemIdType.TankDeliveredVol,
                                                                                                                                         TankDeliveryDatareqType.TankDeliveryDataItemIdType.TankDeliveryStartDateAndTime,
                                                                                                                                         TankDeliveryDatareqType.TankDeliveryDataItemIdType.TankDeliveryStopDateAndTime,
                                                                                                                                         TankDeliveryDatareqType.TankDeliveryDataItemIdType.TankDeliverySeqNo,
                                                                                                                                         TankDeliveryDatareqType.TankDeliveryDataItemIdType.DeliveryReportSeqNo
                                                                                                                                         })))
    _CheckResponse(Response)
    Dim DeliveryDataResponse As TankDeliveryDataRespType = JsonConvert.DeserializeObject(Of TankDeliveryDataRespType)(Response.Json)
    'Call the callback supplying DeliveryData.
    If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then WorkerQueueItem.OperationCompletedCallBack(True, New With {DeliveryDataResponse}, Nothing)
  End Sub

  Private Sub _ClearDeliveryReport(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim DeliveryReportSeqNo As String = WorkerQueueItem.Params.DeliveryReportSeqNo
    Dim TgIdList As List(Of String) = WorkerQueueItem.Params.TgId
    Dim Response As clsForecourtComm.clsResponse
    Dim TankDeliveryDataResponse As TankDeliveryDataRespType
    Dim TankDeliveryList As New List(Of ClearTankDeliveryDataReqType.TankDeliveryType)
    Dim RejectMessageResp As RejectMessageRespType

    For Each TgId As String In TgIdList
      Response = _ForecourtComm.SendMessage(New TankDeliveryDatareqType(TgId, My.Settings.PosId, New List(Of String)({TankDeliveryDatareqType.TankDeliveryDataItemIdType.TankDeliverySeqNo})))

      If Not Response.IsReject Then
        TankDeliveryDataResponse = JsonConvert.DeserializeObject(Of TankDeliveryDataRespType)(Response.Json)
        TankDeliveryList.Add(New ClearTankDeliveryDataReqType.TankDeliveryType(TgId, TankDeliveryDataResponse.data.TankDeliveryDataItems.TankDeliverySeqNo))
      Else
        RejectMessageResp = JsonConvert.DeserializeObject(Of RejectMessageRespType)(Response.Json)
        RaiseEvent OperationCompleted(False, String.Format("Could not read delivery information from TgId {0} ({1})", TgId, RejectMessageResp.data.RejectInfo))
        Return
      End If
    Next

    Response = _ForecourtComm.SendMessage(New ClearTankDeliveryDataReqType(My.Settings.PosId, DeliveryReportSeqNo, TankDeliveryList))

    If Response.IsReject Then
      RejectMessageResp = JsonConvert.DeserializeObject(Of RejectMessageRespType)(Response.Json)
      RaiseEvent OperationCompleted(False, String.Format("Delivery report {0} could not be cleared ({1})", DeliveryReportSeqNo, RejectMessageResp.data.RejectInfo))
    Else
      RaiseEvent OperationCompleted(True, String.Format("Delivery report {0} was cleared successfully", DeliveryReportSeqNo))
    End If
  End Sub

  Private Sub _ReadFpError(FpId As Integer, ByRef ErrorCode As String, ByRef ErrorText As String)
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FpErrorMsgReqType(FpId))
    Dim FpErrorMsgResponse As FpErrorRespType = JsonConvert.DeserializeObject(Of FpErrorRespType)(Response.Json)

    ErrorCode = FpErrorMsgResponse.data.FpErrorCode.value
    ErrorText = FpErrorMsgResponse.ErrorText
  End Sub

  Private Sub _ReadFpError(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim ErrorText As String = String.Empty
    Dim ErrorCode As String = String.Empty
    Dim FpId As Integer = WorkerQueueItem.Params.FpId

    _ReadFpError(FpId, ErrorCode, ErrorText)

    'Call calback supplying Devicetype, DeviceId, ErrorText, and whether or not device was online.
    RaiseEvent DeviceError(DeviceTypes.FUELLING_POINT, FpId, ErrorText, WorkerQueueItem.Params.IsOnline)
  End Sub

  'Read prices.
  Private Sub _ReadPrices(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New FcPriceSetReqType())
    Dim PricesResponse As FcPriceSetRespType = Nothing

    _CheckResponse(Response)

    If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then
      PricesResponse = JsonConvert.DeserializeObject(Of FcPriceSetRespType)(Response.Json)
      WorkerQueueItem.OperationCompletedCallBack(True, PricesResponse, Nothing)
    End If
  End Sub

  'Set prices.
  Private Sub _SetPrices(ByVal WorkerQueueItem As clsWorkerQueueItem)
    Dim Response As clsForecourtComm.clsResponse = _ForecourtComm.SendMessage(New ChangeFcPriceSetReqType(WorkerQueueItem.Params))

    _CheckResponse(Response)
    If WorkerQueueItem.OperationCompletedCallBack IsNot Nothing Then WorkerQueueItem.OperationCompletedCallBack(True, Nothing, Nothing)
  End Sub

  'Reset variables
  Private Sub _InitVariables()
    _IsLoggedOn = False                                  'Reset flag telling if we're logged on.
    _DefinedFuellingPoints.Clear()                       'Clear our list of defined fuelling points.
    _DefinedTankGauges.Clear()                           'Clear our list of defined tank gauges.
  End Sub

  'Adds a worker queue item to the worker queue.
  Private Sub _AddToFcWorkerQueue(ByVal WorkerQueueItem As clsWorkerQueueItem)
    _FcWorkerQueue.Enqueue(WorkerQueueItem)              'Add item to queue.
    _NewItemInqueueEvent.Set()                           'Tell worker thread that there's work to do.
  End Sub

  'Adds a worker queue item to the low priority worker queue.
  Private Sub _AddToLowPriorityFcWorkerQueue(ByVal WorkerQueueItem As clsWorkerQueueItem)
    _FcLowPriorityWorkerQueue.Enqueue(WorkerQueueItem)   'Add item to queue.
    _NewItemInqueueEvent.Set()                           'Tell worker thread that there's work to do.
  End Sub

  Public Shared Function BCDBufToDecimal(Buffer As String, NumberOfDecimals As Integer) As Decimal
    Return Decimal.Parse(Buffer.Insert(Buffer.Length - NumberOfDecimals, "."), Globalization.NumberStyles.AllowDecimalPoint, System.Globalization.CultureInfo.InvariantCulture)
  End Function

  Public Shared Function BCDBufToDateAndTime(Buffer As String) As String
    If Buffer.Length <> 14 Then Throw New Exception("Invalid length")

    Try
      Return String.Format("{0:d} {0:t}", New Date(Buffer.Substring(0, 4), Buffer.Substring(4, 2), Buffer.Substring(6, 2), Buffer.Substring(8, 2), Buffer.Substring(10, 2), Buffer.Substring(12, 2)))
    Catch ex As Exception
      Return "Invalid date/time"
    End Try
  End Function


#Region "Public properties/methods" 'This region contains the public member and properties

  Public Function LookUpGradeName(GradeId As String)
    If _Grades Is Nothing Then
      Return "???"
    Else
      Return _Grades.Item(GradeId)
    End If

  End Function

  'Perform asynchronous forecourt logon.
  Public Sub FcLogon(ByVal IPAddress As String, ByVal PosId As Byte, ByVal LogonString As String, ByVal VolDecimalPointPos As Integer, ByVal PriceDecimalPointPos As Integer, ByVal AmountDecimalPointPos As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.FC_LOGON, New With {IPAddress, PosId, LogonString, VolDecimalPointPos, PriceDecimalPointPos, AmountDecimalPointPos}, CallBack))
  End Sub

  'Read prices asynchronously
  Public Sub ReadPrices(ByVal CallBack As OperationCompletedDelegate)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_PRICES, Nothing, CallBack))
  End Sub

  'Set prices asynchronously
  Public Sub SetPrices(ByVal Prices As Object, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.SET_PRICES, Prices, CallBack))
  End Sub

  'Authorize a fuelling point asynchronously
  Public Sub AuthorizeFuellingPoint(ByVal FpId As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.AUTH_FP, FpId, CallBack))
  End Sub

  'Deauthorize a fuelling point asynchronously
  Public Sub DeauthorizeFuellingPoint(ByVal FpId As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.DEAUTH_FP, FpId, CallBack))
  End Sub

  'Lock a transaction asynchronously
  Public Sub LockTransaction(ByVal FpId As Integer, ByVal TransSeqNo As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.LOCK_FP_TRANS, New With {FpId, TransSeqNo}, CallBack))
  End Sub

  'Unlock a transaction asynchronously
  Public Sub UnlockTransaction(ByVal FpId As Integer, ByVal TransSeqNo As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.UNLOCK_FP_TRANS, New With {FpId, TransSeqNo}, CallBack))
  End Sub

  'Clear a transaction asynchronously
  Public Sub ClearTransAction(ByVal FpId As Integer, ByVal TransSeqNo As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.CLEAR_FP_TRANS, New With {FpId, TransSeqNo}, CallBack))
  End Sub

  'Read tank gauge data asynchronously
  Public Sub ReadTgData(ByVal TgId As Integer, ByVal CallBack As OperationCompletedDelegate)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_TG_DATA, TgId, CallBack))
  End Sub

  'Read deliveries asynchronously
  Public Sub ReadDeliveries(ByVal TgId As Integer, ByVal CallBack As OperationCompletedDelegate)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_TANK_DELIVERY_DATA, TgId, CallBack))
  End Sub

  'Clear fuelling point error asynchronously
  Public Sub ClearFpError(ByVal FpId As Integer, Optional ByVal CallBack As OperationCompletedDelegate = Nothing)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.CLEAR_FP_ERROR, FpId, CallBack))
  End Sub

  'Read totals from fuelling point asynchronously
  Public Sub ReadTotals(ByVal FpId As Integer, ByVal CallBack As OperationCompletedDelegate)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_FP_TOTALS, FpId, CallBack))
  End Sub

  Public Sub SendUserDefinedRequest(Request As String, ByVal CallBack As OperationCompletedDelegate)
    _AddToFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.SEND_USER_DEFINED_REQUEST, Request, CallBack))
  End Sub

  Public Sub CheckFuellingData()
    Dim FuellingData As Object = Nothing
    Dim FuellingDataInFpStatus As Boolean = My.Settings.FcLogonString.Contains("MFDR")

    If Not _IsLoggedOn Then Return

    SyncLock _CachedFpStatusResponses
      For Each FpStatusResponse As FpStatusRespType In _CachedFpStatusResponses.Values
        If Not String.IsNullOrEmpty(FpStatusResponse.data.FpMainState.enum.Fuelling) Then
          If FuellingDataInFpStatus Then 'Check if fuelling data is supplied as part of the fuelling point status
            If Not String.IsNullOrEmpty(FpStatusResponse.data.FpSupplStatusPars.FuellingDataMon_e) AndAlso Not String.IsNullOrEmpty(FpStatusResponse.data.FpSupplStatusPars.FuellingDataVol_e) Then
              FuellingData = New With {.Volume_e = BCDBufToDecimal(FpStatusResponse.data.FpSupplStatusPars.FuellingDataVol_e, My.Settings.VolumeDecimalPointPosition), .Money_e = BCDBufToDecimal(FpStatusResponse.data.FpSupplStatusPars.FuellingDataMon_e, My.Settings.MoneyDecimalPointPosition)}
            End If

            RaiseEvent DeviceStatusChanged(DeviceTypes.FUELLING_POINT, FpStatusResponse.data.FpId, FpStatusResponse.MainStateText, FpStatusResponse.data.FpSubStates.bits.IsOnline, FuellingData)
          Else
            _AddToLowPriorityFcWorkerQueue(New clsWorkerQueueItem(clsWorkerQueueItem.Actions.READ_FUELLING_DATA, New With {FpStatusResponse.data.FpId}))
          End If
        End If
      Next
    End SyncLock
  End Sub

  Public ReadOnly Property IsLoggedOn() As Boolean
    Get
      Return _IsLoggedOn
    End Get
  End Property

#End Region

End Class
