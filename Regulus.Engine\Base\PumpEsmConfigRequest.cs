﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace Triquestra.Common.PumpEsm.Base
{
    public class PumpEsmConfigRequest: PumpEsmRequest
    {
        public PumpEsmConfigRequest(XmlDocument req)
            : base(req)
        {
            ParseConfigReq();
        }

        public PumpConfigs Action
        {
            get
            {
                string s = Message;
                if (!string.IsNullOrEmpty(s))
                    return (PumpConfigs)s[0];
                else
                    return PumpConfigs.Unknown;
            }
        }

        internal void ParseConfigReq()
        {
            XmlNode msgNode = request.SelectSingleNode("action");
            if (null == msgNode)
                return;
#if DEBUG
            if (msgNode.InnerText != "config")
                throw new ArgumentException("The xml is not an action-type");
#endif
            
            switch (this.Action)
            {
                case PumpConfigs.GetFormTop:
                    int width;
                    var s = Message;
                    int.TryParse(s.Substring(2, s.Length - 2), out width);
                    POSScreenWidth = width;
                    break;
                case PumpConfigs.GetItemData:
                    int saleNo;
                    var sn = Message;
                    int.TryParse(sn.Substring(2, sn.Length - 2), out saleNo);
                    SaleNumber = saleNo;
                    break;

                default:
                    break;
            }
            
        }
        /// <summary>
        /// The size of the P1 main form
        /// </summary>
        public int? POSScreenWidth { get; private set; }
        /// <summary>
        /// This is a SaleNumber
        /// </summary>
        public int? SaleNumber { get; private set; }
    }
}
