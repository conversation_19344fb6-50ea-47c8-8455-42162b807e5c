﻿Imports Newtonsoft.Json

Public Class frmJplConsole

  Private WithEvents _Forecourt As clsForecourt

  Public WriteOnly Property Forecourt() As clsForecourt
    Set(ByVal value As clsForecourt)
      _Forecourt = value
    End Set
  End Property

  Private Sub btnSendMessage_Click(sender As Object, e As EventArgs) Handles btnSendMessage.Click
    Try
      Dim o As Object = JsonConvert.DeserializeObject(txtRequest.Text)
      _Forecourt.SendUserDefinedRequest(JsonConvert.SerializeObject(o), AddressOf SendMessageCompleted)
    Catch ex As Exception
      MsgBox(String.Format("The request is not valid JSON ({0})", ex.Message), MsgBoxStyle.Exclamation)
    End Try
  End Sub

  Private Sub SendMessageCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    Dim JsonMsg As String
    Dim Obj As Object


    If InvokeRequired Then
      Invoke(New frmMain.OperationCompleted(AddressOf SendMessageCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        If Data.Response IsNot Nothing Then
          JsonMsg = DirectCast(Data.Response, clsForecourtComm.clsResponse).Json
          Obj = JsonConvert.DeserializeObject(JsonMsg)
          txtResponse.Text = JsonConvert.SerializeObject(Obj, Formatting.Indented)
        End If
      Else
        MsgBox(String.Format("An error occurred ({0})", Ex.Message), MsgBoxStyle.Exclamation)
      End If
    End If

  End Sub

  Public Sub SetRequest(Request As String)
    txtRequest.Text = Request
  End Sub

  Private Sub frmJsonConsole_Load(sender As Object, e As EventArgs) Handles Me.Load
    btnSendMessage.Enabled = _Forecourt.IsLoggedOn
  End Sub

  Private Sub Forecourt_ConnectionClosed(WasLost As Boolean) Handles _Forecourt.ConnectionClosed
    btnSendMessage.Enabled = False
  End Sub

  Private Sub Forecourt_ConnectionEstablished() Handles _Forecourt.ConnectionEstablished
    btnSendMessage.Enabled = True
  End Sub

  Private Sub txtResponse_KeyDown(sender As Object, e As KeyEventArgs) Handles txtResponse.KeyDown, txtRequest.KeyDown
    If (e.KeyCode And Not Keys.Modifiers) = Keys.A AndAlso e.Modifiers = Keys.Control Then
      DirectCast(sender, TextBox).SelectAll()
      e.Handled = True
    End If

  End Sub

End Class