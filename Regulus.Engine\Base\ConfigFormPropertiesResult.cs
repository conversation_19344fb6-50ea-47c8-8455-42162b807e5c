﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Globalization;

namespace Triquestra.Common.PumpEsm.Base
{
    public class ConfigFormPropertiesResult : PumpEsmActionResult
    {
        public ConfigFormPropertiesResult(int formTop)
            : base(true, null)
        {
            PumpFormTop = formTop;

            ResultText = formTop.ToString(CultureInfo.InvariantCulture);
        }

        public int PumpFormTop { get; private set; }


    }
}
