﻿<Project Sdk="Microsoft.NET.Sdk">
 <PropertyGroup>
    <TargetFrameworks>net461;net48</TargetFrameworks>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Authors>Triquestra</Authors>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <title>Triquestra.Common.PumpEsm.RegulusInterface</title>
    <description>Triquestra.Common.PumpEsm.RegulusInterface</description>
    <RootNamespace>Triquestra.Common.PumpEsm.RegulusInterface</RootNamespace>
    <AssemblyName>Triquestra.Common.Forecourt</AssemblyName>
   <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IPumpController.cs" />
    <Compile Include="IRegulus.cs" />
    <Compile Include="ISystemManager.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PumpController.cs" />
    <Compile Include="Regulus.cs" />
    <Compile Include="SystemManager.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Triquestra.Common.ForeCourt.Comms\Triquestra.Common.ForeCourt.Comms.csproj" />
    <ProjectReference Include="..\Triquestra.Common.ForeCourt.Messaging\Triquestra.Common.ForeCourt.Messaging.csproj" />
  </ItemGroup>

</Project>

