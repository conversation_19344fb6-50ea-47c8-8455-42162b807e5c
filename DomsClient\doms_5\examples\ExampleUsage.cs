﻿using System;
using System.Threading.Tasks;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Services;
using DomsIntegration.Core.Utilities;
using DomsIntegration.Core.Messages.Requests;

namespace DomsIntegration.Examples
{
    /// <summary>
    /// Example usage of the Doms POS Client library
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            // Create logger
            var logger = new ConsoleLogger();
            logger.LogInfo(""Starting Doms POS Client Example"");

            // Create client
            using (var client = new JplClient(logger))
            {
                try
                {
                    // Subscribe to unsolicited messages
                    client.UnsolicitedMessageReceived += (sender, message) =>
                    {
                        logger.LogInfo($""Unsolicited message received: {message.Name}"");
                    };

                    // Connect to controller
                    logger.LogInfo(""Connecting to PSS controller..."");
                    await client.ConnectAsync(""192.168.1.100"", useTls: false);
                    
                    // Create services
                    var logonService = new LogonService(client, logger);
                    var forecourtService = new ForecourtControlService(client, logger);
                    var dispenseService = new DispenseControlService(client, logger);
                    
                    // Perform logon
                    logger.LogInfo(""Performing logon..."");
                    var logonResponse = await logonService.LogonAsync(""POS"", ""0045"", ""ExamplePOS.1.0"");
                    logger.LogInfo($""Logged on to controller version {logonResponse.Data.FcSwVersionNo}"");
                    
                    // Get controller status
                    logger.LogInfo(""Getting controller status..."");
                    var status = await forecourtService.GetStatusAsync();
                    logger.LogInfo($""Controller status - PumpTotalsReady: {status.Data.FcStatus1Flags.Bits.PumpTotalsReady}"");
                    
                    // Set date/time
                    logger.LogInfo(""Synchronizing date/time..."");
                    await forecourtService.SetDateTimeAsync(DateTime.Now);
                    logger.LogInfo(""Date/time synchronized"");
                    
                    // Get installation status
                    logger.LogInfo(""Getting installation status..."");
                    var installStatus = await forecourtService.GetInstallationStatusAsync();
                    logger.LogInfo($""Installed device groups: {installStatus.Data.InstalledFcDeviceGroups?.Length ?? 0}"");
                    
                    // Example fuelling point operations
                    if (installStatus.Data.InstalledFcDeviceGroups?.Length > 0)
                    {
                        // Find fuelling points
                        foreach (var deviceGroup in installStatus.Data.InstalledFcDeviceGroups)
                        {
                            if (deviceGroup.InstallMsgCode == ""10H"" || deviceGroup.ExtendedInstallMsgCode == ""0010H"") // Fuelling points
                            {
                                foreach (var fpId in deviceGroup.FcDeviceId)
                                {
                                    logger.LogInfo($""Working with FP {fpId}"");
                                    
                                    // Get FP status
                                    var fpStatus = await dispenseService.GetFuellingPointStatusAsync(fpId);
                                    logger.LogInfo($""FP {fpId} status: {fpStatus.Data.FpMainState?.Value}"");
                                    
                                    // Open FP (if not already open)
                                    if (fpStatus.Data.FpMainState?.Value != ""02H"") // Not idle
                                    {
                                        await dispenseService.OpenFuellingPointAsync(fpId, ""01"");
                                        logger.LogInfo($""Opened FP {fpId}"");
                                    }
                                    
                                    // Authorize FP
                                    await dispenseService.AuthorizeFuellingPointAsync(fpId, ""01"");
                                    logger.LogInfo($""Authorized FP {fpId}"");
                                    
                                    // Wait a bit
                                    await Task.Delay(2000);
                                    
                                    // Cancel authorization
                                    await dispenseService.CancelFuellingPointAuthorizationAsync(fpId, ""01"");
                                    logger.LogInfo($""Cancelled authorization for FP {fpId}"");
                                    
                                    break; // Just demo with first FP
                                }
                                break;
                            }
                        }
                    }
                    
                    // Keep connection alive for a while to receive unsolicited messages
                    logger.LogInfo(""Listening for unsolicited messages for 30 seconds..."");
                    await Task.Delay(30000);
                }
                catch (Exception ex)
                {
                    logger.LogError($""Application error: {ex.Message}"", ex);
                }
                finally
                {
                    logger.LogInfo(""Disconnecting..."");
                    client.Disconnect();
                }
            }

            logger.LogInfo(""Example completed"");
            Console.WriteLine(""Press any key to exit..."");
            Console.ReadKey();
        }
    }
}
