using System;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class JplConsoleForm : Form
    {
        public Forecourt Forecourt { get; set; }

        public JplConsoleForm()
        {
            InitializeComponent();
        }

        private void JplConsoleForm_Load(object sender, EventArgs e)
        {
            // Initialize console
            txtOutput.Text = "JPL Console - Enter JSON commands below\r\n";
        }

        private void btnSend_Click(object sender, EventArgs e)
        {
            if (Forecourt != null && !string.IsNullOrWhiteSpace(txtInput.Text))
            {
                string command = txtInput.Text.Trim();
                txtOutput.AppendText($">> {command}\r\n");

                try
                {
                    Forecourt.SendUserDefinedRequest(command, (success, data, ex) =>
                    {
                        if (InvokeRequired)
                        {
                            Invoke(new Action(() =>
                            {
                                if (success)
                                {
                                    dynamic result = data;
                                    txtOutput.AppendText($"<< SUCCESS: {result.Response.Json}\r\n");
                                }
                                else
                                {
                                    txtOutput.AppendText($"<< ERROR: {ex?.Message}\r\n");
                                }
                                txtOutput.ScrollToCaret();
                            }));
                        }
                        else
                        {
                            if (success)
                            {
                                dynamic result = data;
                                txtOutput.AppendText($"<< SUCCESS: {result.Response.Json}\r\n");
                            }
                            else
                            {
                                txtOutput.AppendText($"<< ERROR: {ex?.Message}\r\n");
                            }
                            txtOutput.ScrollToCaret();
                        }
                    });
                }
                catch (Exception ex)
                {
                    txtOutput.AppendText($"<< EXCEPTION: {ex.Message}\r\n");
                    txtOutput.ScrollToCaret();
                }

                txtInput.Clear();
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtOutput.Clear();
            txtOutput.AppendText("JPL Console - Enter JSON commands below\r\n");
        }

        private void txtInput_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && e.Control)
            {
                btnSend_Click(sender, e);
                e.Handled = true;
            }
        }
    }
}
