using System;
using System.Windows.Forms;

namespace JPL_Demo_POS_CSharp
{
    public partial class OptionsForm : Form
    {
        public OptionsForm()
        {
            InitializeComponent();
        }

        private void OptionsForm_Load(object sender, EventArgs e)
        {
            // Load settings into form controls
            LoadSettings();
        }

        private void LoadSettings()
        {
            var settings = Properties.Settings.Default;

            txtIPAddress.Text = settings.IPAddress ?? "";
            numPosId.Value = settings.PosId;
            numPortNumber.Value = settings.PortNumber;
            numVolumeDecimalPos.Value = settings.VolumeDecimalPointPosition;
            numPriceDecimalPos.Value = settings.PriceDecimalPointPosition;
            numMoneyDecimalPos.Value = settings.MoneyDecimalPointPosition;
            txtFcLogonString.Text = settings.FcLogonString ?? "";
            chkClearDeliveryReports.Checked = settings.ClearDeliveryReports;
            chkShowHeartBeatMessages.Checked = settings.ShowHeartBeatMessages;
            chkShowUnsolicitedMessages.Checked = settings.ShowUnsolicitedMessages;
            chkAutoLogon.Checked = settings.AutoLogon;
            chkClearBackOfficeRecords.Checked = settings.ClearBackOfficeRecords;
            chkAutoLockTransactions.Checked = settings.AutoLockTransactions;
        }

        private void SaveSettings()
        {
            var settings = Properties.Settings.Default;

            settings.IPAddress = txtIPAddress.Text;
            settings.PosId = (int)numPosId.Value;
            settings.PortNumber = (int)numPortNumber.Value;
            settings.VolumeDecimalPointPosition = (int)numVolumeDecimalPos.Value;
            settings.PriceDecimalPointPosition = (int)numPriceDecimalPos.Value;
            settings.MoneyDecimalPointPosition = (int)numMoneyDecimalPos.Value;
            settings.FcLogonString = txtFcLogonString.Text;
            settings.ClearDeliveryReports = chkClearDeliveryReports.Checked;
            settings.ShowHeartBeatMessages = chkShowHeartBeatMessages.Checked;
            settings.ShowUnsolicitedMessages = chkShowUnsolicitedMessages.Checked;
            settings.AutoLogon = chkAutoLogon.Checked;
            settings.ClearBackOfficeRecords = chkClearBackOfficeRecords.Checked;
            settings.AutoLockTransactions = chkAutoLockTransactions.Checked;

            settings.Save();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            SaveSettings();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
