﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.SystemMessages
{
    public class ForecourtSystemRequest:ForecourtMessage
    {
        public ForecourtSystemRequest(int sequenceNo,ForecourtCommandMessageTypes requestCode, string name) : base(ForecourtMessageClasses.SYSMGT_REQUEST, sequenceNo)
        {
            RequestCode = requestCode;
            Name = name;
        }

        public ForecourtCommandMessageTypes RequestCode { get; set; }

        public string Name { get; set; }
        
        public override XDocument Serialise()
        {
            var xdoc= base.Serialise();
            xdoc.Root.Add(
                new XElement("SysMgtReq", 
                    new XAttribute("Code",RequestCode)
                    )
                );
            if (!string.IsNullOrEmpty(Name))
            {
                xdoc.Root.Element("SysMgtReq").Add(new XAttribute("Name", Name));
            }

            return xdoc;
        }
    }
}
