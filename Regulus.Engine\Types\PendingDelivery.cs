﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Types
{
    public class PendingDelivery
    {

        private IDelivery _delivery;
        private DateTime _ts;
        public PendingDelivery(IDelivery delivery)
        {
            _delivery = delivery;
            _ts = DateTime.Now;
        }

        public IDelivery Delivery { get { return _delivery; } }

        public bool IsExpired()
        {
            // should it be the same constant
            return DateTime.Now.Subtract(_ts) > PumpInfoPosFooter.OFFLINE_TIMEOUT;
        }
    }

}
