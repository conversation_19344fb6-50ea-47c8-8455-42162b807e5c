﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Messages.Requests;
using DomsIntegration.Core.Messages.Responses;
using DomsIntegration.Core.Models;
using DomsIntegration.Core.Utilities;

namespace DomsIntegration.Core.Services
{
    /// <summary>
    /// Service for dispense control functions
    /// </summary>
    public class DispenseControlService
    {
        private readonly JplClient _client;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the DispenseControlService
        /// </summary>
        /// <param name=""client"">JPL client instance</param>
        /// <param name=""logger"">Logger instance</param>
        public DispenseControlService(JplClient client, ILogger logger = null)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// Gets the status of a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>FP status response</returns>
        public async Task<FpStatusResponse> GetFuellingPointStatusAsync(string fpId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug($"Requesting status for FP {fpId}");

                var request = new FpStatusRequest
                {
                    Data = new FpStatusRequestData
                    {
                        FpId = fpId
                    }
                };

                return await _client.SendMessageAsync<FpStatusResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to get FP {fpId} status: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Opens a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="posId">POS ID</param>
        /// <param name="operationModeNo">Operation mode number</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Open FP response</returns>
        public async Task<OpenFpResponse> OpenFuellingPointAsync(string fpId, string posId, int operationModeNo = 1, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Opening FP {fpId} with POS {posId}, mode {operationModeNo}");

                var request = new OpenFpRequest
                {
                    Data = new OpenFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId,
                        FpOperationModeNo = operationModeNo
                    }
                };

                return await _client.SendMessageAsync<OpenFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to open FP {fpId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Closes a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Close FP response</returns>
        public async Task<CloseFpResponse> CloseFuellingPointAsync(string fpId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Closing FP {fpId}");

                var request = new CloseFpRequest
                {
                    Data = new CloseFpRequestData
                    {
                        FpId = fpId
                    }
                };

                return await _client.SendMessageAsync<CloseFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to close FP {fpId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Authorizes a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="posId">POS ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authorize FP response</returns>
        public async Task<AuthorizeFpResponse> AuthorizeFuellingPointAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Authorizing FP {fpId} with POS {posId}");

                var request = new AuthorizeFpRequest
                {
                    Data = new AuthorizeFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<AuthorizeFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to authorize FP {fpId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Authorizes a fuelling point with preset limits
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="posId">POS ID</param>
        /// <param name="presetType">Preset type</param>
        /// <param name="volumeLimit">Volume limit (if applicable)</param>
        /// <param name="moneyLimit">Money limit (if applicable)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Authorize FP response</returns>
        public async Task<AuthorizeFpResponse> AuthorizeFuellingPointWithPresetAsync(
            string fpId,
            string posId,
            PresetType presetType,
            decimal? volumeLimit = null,
            decimal? moneyLimit = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Authorizing FP {fpId} with preset {presetType}");

                var request = new AuthorizeFpPresetRequest
                {
                    Data = new AuthorizeFpPresetRequestData
                    {
                        FpId = fpId,
                        PosId = posId,
                        PresetType = ((int)presetType).ToString("00H"),
                        VoidPresetLimit = "0",
                        VolumePresetLimit = volumeLimit?.ToString() ?? "0",
                        MoneyPresetLimit = moneyLimit?.ToString() ?? "0",
                        FloorPresetLimit = "0"
                    }
                };

                return await _client.SendMessageAsync<AuthorizeFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to authorize FP {fpId} with preset: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Cancels authorization for a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="posId">POS ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Cancel authorization response</returns>
        public async Task<CancelFpAuthResponse> CancelFuellingPointAuthorizationAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo($"Cancelling authorization for FP {fpId}");

                var request = new CancelFpAuthRequest
                {
                    Data = new CancelFpAuthRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<CancelFpAuthResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to cancel authorization for FP {fpId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Emergency stops a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="posId">POS ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Emergency stop response</returns>
        public async Task<EstopFpResponse> EmergencyStopFuellingPointAsync(string fpId, string posId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogWarning($"Emergency stopping FP {fpId}");

                var request = new EstopFpRequest
                {
                    Data = new EstopFpRequestData
                    {
                        FpId = fpId,
                        PosId = posId
                    }
                };

                return await _client.SendMessageAsync<EstopFpResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to emergency stop FP {fpId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets a supervised transaction from a fuelling point
        /// </summary>
        /// <param name="fpId">Fuelling point ID</param>
        /// <param name="transSeqNo">Transaction sequence number</param>
        /// <param name="posId">POS ID</param>
        /// <param name="transParIds">Transaction parameter IDs to retrieve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Supervised transaction response</returns>
        public async Task<FpSupTransResponse> GetSupervisedTransactionAsync(
            string fpId,
            string transSeqNo,
            string posId,
            string[] transParIds = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug($"Getting supervised transaction {transSeqNo} from FP {fpId}");

                var request = new FpSupTransRequest
                {
                    Data = new FpSupTransRequestData
                    {
                        FpId = fpId,
                        TransSeqNo = transSeqNo,
                        PosId = posId,
                        TransParId = transParIds ?? new string[] { "51", "52", "53", "54" } // Basic transaction data
                    }
                };

                return await _client.SendMessageAsync<FpSupTransResponse>(request, cancellationToken: cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to get supervised transaction from FP {fpId}: {ex.Message}");
                throw;
            }
        }
    }
}
