﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;

namespace DomsPosProtocol.Network
{
    public class PssConnectionFactory
    {
        public static PssClient CreateClient(PssConnectionConfig config)
        {
            return new PssClient(config);
        }

        public static PssClient CreateUnencryptedClient(string hostname, int port = 8888)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = false
            };
            return new PssClient(config);
        }

        public static PssClient CreateEncryptedClient(string hostname, int port = 8889, 
            X509Certificate2 clientCertificate = null)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = true,
                ClientCertificate = clientCertificate
            };
            return new PssClient(config);
        }
    }
}
