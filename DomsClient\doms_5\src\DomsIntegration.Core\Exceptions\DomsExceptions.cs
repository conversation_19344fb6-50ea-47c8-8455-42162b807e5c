﻿using System;

namespace DomsIntegration.Core.Exceptions
{
    /// <summary>
    /// Base exception for all Doms POS client exceptions
    /// </summary>
    public class DomsPosException : Exception
    {
        public DomsPosException(string message) : base(message) { }
        public DomsPosException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown for communication-related errors
    /// </summary>
    public class DomsCommunicationException : DomsPosException
    {
        public DomsCommunicationException(string message) : base(message) { }
        public DomsCommunicationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown for protocol-related errors
    /// </summary>
    public class DomsProtocolException : DomsPosException
    {
        public DomsProtocolException(string message) : base(message) { }
        public DomsProtocolException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when a message is rejected by the controller
    /// </summary>
    public class DomsMessageRejectedException : DomsProtocolException
    {
        public string RejectCode { get; }
        public string RejectInfo { get; }

        public DomsMessageRejectedException(string rejectCode, string rejectInfo)
            : base($"Message rejected - Code: {rejectCode}, Info: {rejectInfo}")
        {
            RejectCode = rejectCode;
            RejectInfo = rejectInfo;
        }
    }
}
