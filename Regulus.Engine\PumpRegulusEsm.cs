﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.Base;
using System.Windows.Forms;
using Triquestra.Common;
using Triquestra.Common.PumpEsm.Types;
using Triquestra.Common.PumpEsm.DataAccess;
using System.Threading;
using Triquestra.Common.PumpEsm.Helpers;

namespace Triquestra.Common.PumpEsm
{
    public class PumpRegulusEsm: PumpEsmBase
    {
        NLog.Logger _log = NLog.LogManager.GetCurrentClassLogger();
        private PumpInfoPosFooter _forecourt = null;
        private PumpDataProvider _dataProvider;
        private Thread _formThread;
        private WinFormUi.Controls.PrepayForm _prepayForm;

        IGuiHelper _guiHelper = new GuiHelper();

        protected override PumpEsmActionResult EsmInit(System.Xml.XmlDocument request)
        {
            try
            {
                _dataProvider = new PumpDataProvider(Global.Database.Connection);
                CheckConfig();
            }
            catch (Exception ex)
            {
                var log = NLog.LogManager.GetCurrentClassLogger();
                log.Error(ex.ToString());
                return new PumpEsmActionResult(false, ex.Message);
            }
            
            try
            {
                //threadProc();
                _formThread = new Thread(threadProc);
                _formThread.Start();
                int timeoutCnt = 0;
                while (timeoutCnt < 5 && _forecourt == null)
                {
                    timeoutCnt++;
                    Thread.Sleep(500);
                }
                if (_forecourt == null)
                    throw new Exception("Unable to initialise the form");
            }
            catch (Exception ex)
            {
                var log = NLog.LogManager.GetCurrentClassLogger();
                log.Error(ex.ToString());
                return new PumpEsmActionResult(false, ex.Message);
            }
            return base.EsmInit(request);
        }

        private void threadProc(object param)
        {
            var screenBounds = Screen.PrimaryScreen.Bounds;            

            _forecourt = new PumpInfoPosFooter(_dataProvider, screenBounds.Width);
            _forecourt.SendScanCode = this.SendScanCode;
            _forecourt.MainActiveWindow = this.ApplicationWin32Window.Handle;
            _forecourt.SetGuiMode(Triquestra.Common.GUI.Mode.Till);
            _forecourt.PosMode = this.PosMode;
            _forecourt.ApplyTheme();
            _forecourt.Left = 0;
            _forecourt.Top = screenBounds.Height - _forecourt.Height;
            Application.Run(_forecourt);
        }

        

        private void CheckConfig()
        {
            var sb = new StringBuilder();
            if (!_dataProvider.Tanks.Any())
                sb.AppendLine("Tank configuration is missing");

            if (!_dataProvider.Dispensers.Any())
                sb.AppendLine("Dispenser configuration is missing");

            if (!_dataProvider.Nozzles.Any())
                sb.AppendLine("Nozzle configuration is missing");

            if (sb.Length > 0)
            {
                sb.AppendLine("Please contact support.");
                var txt = sb.ToString();
                Triquestra.Common.GUI.Dialogs.ShowMessage(Triquestra.Common.GUI.Mode.Till, txt, "Infinity Point Of Sale", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw new Exception(txt);
            }
        }

        
        protected override PumpEsmActionResult GetFormProperties(PumpEsmConfigRequest req)
        {
            var res = new ConfigFormPropertiesResult(Screen.PrimaryScreen.Bounds.Height - 185);
            return res;
        }

        protected override PumpEsmActionResult HidePumpForm(PumpEsmActionRequest req)
        {
            //form.Hide();
            return base.HidePumpForm(req);
        }

        protected override PumpEsmActionResult ShowPumpForm(PumpEsmActionRequest req)        
        {            
            return base.ShowPumpForm(req);
        }
        
        protected override PumpEsmActionResult GetItemData(PumpEsmConfigRequest req)
        {            
            //return new PumpEsmActionResult(true, "0,Z91,5.550,10.462,1.88,11,22,33,44,55,66,77,88,99,101,102");

            PetrolSaleItem delivery =_forecourt.ActiveDelivery;
            if (delivery==null || delivery.IsProcessed)
            {
                _guiHelper.ShowMessage("Unable to add an item into sale.",MessageBoxIcon.Error);
                return new PumpEsmActionResult(false, string.Empty);
            }
            
            delivery.SaleID = req.SaleNumber.Value;
            delivery.IsProcessed = true;            

            //fill CR16040
            var volume = delivery.Volume;
            var value = delivery.Amount;
            var price = delivery.Price;

            if (delivery.Mode == Messaging.AuthModes.PREPAY)
            {
                if (0 != delivery.DeliveryId && delivery.Limit > 0m)
                {
                    //negative quantity => refund
                    volume = delivery.Volume - Math.Round(delivery.Limit / price, 4);//as designed in ApplyPrepay(...)
                    value = value - delivery.Limit;
                }
            }
            //b147480 pass the values as they are, so the values for PostPay left untouched.
            ////b145726 this seems like helping with the rounding issue
            //volume = (delivery.Limit>0?-1m:1m) * (value / price);

            string txt1;
            string txt2;
            string txt3;
            string txt4;
            string txt5;
            string esmNote;
            GetTextFieldsInfo(delivery, out txt1, out txt2, out txt3, out txt4, out txt5, out esmNote);

            //CR16708 - Consignment
            var item = _dataProvider.GetItemByBlendId(delivery.BlendId);
            decimal? cost = GetDeliveryCost(delivery, item);

            _forecourt.AddDeliveryToList();

            ConfigGetItemDataResult res = new ConfigGetItemDataResult(delivery.Handle,
               item.UPC, volume, value, price,
                delivery.DispenserId.ToString(), delivery.DeliveryId)
                {
                    FieldText1 = txt1.Replace(',', ' '),
                    FieldText2 = txt2.Replace(',', ' '),
                    FieldText3 = txt3.Replace(',', ' '),
                    FieldText4 = txt4.Replace(',', ' '),
                    FieldText5 = txt5.Replace(',', ' '),
                Cost = cost,
                    ESMNote = esmNote
                };            

            _log.Debug("GetItemData response: {0}", res.ResultText);

            return res;
        }

        //CR16708 - Consignment
        private decimal? GetDeliveryCost(PetrolSaleItem delivery, Common.Database.Items item)
        {
            decimal comission = _dataProvider.GetConsigmentComission(item.UPC);
            decimal? cost = null;
            if (comission != 0m)
            {
                var tax = (100m + _dataProvider.GetTaxRate(item.TaxNo ?? 0)) / 100m;
                //we need to restore the price as it was before the taxes
                cost = (delivery.Price - comission) / tax;
            }
            return cost;
        }
        //protected override PumpEsmActionResult SetScanBoxHandle(PumpEsmActionRequest req)
        //{
        //    //_forecourt.ScanCodePtr = req.POSScanBoxWin32Window.Handle;
        //    return base.SetScanBoxHandle(req);
        //}
        private void GetTextFieldsInfo(PetrolSaleItem delivery, out string txt1, out string txt2, out string txt3, out string txt4, out string txt5, out string esmNote)
        {
            Fuel_Hose nozzle = null;
            Fuel_Tank tank = null;
            int deliveryId = delivery.DeliveryId;            

            var pump = _dataProvider.Dispensers.SingleOrDefault(s => s.ID == delivery.DispenserId);
            if (pump != null)
            {
                int nozzleId = 0;
                nozzleId = ((delivery.NozzleId == 1) ? pump.H1
                    : (delivery.NozzleId == 2) ? pump.H2
                        : (delivery.NozzleId == 3) ? pump.H3
                            : (delivery.NozzleId == 4) ? pump.H4
                                : 0) ?? 0;

                nozzle = _dataProvider.Nozzles.SingleOrDefault(s => s.ID == nozzleId);
            }
            if (nozzle != null)
                tank = _dataProvider.Tanks.SingleOrDefault(s => s.ID == nozzle.TankID);
            
            if (nozzle == null || tank == null)
            {
                var log = NLog.LogManager.GetCurrentClassLogger();
                log.Warn("The nozzle/tank configuration is missing or invalid. Pump #{0}, Nozzle #{1}", delivery.DispenserId, delivery.NozzleId);
            }
            txt1 = (tank != null ? tank.ID.ToString("00") : "00") + (tank != null ? tank.Description : string.Empty);
            txt2 = delivery.DispenserId.ToString("00") + "R" + (pump != null ? pump.Description.Substring(0, pump.Description.Length > 17 ? 17 : pump.Description.Length) : string.Empty);
            txt3 = nozzle != null ? nozzle.ID.ToString("00") + nozzle.Description.Substring(0, nozzle.Description.Length > 8 ? 8 : nozzle.Description.Length) : "00";
            txt4 = delivery.Mode == Messaging.AuthModes.PREPAY? "PREPAY" : string.Empty;
            txt5 = delivery.DispenserId.ToString();

            esmNote = deliveryId.ToString("0000000");

        }

        protected override PumpEsmActionResult OnItemVoided(PumpEsmNotifyRequest req)
        {
            _forecourt.ProcessDelivery(req.SaleID.Value, true);
            return base.OnItemVoided(req);
        }

        protected override PumpEsmActionResult OnSaleVoided(PumpEsmNotifyRequest req)
        {
            _forecourt.ProcessDeliveries(req.SaleID.Value, true);
            return base.OnSaleVoided(req);
        }

        protected override PumpEsmActionResult OnSaleCompleted(PumpEsmCompleteRequest req)
        {
            _forecourt.Receipt = req.Receipt;
            _forecourt.ProcessDeliveries(req.SaleID.Value, false);
            return base.OnSaleCompleted(req);
        }

        protected override PumpEsmActionResult PumpFormTopBottom(PumpEsmActionRequest req)
        {
            return new PumpEsmActionResult(false, string.Empty);
        }

        protected override PumpEsmActionResult OnDisablePetrolSales(PumpEsmNotifyRequest req)
        {
            _forecourt.AllowSales = false;
            return base.OnDisablePetrolSales(req);
        }

        protected override PumpEsmActionResult OnEnablePetrolSales(PumpEsmNotifyRequest req)
        {
            _forecourt.AllowSales = true;            
            return base.OnEnablePetrolSales(req);
        }

        protected override PumpEsmActionResult OnCheckAllOk(PumpEsmNotifyRequest req)
        {
            var res = _forecourt.ValidateSale(req.SaleID.Value);
            return new PumpEsmActionResult(res, string.Empty);
            //return new PumpEsmActionResult(true, string.Empty);
        }

        protected override PumpEsmActionResult DeInit(System.Xml.XmlDocument request)
        {
            _forecourt.Invoke(new Action(_forecourt.Close));
            _forecourt = null;
            return base.DeInit(request);
        }

    }
}
