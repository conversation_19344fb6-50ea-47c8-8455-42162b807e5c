﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.SystemMessages
{

    public class ForecourtSystemStatusResponse : ForecourtSystemResponse
    {
        public ForecourtSystemStatusResponse( int sequenceNo, string applicationName, ApplicationStatus status) : base(ForecourtCommandMessageTypes.APPLICATION_STATUS, sequenceNo)
        {
            ApplicationName = applicationName;
            Status = status;
        }

        public string ApplicationName { get; set; }
        public ApplicationStatus Status { get; set; }

        public static ForecourtSystemStatusResponse Parse(XContainer messageNode, int sequence)
        {
            var app = messageNode.Element("Application");
            if (app == null)
            {
                throw new XmlSchemaValidationException("SysMgtResp does not have <Application> node.");
            }
            var name = app.Attribute("Name") != null ? app.Attribute("Name").Value : "";
            var status = app.Attribute("Status") != null
                ? (ApplicationStatus)Enum.Parse(typeof(ApplicationStatus), app.Attribute("Status").Value)
                : ApplicationStatus.Unknown;
            return new ForecourtSystemStatusResponse(sequence, name, status);
        }
    }
}
