﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public interface INozzle
    {
        int NozzleId { get; set; }
        NozzleStates State { get; set; }
    }

    public class Nozzle : INozzle
    {
        public int NozzleId { get; set; }
        public NozzleStates State { get; set; }

        public static Nozzle Parse(XElement nz)
        {
            /*
            //<CommandResp Code="DISPENSER_DATA" Result="SUCCESS">
            //  <DispenserData>
            //    <Dispenser ID="1" State="IDLE">
            //      <Nozzles>
            //        <Nozzle ID="1" State="IN" />
            //      </Nozzles>
            //    </Dispenser>
            //  </DispenserData>
            //</CommandResp>
          */
            var nozzleId = int.Parse(nz.Attribute("ID").Value);
            var state = (NozzleStates)Enum.Parse(typeof(NozzleStates), nz.Attribute("State").Value);
            var nozzle = new Nozzle()
            {
                NozzleId = nozzleId,
                State = state
            };         
            return nozzle;
        }
    }
}
