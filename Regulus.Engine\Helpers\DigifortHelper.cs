﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Xml.Linq;
using Triquestra.Common.PumpEsm.Base;

namespace Triquestra.Common.PumpEsm.Helpers
{
    public class DigifortHelper: IDigifortHelper
    {
        const int EVENTLOG_PUMP_SUSPEND = 30148;
        private IntPtr _dgWindow;
        public DigifortHelper(IntPtr dgWindow)
        {
            _dgWindow = dgWindow;
        }

        private string FormatEventXml(bool isSuspend, int pumpNo)
        {
            string pumpInfo = string.Format("{0} {1}",
                    isSuspend ? "Stop" : "Play",
                    (pumpNo > 0) ? string.Format("Pump {0:00}", pumpNo) : "All");
            var xml = new XElement("event",
                new XElement("id", EVENTLOG_PUMP_SUSPEND),
                new XElement("name", "Pump suspend/resume"),
                new XElement("transno", 0),
                new XElement("total", 0m),
                new XElement("str", pumpInfo),
                new XElement("int", pumpNo),
                new XElement("dec1", isSuspend ? 1 : 2),
                new XElement("dec2", 0m),
                new XElement("theuser", 0m),
                new XElement("authuser", 0m)
                );

            return xml.ToString();
        }

        public void NotifySuspend(bool isSuspend)
        {
            NotifySuspend(isSuspend, 0);            
        }

        public void NotifySuspend(bool isSuspend,int pumpNo)
        {
            if (_dgWindow != IntPtr.Zero)
            {
                var msgToSend = FormatEventXml(isSuspend, pumpNo);

                Native.SetWindowText(_dgWindow, msgToSend);

                Native.PostMessage(_dgWindow, Native.WM_APP + 2052, (int)Native.WM_APP + 2052, (int)Native.WM_APP + 2052);

            }
        }        
    }
}
