﻿<?xml version="1.0" encoding="utf-8"?><Database Name="AKPOS" EntityNamespace="Triquestra.Common.PumpEsm.DataAccess" ContextNamespace="Triquestra.Common.PumpEsm.DataAccess" Class="PumpDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Connection Mode="AppSettings" ConnectionString="Data Source=.;Initial Catalog=AKPOS;Integrated Security=True" SettingsObjectName="Triquestra.Common.PumpEsm.DataAccess.Properties.Settings" SettingsPropertyName="AKPOSConnectionString" Provider="System.Data.SqlClient" />
  <Table Name="dbo.Fuel_Config" Member="Fuel_Configs">
    <Type Name="Fuel_Config">
      <Column Name="Parameter" Type="System.String" DbType="VarChar(100) NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="GroupID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Value" Type="System.String" DbType="VarChar(200)" CanBeNull="true" />
      <Column Name="Description" Type="System.String" DbType="VarChar(200)" CanBeNull="true" />
      <Column Name="ValueNotes" Type="System.String" DbType="VarChar(500)" CanBeNull="true" />
      <Column Name="BVisible" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Tanks" Member="Fuel_Tanks">
    <Type Name="Fuel_Tank">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="UPC" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="Capacity" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TankMeterType" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="HWA" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HVW" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LVW" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LVA" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HRA" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TempUhalo" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TempLhalo" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ToleranceLoss" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ToleranceGain" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TankType" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="ManifoldID" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Hoses" Member="Fuel_Hoses">
    <Type Name="Fuel_Hose">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="DeviceID" Type="System.String" DbType="VarChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="PumpID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TankID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MeterID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UPC" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Meters" Member="Fuel_Meters">
    <Type Name="Fuel_Meter">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="DeviceID" Type="System.String" DbType="VarChar(6) NOT NULL" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Offset" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="Rollover" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_ProfilePumpModes" Member="Fuel_ProfilePumpModes">
    <Type Name="Fuel_ProfilePumpMode">
      <Column Name="ProfileID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="PumpID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="AuthMode" Type="System.String" DbType="VarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="DispensorMode" Type="System.String" DbType="VarChar(8) NOT NULL" CanBeNull="false" />
      <Column Name="AuthModeMask" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Profiles" Member="Fuel_Profiles">
    <Type Name="Fuel_Profile">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="FromTime" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ToTime" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Enabled" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_PumpModels" Member="Fuel_PumpModels">
    <Type Name="Fuel_PumpModel">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Manufacturer" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="Name" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="Description" Type="System.String" DbType="VarChar(100)" CanBeNull="true" />
      <Column Name="Hoses" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="PriceLevels" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="ElectronicTotals" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PresetType" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Pumps" Member="Fuel_Pumps">
    <Type Name="Fuel_Pump">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="DFValue" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="DFVolume" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="DFPrice" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="H1" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="H2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="H3" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="H4" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StackSize" Type="System.Int16" DbType="SmallInt NOT NULL" CanBeNull="false" />
      <Column Name="PumpModelID" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LoopId" Type="System.String" DbType="VarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="CategoryType" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_Recon" Member="Fuel_Recons">
    <Type Name="Fuel_Recon">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TransNo" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Branch" Type="System.Int16" DbType="SmallInt NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Station" Type="System.Int16" DbType="SmallInt NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="TheUser" Type="System.String" DbType="NChar(10)" CanBeNull="true" />
      <Column Name="Status" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Logged" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Type" Type="System.String" DbType="VarChar(1)" CanBeNull="true" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SalesQty" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="MeterQty" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="TankLevelStart" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="TankLevelEnd" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="TestDeliveryQty" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="SMReceiptsQty" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="UPC" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="TankDesc" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Note" Type="System.String" DbType="VarChar(100)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Fuel_ReconDetails" Member="Fuel_ReconDetails">
    <Type Name="Fuel_ReconDetail">
      <Column Name="TransNo" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Branch" Type="System.Int16" DbType="SmallInt NOT NULL" CanBeNull="false" />
      <Column Name="Station" Type="System.Int16" DbType="SmallInt NOT NULL" CanBeNull="false" />
      <Column Name="Line" Type="System.Int16" DbType="SmallInt NOT NULL" CanBeNull="false" />
      <Column Name="OpenValue" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="CloseValue" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="MovementValue" Type="System.Decimal" DbType="Money" CanBeNull="true" />
      <Column Name="Type" Type="System.String" DbType="VarChar(1) NOT NULL" CanBeNull="false" />
      <Column Name="ItemID" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
</Database>