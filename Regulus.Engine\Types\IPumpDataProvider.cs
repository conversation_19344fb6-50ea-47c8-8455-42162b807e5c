﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.DataAccess;

namespace Triquestra.Common.PumpEsm.Types
{
    public interface IPumpDataProvider
    {
        Triquestra.Common.Database.Items GetItemByBlendId(int blendId);
        string GetUPCforBlendId(int blendId);
        decimal GetTaxRate(int taxId);
        string GetPumpConfig(string param,int group);
        decimal GetConsigmentComission(string upc);
        bool PumpAvailable(int pumpId);
        void ReportPumpSuspendAction(bool isSuspend, int pumpNo);
        bool IsPrepayBlocked();
        string GetCustomTextPrepay();
        HashSet<int> RetailPumps { get; }
        IEnumerable<Fuel_Tank> Tanks { get; }
        IEnumerable<Fuel_Pump> Dispensers { get; }
        IEnumerable<Fuel_Hose> Nozzles { get; }
        List<int> Denominations { get; }
        string DigifortTitle { get; }
        int WorkstationId { get; }
        string TcpAddress { get; }
        string UdpAddress { get; }
        int TcpPort { get; }
        int UdpPort { get; }

    }
}
