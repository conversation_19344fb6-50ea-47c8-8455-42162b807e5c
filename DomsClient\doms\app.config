<?xml version="1.0"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="JPL_Demo_POS.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <system.diagnostics>
        <sources>
            <!-- This section defines the logging configuration for My.Application.Log -->
            <source name="DefaultSource" switchName="DefaultSwitch">
                <listeners>
                    <add name="FileLog"/>
                    <!-- Uncomment the below section to write to the Application Event Log -->
                    <!--<add name="EventLog"/>-->
                </listeners>
            </source>
        </sources>
        <switches>
            <add name="DefaultSwitch" value="Information"/>
        </switches>
        <sharedListeners>
            <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
            <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
            <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
        </sharedListeners>
    </system.diagnostics>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup>
    <userSettings>
        <JPL_Demo_POS.My.MySettings>
            <setting name="PosId" serializeAs="String">
                <value>25</value>
            </setting>
            <setting name="IPAddress" serializeAs="String">
                <value/>
            </setting>
            <setting name="VolumeDecimalPointPosition" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="PriceDecimalPointPosition" serializeAs="String">
                <value>3</value>
            </setting>
            <setting name="MoneyDecimalPointPosition" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="FcLogonString" serializeAs="String">
                <value>POS,RI,UNSO_TRBUFSTA_3,UNSO_INSTSTA_1,UNSO_FPSTA_3:MFDR=01</value>
            </setting>
            <setting name="ClearDeliveryReports" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="ShowHeartBeatMessages" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ShowUnsolicitedMessages" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="CurrentRequest" serializeAs="String">
                <value/>
            </setting>
            <setting name="AutoLogon" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="PortNumber" serializeAs="String">
                <value>8888</value>
            </setting>
            <setting name="ClearBackOfficeRecords" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="AutoLockTransactions" serializeAs="String">
                <value>False</value>
            </setting>
        </JPL_Demo_POS.My.MySettings>
    </userSettings>
</configuration>
