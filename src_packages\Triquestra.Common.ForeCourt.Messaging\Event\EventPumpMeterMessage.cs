﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventPumpMeterMessage : ForecourtEventMessage
    {
        public int NozzleId { get; set; }
        public int BlendId { get; set; }
        public int TankId { get; set; }
        public decimal Price { get; set; }
        public decimal Amount { get; set; }
        public decimal Volume { get; set; }
        public int Deliveries { get; set; }
        public int DispenserId { get { return this.SourceId; } set { this.SourceId = value; } }
        //<FCCMessage>
        //<Header MessageType="EVENT" SourceID="1"/>
        //<Event Code="ELECTRONIC_TOTALS">
        //    <ElectronicTotals NozzleID="4" BlendID="3035" TankID="6" Price="2.704" Amount="1.622" Volume="0.600" Deliveries="0"/>
        //</Event>
        //</FCCMessage>

        public EventPumpMeterMessage(int sequenceNo, int sourceId, int targetId, int nozzleId, int blendId, int tankId,
            decimal price,decimal amount, decimal volume, int deliveries)
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.ELECTRONIC_TOTALS)
        {
            NozzleId = nozzleId;
            BlendId = blendId;
            TankId = tankId;
            Price = price;
            Amount = amount;
            Volume = volume;
            Deliveries = deliveries;
        }

        public static EventPumpMeterMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var eTotal = eventNode.Element("ElectronicTotals");

            if (eTotal == null)
            {
                throw new XmlSchemaException("eventNode does not have <ElectronicTotals> node");
            }

            var nozzleID = int.Parse(eTotal.Attribute("NozzleID").Value);
            var blendId = int.Parse(eTotal.Attribute("BlendID").Value);
            var tankId = int.Parse(eTotal.Attribute("TankID").Value);
            var price = decimal.Parse(eTotal.Attribute("Price").Value);
            var volume = decimal.Parse(eTotal.Attribute("Volume").Value);
            var amount = decimal.Parse(eTotal.Attribute("Amount").Value);

            var count = int.Parse(eTotal.Attribute("Deliveries").Value);

            return new EventPumpMeterMessage(seqNo, sourceId, targetId, nozzleID, blendId, tankId, price, amount, volume,
                count);
        }
    }
}
