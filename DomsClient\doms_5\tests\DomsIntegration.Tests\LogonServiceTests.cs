﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsIntegration.Core.Communication;
using DomsIntegration.Core.Services;
using DomsIntegration.Core.Utilities;
using System;

namespace DomsIntegration.Tests
{
    [TestClass]
    public class LogonServiceTests
    {
        [TestMethod]
        public void LogonService_Constructor_ShouldThrowExceptionForNullClient()
        {
            // Arrange, Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => new LogonService(null));
        }

        [TestMethod]
        public void LogonService_Constructor_ShouldInitializeWithValidClient()
        {
            // Arrange
            var client = new JplClient();

            // Act
            var service = new LogonService(client);

            // Assert
            Assert.IsNotNull(service);
        }

        [TestMethod]
        public void LogonService_Constructor_ShouldInitializeWithCustomLogger()
        {
            // Arrange
            var client = new JplClient();
            var logger = new ConsoleLogger();

            // Act
            var service = new LogonService(client, logger);

            // Assert
            Assert.IsNotNull(service);
        }
    }
}
