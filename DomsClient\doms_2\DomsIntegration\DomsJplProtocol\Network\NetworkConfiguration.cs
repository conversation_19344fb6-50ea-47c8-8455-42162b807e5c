﻿using System;
using System.Security.Cryptography.X509Certificates;

namespace DomsJplProtocol.Network
{
    // Connection configuration
    public class PssConnectionConfig
    {
        public string HostName { get; set; } = "localhost";
        public int Port { get; set; } = 8888;
        public bool UseTls { get; set; } = false;
        public int ConnectionTimeoutMs { get; set; } = 30000;
        public int HeartbeatIntervalMs { get; set; } = 20000;
        public int MaxHeartbeatMissedCount { get; set; } = 3;
        public X509Certificate2 ClientCertificate { get; set; }
        public bool ValidateServerCertificate { get; set; } = true;
    }

    // Connection state
    public enum ConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        LoggedOn,
        Disconnecting,
        Error
    }

    // Event arguments for connection events
    public class ConnectionStateChangedEventArgs : EventArgs
    {
        public ConnectionState OldState { get; set; }
        public ConnectionState NewState { get; set; }
        public string Reason { get; set; }
    }

    public class MessageReceivedEventArgs : EventArgs
    {
        public DomsJplProtocol.Core.JplMessage Message { get; set; }
        public string RawMessage { get; set; }
    }

    public class ErrorEventArgs : EventArgs
    {
        public Exception Exception { get; set; }
        public string Message { get; set; }
        public bool IsFatal { get; set; }
    }
}
