﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public interface IForecourtMessage
    {
        int SequenceNo { get; set; }
        ForecourtMessageClasses MessageClass { get; }
        XDocument Serialise();
        void Validate();
    }

    public enum ForecourtMessageClasses
    {
        Undefined,
        COMMAND_REQUEST,
        COMMAND_RESPONSE,
        EVENT,
        CONFIG_REQUEST,
        CONFIG_RESPONSE,
        XPathReq,
        XPathResp,
        SYSMGT_REQUEST,
        SYSMGT_RESPONSE
    }

    public enum ForecourtCommandMessageTypes
    {
        Unknown,
        APPLICATION_START,
        APPLICATION_STOP,
        APPLICATION_STATUS,
        AUTHORIZ<PERSON>, //Authorize a dispenser for delivery. 
        ABORT,
        CLEAR_EXCEPTION,
        CONTROLLER_CONFIG,
        CONTROLLER_TERMINATE,
        DELIVERY_CLEAR,
        DELIVERY_LOCK,
        DELIVERY_UNLOCK,
        DELIVERY_DATA,
        DISPENSER_SUSPEND,
        DISPENSER_RESUME,
        DISPENSER_RESET,
        DISPENSER_DATA,
        DISPENSER_LIST,
        DISPENSER_CONFIG,
        SYSTEM_TIME,
        DRIVER_STOP,
        DRIVER_SHUTDOWN,
        DRIVER_START,
        DISPENSER_SHUTDOWN,
        DISPENSER_START,
        GET_ELEC_TOTS,

        RESERVE, //Reserve (take ownership of) the dispenser prior to authorizing for a preauth or prepay delivery. 

        SET_PROFILE
    }

    public enum ForecourtConfigurationTypes
    {
        SET_CONFIG,
        GET_AUTHMODE_PROFILES
    }

    public enum ForecourtCommandResults
    {
        //General
        SUCCESS,
        Pump_Busy,
        Wrong_Pump_State,
        Wrong_Delivery_State,
        DELIVERY_STACK_FULL, //Delivery stack is full – can’t reserve for or authorize a new delivery.
        DELIVERY_DATA,
        DISPENSER_DATA,
        Invalid_Pump_Number,
        Invalid_Loop_ID,
        Pump_Not_Available,
        Invalid_SType,
        Loop_Not_Available,
        Controller_Not_Available,
        Driver_Not_Available,
        Failure,
        Software_Exception,
        //Command Specific - PrePay/PreAuth
        Invalid_Limit,
        Invalid_Limit_Type,
        Pump_Has_No_Preset,
        Hose_Not_Available,
        Invalid_Blend,
        Invalid_Price_Level,
        //Command Specific - PreAuth
        Pump_Not_Reserved,
        Volume_Limits_Not_Allowed,
        //Command Specific - Abort
        Pump_Not_Yours,
        //Command Specific - Reserve
        Pump_Already_Reserved,
        //Command Specific - Lock/Unlock/Clear
        Invalid_Delivery_ID,
        Delivery_Not_Found,
        Delivery_Locked_By_Another,
        //Command Specific - Lock
        Delivery_Already_Locked,
        //Command Specific - Unlock
        Delivery_Not_Locked,
    }

    public enum DispenserModes
    {
        Undefined,
        AUTO_AUTH,
        COMP_AUTH, // manual auth
    }

    public enum EventMessageTypes
    {
        HEARTBEAT,
        NOZZLE_STATE_CHANGE,
        DELIVERY_STARTED,
        DELIVERY_PROGRESS,
        DELIVERY_COMPLETE,
        DELIVERY_STATE_CHANGE,
        DELIVERY_TIMEOUT,
        DELIVERY_CLEAR,
        DELIVERY_DELETED,
        DELIVERY_ABORTED,
        DELIVERY_LOCKED,
        DELIVERY_STACKED,
        DELIVERY_UNLOCKED,
        DISPENSER_STATE_CHANGE,
        DISPENSER_CLEAR_REQUIRED,
        DISPENSER_MODE_CHANGE,
        AUTHMODE_PROFILE_CHANGE,
        CONFIG_FILE_MODIFIED,
        CONFIG_CHANGE,
        EXCEPTION,
        ELECTRONIC_TOTALS,
        ATTENDANT_TAG_SWIPE
    }

    public enum NozzleStates
    {
        NOT_AUTHORIZED,
        IN,
        OUT,
        DELIVERING,
        NOT_DELIVERING,
    }

    public enum DeliveryStates
    {
        INITIALIZED,
        RESERVED,
        AUTHORIZING,
        AUTHORIZED,
        NOT_DELIVERING,
        DELIVERING,
        FINISHING,
        COMPLETED,
        PREPAY_REFUND,
        REFUND_FINALIZING,
        REFUND_TAKEN,
        FINALIZING,
        FINALIZED,
        ABORTING,
        RECOVERING,
        DELETING,
        DELIVERY_DELETED
    }

    public enum DispenserStates
    {
        STOPPED,
        SYSTEM_ERROR,
        NOT_RESPONDING,
        INITIALIZING,
        INITIALIZED,
        RECOVERING,
        PRICE_CHANGING,
        SUSPENDED,
        SWITCHING_LIGHTS,
        IDLE,
        CALLING,
        CALLING_ATTENDANT,
        RESERVED,
        AUTHORIZING,
        AUTHORIZED,
        DELIVERING,
        NOT_DELIVERING,
        FINALIZED,
        FINISHING,
        CLEAR_REQUIRED,
        CLEARING,
    }

    public enum AuthModes
    {
        None,
        POSTPAY,
        PREPAY,
        PREAUTH
    }

    public enum DeliveryTimeoutTypes
    {
        PROTOCOL_TIMEOUT, //Dispenser failed to respond to poll.
        RESERVE_TIMEOUT, // Timed out waiting to transition from Reserved to Authorizing state.
        AUTH_TIMEOUT, // Timed out waiting to begin delivering fuel.
        NO_FLOW_TIMEOUT, //Delivery started (i.e., fuel dispensed), but fuel stopped flowing and no-flow timeout occurred.
        DELIVERY_TIMEOUT, //Delivery started but did not complete within the site-configured maximum delivery time period.
        MONITOR_TIMEOUT, //Delivery completed but timed out waiting to be finalized. Delivery was auto-finalized.
        PREPAY_REFUND_TIMEOUT, //Prepay delivery with refund due timed out waiting to be finalized. Delivery was auto-finalized.
        PREPAY_REFUND_TAKEN_TIMEOUT, //Prepay delivery with refund due was finalized (i.e., customer given refund) then held in memory (stack) until site-configured time period expired.
    }

    public enum DeliveryExceptions
    {
        NONE,
        TOTALS_LOST, // Delivery totals was not retrieved from dispenser. Exception causes transition to CLEAR_REQUIRED state on dispenser.
        DELIVERY_OVERRUN, //1.  Delivery went over a prepaid limit. Exception causes transition to CLEAR_REQUIRED state on dispenser.
                          //2.  Need to collect more money from customer. Finalize current delivery to clear CLEAR_REQUIRED state.
        RESERVE_ABORTED, // Abort indication - reserve.
        AUTHORIZED_ABORTED, // Abort indication - authorization.
        ABORTED, // Abort indication - delivery.
        SUSPENDED, // Suspend indication. When suspending, a delivery in progress is aborted and available for finalizing.
        REFUND_NOT_TAKEN, // Indication of prepay refund not being given to customer.
        RECOVERED_DELIVERY, // If a delivery is in progress when PumpControl is stopped (or fails), then when PumpControl restarts, the  delivery object, which was current prior to PumpControl termination, is restored, completed, and marked with this exception. 
        RECOVERY_DELIVERY, // If a delivery is in progress when PumpControl is started, a new delivery object is created, “connected” with delivery in progress, and marked with this exception. 
        DISPENSER_OFFLINE, // If a delivery is in progress when communications with the dispenser is lost (off-line), then the delivery object is completed and marked with this exception. 
        TOTALS_ERROR, // Error occurs when delivery is completed and TotalAmount does not equal TotalQuantity X UnitPrice. The inequality most typically is due to the dispenser and site configuration unit prices not being the same.
        HOLD_FOR_REFUND, // Current customer needs refund. Finalize current delivery to clear CLEAR_REQUIRED state.
        STACK_FULL, // Delivery stack is full. Finalize any delivery to clear CLEAR_REQUIRED state.
        NOZZLE_LEFT_OUT, // Nozzle left out after delivery. Stow the nozzle to clear CLEAR_REQUIRED state.
        DELIVERY_TOTALS_LOST, // Failed to get delivery completion totals from dispenser. Do manual recovery (read dispenser totals), then finalize the delivery to clear CLEAR_REQUIRED state.
        PRICE_ERROR,	// 	Unit price X total quantity does not equal total sale price. Reconcile delivery totals with customer, then finalize current delivery to clear the CLEAR_REQUIRED state.
		PUMP_OFFLINE,
        TIMED_OUT
    }

    public enum FinalizeMethods
    {
        SALE,
        NOSALE,
        TESTDELIVERY,
        DRIVEOFF,
        AUTO
    }

    public enum LimitTypes
    {
        NONE,
        VALUE,
        VOLUME
    }

    public enum ApplicationStatus
    {
        Unknown,
        Active,
        Disabled,
        Stopping
    }
}
