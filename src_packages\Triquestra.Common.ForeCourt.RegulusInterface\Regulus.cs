﻿using System;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public class Regulus:IRegulus
    {
        #region Private Elements
        private readonly IPumpController _pumpController;
        private readonly ISystemManager _systemManager;
        private readonly int _workstationId;
        #endregion

        #region Private Methods

        private void CheckRegulusTime(DateTime regulusTime)
        {
            var systemTime = DateTime.Now;
            //Log("CheckRegulusTime systemTime=" + systemTime + " regulusTime=" + regulusTime);
            if (DateTime.Compare(regulusTime, systemTime) < 0)
            {
                //regulus time is behind system time
                if (DateTime.Compare(regulusTime.AddHours(2), systemTime) < 0)
                {

                }
            }
        }

        #endregion


        #region Constructors

        /// <summary>
        /// We need to create a new instance of Regulus in order to access/control/consume forecourt services
        /// </summary>
        /// <param name="pumpController"></param>
        /// <param name="systemManager"></param>
        public Regulus(IPumpController pumpController, ISystemManager systemManager)
        {
            _pumpController = pumpController;
            _systemManager = systemManager;
            if (_pumpController != null)
            {
                _workstationId = pumpController.WorkstationId;
            }
        }
        #endregion
        public IPumpController PumpController { get { return _pumpController; } }

        public ISystemManager SystemManager { get { return _systemManager; } }

        public int WorkstationId { get { return _workstationId; } }        

        #region Public Methods

        public void Connect()
        {
            if(_pumpController!=null) _pumpController.Connect();
            if (_systemManager != null) _systemManager.Connect();
        }

        public void Disconnect()
        {
            if (_pumpController != null) _pumpController.Disconnect();
            if (_systemManager != null) _systemManager.Disconnect();
        }

        public void StartPumpController()
        {
            if (_systemManager != null) _systemManager.StartApplication("PumpControl");
        }

        public void StopPumpController()
        {
            if (_systemManager != null) _systemManager.StopApplication("PumpControl");
        }

        #endregion


    }


}
