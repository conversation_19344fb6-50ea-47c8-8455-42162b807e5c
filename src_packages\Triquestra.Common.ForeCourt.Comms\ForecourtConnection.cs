﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Xml.Linq;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.PumpEsm.Messaging.Event;
using Triquestra.Common.PumpEsm.Messaging.Request;
using Triquestra.Common.PumpEsm.Messaging.SystemMessages;
using Triquestra.Common.PumpEsm.Messaging.Response;

namespace Triquestra.Common.PumpEsm.Comms
{
    internal interface IForecourtConnectionEvents
    {
        void DoConnected(object sender, EventArgs e);
        void DoMessageReceived(object sender, EventArgs e);
    }
    public partial class ForecourtConnection : IForecourtConnection, IForecourtConnectionEvents
    {
        private static ManualResetEvent _receiveDone = new ManualResetEvent(false);
        private static readonly ManualResetEvent SendDone = new ManualResetEvent(false);
        private readonly string _tcpHost;
        private readonly int _tcpPort;
        private readonly string _udpHost;
        private readonly int _udpPort;
        protected int _station;
        private InternalConnection _conn;

        private class InternalConnection
        {
            private object _syncRoot = new object();
            private bool _receiveMessages;
            private bool __connected;
            private Socket _tcpClient;
            private UdpClient _udpClient;
            private Thread _clientThread;
            private readonly object _msgLock = new object();
            private DateTime _lastSendDataCall = DateTime.MinValue;
            private static readonly TimeSpan SENDDATA_TIMEOUT = new TimeSpan(0, 0, 0, 0, 300);
            private readonly string _tcpHost;
            private readonly int _tcpPort;
            private readonly string _udpHost;
            private readonly int _udpPort;
            private int _station;
            private bool _connected { get { lock (this._syncRoot) { return __connected; } } set { lock (this._syncRoot) { __connected = value; } } }
            public bool Connected { get { return _connected; } }
            internal InternalConnection(string tcpHost, int tcpPort, string udpHost, int udpPort, int station)
            {
                _tcpHost = tcpHost;
                _tcpPort = tcpPort;
                _udpHost = udpHost;
                _udpPort = udpPort;
                _station = station;
                __connected = false;
                _receiveMessages = false;
            }

            public Socket TcpSocket
            {
                get { return _tcpClient; }
            }

            
            //The method is executed in a separate thread!
            private void UdpReceive(object param)
            {
                Log("UdpReceive _receiveMessages=" + _receiveMessages);
                var udpClt = (UdpClient)param;
                try
                {
                    while (_receiveMessages)
                    {
                        try
                        {
                            var ipEp = new IPEndPoint(IPAddress.Any, _udpPort);
                            var data = udpClt.Receive(ref ipEp);
                            var stringData = Encoding.ASCII.GetString(data, 0, data.Length);
                            if (!stringData.Contains("HEARTBEAT"))
                            {
                                Log(@"c:\temp\comm\udpreceive.txt", stringData);
                            }
                            var messages = ProcessMessages(stringData);

                            OnMessageReceived(messages, EventArgs.Empty);
                        }
                        catch (SocketException se)
                        {
                            if (se.SocketErrorCode != SocketError.TimedOut)
                                throw;
                        }
                        catch (ObjectDisposedException)
                        {
                            //this is possible for disconnect
                        }
                        catch (Exception x)
                        {
                            LogException("UdpReceive", x);
                        }
                    }
                }
                finally
                {
                    udpClt.Close();
                }
            }

            /// <summary>
            /// we are receiving multiple xmls in one message unexpectedly. it needs to be splitted.
            /// </summary>
            /// <param name="response"></param>
            /// <returns></returns>
            private IEnumerable<string> SplitXmls(string messages)
            {
                /*
                 * 
                 * <?xml version="1.0" encoding="UTF-8"?>
                 * <FCCMessage><Header MessageType="COMMAND_RESPONSE" SeqNo="0" SourceID="2" TargetID="1"/><CommandResp Code="DELIVERY_DATA" Result="SUCCESS"><DeliveryData><Dispenser State="IDLE"/></DeliveryData></CommandResp></FCCMessage>
                 * 
                 * <?xml version="1.0" encoding="UTF-8"?>
                 * <FCCMessage><Header MessageType="COMMAND_RESPONSE" SeqNo="0" SourceID="2" TargetID="2"/><CommandResp Code="DELIVERY_DATA" Result="SUCCESS"><DeliveryData><Dispenser State="IDLE"/></DeliveryData></CommandResp></FCCMessage>
                 */
                var split = new[] { "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" };
                var messageArray = messages.Split(split, new StringSplitOptions());
                return messageArray.Where(s => !string.IsNullOrEmpty(s)).ToList();
            }

            private List<IForecourtMessage> ProcessMessages(string responseText)
            {
                const string re = @"[^\x09\x0A\x0D\x20-\xD7FF\xE000-\xFFFD\x10000-x10FFFF]";
                responseText = Regex.Replace(responseText, re, "");

                var xmls = SplitXmls(responseText);
                var result = new List<IForecourtMessage>();
                foreach (var xml in xmls)
                {
                    if (string.IsNullOrEmpty(xml))
                    {
                        Log("_respsonse is empty");
                        return null;
                    }

                    XDocument xdoc;
                    try
                    {
                        xdoc = XDocument.Parse(xml);
                    }
                    catch (Exception x)
                    {
                        LogException("failed to process response " + Environment.NewLine + xml + Environment.NewLine, x);
                        throw;
                    }
                    try
                    {
                        var responseMessage = ForecourtMessage.Deserialise(xdoc);
                        if (responseMessage != null && !(responseMessage is EventHeartbeatMessage))
                        {
                            Log(@"c:\temp\comm\" + responseMessage.MessageClass + ".txt", xdoc.ToString());
                        }
                        
                        //S19672
                        //if((responseMessage is CommandResponse) &&
                        //    ((CommandResponse)responseMessage).MessageType == ForecourtCommandMessageTypes.GET_ELEC_TOTS &&
                        //    ((CommandResponse)responseMessage).CommandResult ==ForecourtCommandResults.Wrong_Pump_State)
                        //{
                        //    ((EventPumpMeterMessage)responseMessage).Deliveries = 0;
                        //}

                        result.Add(responseMessage);

                    }
                    catch (Exception x)
                    {
                        Log(@"c:\temp\comm\failedmsg.txt", x.ToString());
                        Log(@"c:\temp\comm\failedmsg.txt", xdoc.ToString());
                        throw;
                    }
                }

                return result;
            }

            public void Connect()
            {
                const int UDP_RECEIVE_TIMEOUT = 5000;
                Log("Establishing TCP and UDP connections");
                if (_udpClient == null)
                {
                    Log(string.Format("UDP: {0}:{1}",_udpHost,_udpPort));
                    var udpEndPoint = new IPEndPoint(IPAddress.Any, _udpPort);
                    _udpClient = new UdpClient();
                    _udpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, 1);
                    _udpClient.Client.ReceiveTimeout = UDP_RECEIVE_TIMEOUT;
                    _udpClient.Client.Bind(udpEndPoint);
                    _udpClient.JoinMulticastGroup(IPAddress.Parse(_udpHost), 1);

                    _receiveMessages = true;

                    _clientThread = new Thread(UdpReceive);
                    _clientThread.Start(_udpClient);
                }
                if (_tcpClient != null)
                {
                    var clientToDisconnect = _tcpClient;
                    _tcpClient = null;
                    //was it concurrency?
                    if (clientToDisconnect != null)
                        clientToDisconnect.Close();
                }

                Log(string.Format("TCP: {0}:{1}", _tcpHost, _tcpPort));
                _tcpClient = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

                var tcpEndPoint = new IPEndPoint(IPAddress.Parse(_tcpHost), _tcpPort);

                _tcpClient.BeginConnect(tcpEndPoint, ConnectCallback, _tcpClient);

                //if (_tcpClient.Connected)
                //{
                //    OnConnected(EventArgs.Empty);
                //}
                //else
                //{
                //    Log("_tcpClient.Connected = false");
                //}
            }

            private void ConnectCallback(IAsyncResult ar)
            {
                Log("ConnectCallback");
                try
                {
                    var sock = (Socket)ar.AsyncState;
                    sock.EndConnect(ar);

                    _connected = _tcpClient.Connected;

                    if (_tcpClient.Connected)
                    {
                        OnConnected(EventArgs.Empty);
                    }
                    else
                    {
                        Log("_tcpClient.Connected = false");
                    }
                }
                catch (Exception ex)
                {
                    _connected = false;
                    LogException("ConnectCallback", ex);
                }
            }

            public void Disconnect()
            {
                Log("No more listeners found. Closing sockets.");
                try
                {
                    _receiveMessages = false;
                    _connected = false;
                    //allow some time to finalise the queue
                    if (_udpClient != null)
                    {
                        //var discUdp = _udpClient;
                        _udpClient = null;
                        //DO NOT close the UDP connection here - it'll be automatically closed in UdpReceive()
                        //if (discUdp != null)
                        //{
                        //    discUdp.Close();
                        //}
                    }
                    if (_tcpClient != null)
                    {
                        var discTcp = _tcpClient;
                        _tcpClient = null;                        
                        if (discTcp != null)
                        {
                            discTcp.Close();
                        }
                    }                    
                }
                catch(Exception ex)
                {
                    LogException("Disconnect", ex);
                }
            }

            public void Attach(IForecourtConnectionEvents me)
            {
                lock (_msgLock)
                {
                    _messageReceived.Add(me);

                    if (_messageReceived.Count == 1)
                        Connect();
                }
            }
            public void Detach(IForecourtConnectionEvents me)
            {
                lock (_msgLock)
                {
                    _messageReceived.Remove(me);

                    if (_messageReceived.Count == 0)
                        Disconnect();
                }
            }
            List<IForecourtConnectionEvents> _messageReceived = new List<IForecourtConnectionEvents>();

            public void OnMessageReceived(List<IForecourtMessage> sender, EventArgs e)
            {
                IForecourtConnectionEvents[] arrToInvoke;
                lock (_msgLock)
                    arrToInvoke = _messageReceived.ToArray();

                for (int i = 0; i < arrToInvoke.Length; i++)
                    if (arrToInvoke[i] != null)
                        arrToInvoke[i].DoMessageReceived(sender, e);
            }

            public void OnConnected(EventArgs e)
            {
                IForecourtConnectionEvents[] arrToInvoke;
                lock (_msgLock)
                    arrToInvoke = _messageReceived.ToArray();

                for (int i = 0; i < arrToInvoke.Length; i++)
                    if (arrToInvoke[i] != null)
                        arrToInvoke[i].DoConnected(this, e);

                TcpReceive();
            }

            protected void TcpReceive()
            {
                Log("TcpReceive()");
                try
                {
                    var state = new SocketReceiveState
                    {
                        WorkSocket = _tcpClient
                    };

                    _tcpClient.BeginReceive(state.buffer, 0, SocketReceiveState.BufferSize, 0, ReceiveCallback, state);
                }
                catch (Exception ex)
                {
                    LogException("TcpReceive", ex);
                }
            }

            private void ReceiveCallback(IAsyncResult ar)
            {
                Log("ReceiveCallback");
                try
                {
                    var state = (SocketReceiveState)ar.AsyncState;
                    var sock = state.WorkSocket;

                    //need to lock to ensure no race conditions around the checking around the availablity of data for coneection checking
                    SocketError err;
                    var bytesRead = sock.EndReceive(ar, out err);

                    if (bytesRead > 0)
                    {
                        var respsonse = Encoding.ASCII.GetString(state.buffer, 0, bytesRead);
                        Log("ReceiveCallback respsonse=" + respsonse);
                        try
                        {
                            var msg = ProcessMessages(respsonse);
                            OnMessageReceived(msg, EventArgs.Empty);
                        }
                        finally
                        {
                            if (_connected && sock.Connected)
                                sock.BeginReceive(state.buffer, 0, SocketReceiveState.BufferSize, 0, ReceiveCallback, state);
                        }
                    }
                    else
                    {
                        // https://msdn.microsoft.com/en-us/library/w7wtt64b(v=vs.110).aspx
                        //   zero data sent means the connection was dropped/closed. 
                        _connected = false;
                        Log("ReceiveCallback: the connection was dropped with the message: " + err.ToString());
                    }
                }
                catch (ObjectDisposedException)
                {
                    //b146160 - we may try to read from an already disconnected channel
                }
                catch (Exception ex)
                {
                    LogException("ReceiveCallback", ex);
                }
            }

            internal void SendData(string data)
            {
                Log("SendData=" + data);
                var byteData = Encoding.ASCII.GetBytes(data);
                lock (_msgLock)
                {

                    var dt = DateTime.Now;
                    if (dt.Subtract(_lastSendDataCall) < SENDDATA_TIMEOUT)
                    {
                        Log("SendData: too fast. Taking a nap...");
                        Thread.Sleep(SENDDATA_TIMEOUT);
                    }
                    _lastSendDataCall = dt.Add(SENDDATA_TIMEOUT);

                    if (_tcpClient == null)
                    {
                        throw new Exception("Controller is not connected.");
                    }

                    if (!_tcpClient.Connected || !_connected)
                    {
                        //by AC: the .Connect() method is async, we cannot execute .Send until we got a response from ConnectCallback()
                        Log("The controller is not connected. Attempting to reconnect");
                        Connect();
                        Thread.Sleep(500);
                        if (!_tcpClient.Connected)
                        {
                            Log("500ms... Not connected..");
                            Thread.Sleep(500);
                        }
                        if (!_tcpClient.Connected)
                        {
                            Log("1s... Not connected..");
                            throw new Exception("Unable to connect to the controller.");
                        }
                    }

                    try
                    {
                        _tcpClient.Send(byteData, 0, byteData.Length, SocketFlags.None);
                    }
                    catch (Exception x)
                    {
                        LogError("Send failed " + x.Message);
                        throw;
                    }
                    Log("_tcpClient.Send");
                }

            }
        }

        public bool IsConnected { get { return _conn != null && _conn.Connected; } }
        public ForecourtConnection(string tcpHost, int tcpPort, string udpHost, int udpPort)
            : this(tcpHost, tcpPort, udpHost, udpPort, 0)
        { }

        public ForecourtConnection(string tcpHost, int tcpPort, string udpHost, int udpPort, int station)
        {
            _tcpHost = tcpHost;
            _tcpPort = tcpPort;
            _udpHost = udpHost;
            _udpPort = udpPort;
            _station = station;
        }


        public void SendCommand(ForecourtCommandMessageTypes commandType)
        {
            SendCommand(ForecourtCommandRequest.Create(commandType));
        }

        public void SendCommand(int sourceId, ForecourtCommandMessageTypes commandType)
        {
            SendCommand(sourceId, 0, commandType);
        }

        public void SendCommand(int sourceId, int targetId, ForecourtCommandMessageTypes commandType)
        {
            var command = ForecourtCommandRequest.Create(sourceId, targetId, commandType);
            SendCommand(command);
        }

        public void SendCommand(IForecourtMessage command)
        {
            SendData(command.ToString());
        }

        protected void SendData(string command)
        {
            if (_conn != null)
                _conn.SendData(command.ToString());
            else
                throw new InvalidOperationException("Connection not established.");
        }

        public void SendSystemRequest(ForecourtCommandMessageTypes commandType, string name)
        {
            var command = new ForecourtSystemRequest(1, commandType, name);
            SendCommand(command);
        }

        public static void Log(string msg)
        {
            const string file = @"c:\temp\comm\comm.txt";
            Log(file, msg);
        }

        public static void LogError(string msg)
        {
            const string file = @"c:\temp\comm\exception.txt";
            Log(file, msg);
        }

        public static void LogException(string place, Exception x)
        {
            const string file = @"c:\temp\comm\exception.txt";
            LogError(place + Environment.NewLine + ExceptionMessage(x));
        }

        private static string ExceptionMessage(Exception x)
        {
            var sb = new StringBuilder();
            sb.AppendLine("Exception.Message=" + x.Message);
            sb.AppendLine("Exception.StackTrace" + x.StackTrace);
            if (x.InnerException != null)
            {
                sb.AppendLine(ExceptionMessage(x.InnerException));
            }

            return sb.ToString();
        }

        public static void Log(string file, string msg)
        {
            if (Directory.Exists(@"c:\temp\comm"))
            {
                // from Baran
                // ForecourtConnection.cs line 333 I uncommented old logging to make it work. Andrei needs to look at it when he is back            
                try
                {
                    using (var streamWriter = new StreamWriter(file, true))
                    {
                        streamWriter.WriteLine(DateTime.Now + " " + msg);
                        streamWriter.Flush();
                    }
                }
                catch (Exception x)
                {
                    Console.WriteLine("LoggerFile failed on write of:{0} ", x.Message);
                }
            }

            try
            {
                var log = NLog.LogManager.GetCurrentClassLogger();
                file = Path.GetFileNameWithoutExtension(file);
                if (file.StartsWith("exception") || file.StartsWith("failedmsg"))
                    log.Error(msg);
                else if (file.StartsWith("comm") || file.StartsWith("udpreceive"))
                    log.Trace(file + ":" + msg);
                else
                    log.Debug(file + ":" + msg);
            }
            catch
            {
                //    something is terribly wrong..
            }
        }

        public event EventHandler MessageReceived;

        public event EventHandler Connected;

        void IForecourtConnectionEvents.DoMessageReceived(object sender, EventArgs e)
        {
            OnMessageReceived((List<IForecourtMessage>)sender, e);
        }

        void IForecourtConnectionEvents.DoConnected(object sender, EventArgs e)
        {
            OnConnected(e);
        }

        public virtual void OnMessageReceived(List<IForecourtMessage> sender, EventArgs e)
        {
            var handler = MessageReceived;
            if (handler != null) handler(sender, e);
        }

        public virtual void OnConnected(EventArgs e)
        {
            var handler = Connected;
            if (handler != null) handler(this, e);
        }

        public Socket TcpSocket
        {
            get { throw new NotImplementedException(); }
        }

        public void Connect()
        {
            if (_conn != null)
            {
                if (!_conn.Connected)
                    _conn.Connect();
                else
                    throw new InvalidOperationException("Already connected.");
            }
            var conn = CPool<InternalConnection>.GetCachedConnection(_tcpHost, _tcpPort, _udpHost, _udpPort, _station,
                () =>
                {
                    var res = new InternalConnection(_tcpHost, _tcpPort, _udpHost, _udpPort, _station);
                    return res;
                });

            if (_conn == null)
            {
                Subscribe(conn);
            }
        }


        public void Disconnect()
        {
            if (_conn != null)
                UnSubscribe(_conn);
        }

        private void Subscribe(InternalConnection res)
        {
            try
            {
                res.Attach(this);
            }
            catch
            {
                UnSubscribe(res);
                throw;
            }
            _conn = res;
        }
        private void UnSubscribe(InternalConnection res)
        {
            _conn = null;
            res.Detach(this);
        }
    }
}
