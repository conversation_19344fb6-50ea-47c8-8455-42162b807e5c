﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.Database;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Builders
{
    /// <summary>
    /// Delivery builder
    /// </summary>
    public class DeliveryBuilder
    {
        /// <summary>
        /// Builds delivery from prepay and item 
        /// </summary>
        /// <param name="tankId">Tank ID. Will be assigned to zero if passed as null</param>
        /// <param name="prepay">Prepay data</param>
        /// <param name="item">Item data</param>
        /// <returns></returns>
        public static Delivery Build(int? tankId, IPrepay prepay, Items item)
        {
            var delivery = new Delivery
            {
                Mode = AuthModes.PREPAY,
                DeliveryId = 0,
                DispenserId = prepay.DispenserId,
                NozzleId = prepay.Blend.NozzleId,
                BlendId = prepay.Blend.BlendId,
                TankId = tankId ?? 0,
                Price = item.Price1,
                Amount = prepay.Amount,
                Volume = Math.Round(prepay.Amount / item.Price1, 4),
                State = DeliveryStates.RESERVED,
                Limit = 0m,
                LimitType = LimitTypes.VALUE
            };

            return delivery;
        }
    }
}
