﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public interface IDispenserData
    {
        int DispenserId { get; set; }
        DispenserStates State { get; set; }
        /// <summary>
        /// Only for AUTHORISED and DELIVERING states
        /// </summary>
        AuthModes AuthMode { get; set; }
        List<INozzle> Nozzles { get; set; }
    }

    public class DispenserData : IDispenserData
    {
        public int DispenserId { get; set; }
        public DispenserStates State { get; set; }
        /// <summary>
        /// Only for AUTHORISED and DELIVERING states
        /// </summary>
        public AuthModes AuthMode { get; set; }
        public List<INozzle> Nozzles { get; set; }

        public DispenserData(int id, DispenserStates state, IEnumerable<INozzle> nozzles) 
            : this(id, state, AuthModes.None, nozzles) 
        { }
        public DispenserData(int id, DispenserStates state, AuthModes mode, IEnumerable<INozzle> nozzles)
        {
            DispenserId = id;
            State = state;
            AuthMode = mode;
            Nozzles = new List<INozzle>();
            if (nozzles != null)
                Nozzles.AddRange(nozzles);
        }

        public static DispenserData Parse(XElement element)
        {
            AuthModes authMode = AuthModes.None;
            var state = (DispenserStates)Enum.Parse(typeof(DispenserStates), element.Attribute("State").Value);
            //b148160 add optional attribute Mode
            var modeAttr = element.Attribute("Mode");
            if (modeAttr != null)
            {
                authMode = (AuthModes)Enum.Parse(typeof(AuthModes), modeAttr.Value);
            }
            var id = int.Parse(element.Attribute("ID").Value);

            var nozzleElement = element.Element("Nozzles");
            if (nozzleElement==null)
                throw new XmlSchemaException("Node does not have Nozzles attribute");
            var nozzles = nozzleElement.Elements("Nozzle")
                .Select(Nozzle.Parse)
                .ToArray();
            return new DispenserData(id, state, authMode, nozzles);
            
            //<Dispenser ID="1" State="IDLE" Mode="PREPAY" >
            //  <Nozzles>
            //      <Nozzle ID="1" State="IN" />
            //  </Nozzles>
            //</Dispenser>

        }
    }
}
