﻿using System;
using System.Xml;
using Triquestra.Common.PumpEsm.Base.Helpers;
using System.Windows.Forms;

namespace Triquestra.Common.PumpEsm.Base
{
    public class PumpEsmActionRequest: PumpEsmRequest
    {       
        /// <summary>
        /// Creates an instance of the object.
        /// </summary>
        /// <param name="requestDoc">The request in XML format.</param>
        public PumpEsmActionRequest(XmlDocument requestDoc)
            :base(requestDoc)
        {
            ParseSaleAction();
        }

        internal void ParseSaleAction()
        {
            XmlNode msgNode = request.SelectSingleNode("action");
            if (null == msgNode)
                return ;
#if DEBUG            
            if (msgNode.InnerText != "action")
                throw new ArgumentException("The xml is not an action-type");
#endif
            msgNode = request.SelectSingleNode("message/PumpAction");
            if (msgNode!=null)
            {
                if (msgNode.InnerText=="PumpLogon")
                {
                    var pNode = request.SelectSingleNode("message/UserName");
                    if (pNode!=null)
                        UserName = pNode.InnerText;

                    pNode = request.SelectSingleNode("message/UserPassword");
                    if (pNode!=null)
                        UserPassword = pNode.InnerText;
                }

            }
            else
            {
                switch (this.Action)
                {
                    case PumpActions.SetScanBoxHandle:
                        long handle;
                        string s = Message;
                        long.TryParse(s.Substring(2,s.Length-2),out handle);
                        if (handle != 0)
                            this.POSScanBoxWin32Window = new WindowWrapper(new IntPtr(handle));
                        else
                            this.POSScanBoxWin32Window = null;
                        break;
                    case PumpActions.HidePumpForm:
                        break;
                    case PumpActions.ShowPumpForm:
                        break;
                    case PumpActions.LogonAllowed:
                        break;
                    case PumpActions.PumpFormOnTop:
                        break;

                    default: 
                        break;
                }
            }            
        }

        public new PumpActions Action
        {
            get
            {
                string s = Message;
                if (!string.IsNullOrEmpty(s))
                    return (PumpActions)s[0];
                else
                    return PumpActions.Unknown;
            }
        }
        /// <summary>
        /// Login information
        /// </summary>
        public string UserName { get; private set; }
        /// <summary>
        /// Password information
        /// </summary>
        public string UserPassword { get; private set; }
        /// <summary>
        /// The Win32 reference to a POS scanbox
        /// </summary>
        public IWin32Window POSScanBoxWin32Window { get; private set; }
    }
}
