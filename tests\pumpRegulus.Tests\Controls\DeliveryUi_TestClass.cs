﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Triquestra.Common.PumpEsm.Controls;

namespace pumpRegulus.Tests.Controls
{
    [TestClass]
    public class DeliveryUi_TestClass
    {
        [TestMethod]
        public void DeliveryUi_TestClass_()
        {
            DeliveryUi deliveryControl = new DeliveryUi();

            //deliveryControl.DeliveryList
        }
    }
}
