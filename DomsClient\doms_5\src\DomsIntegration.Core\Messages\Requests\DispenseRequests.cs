﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Models;

namespace DomsIntegration.Core.Messages.Requests
{
    /// <summary>
    /// FP Status request
    /// </summary>
    public class FpStatusRequest : JplRequest
    {
        public FpStatusRequest() : base("FpStatus_req") { }

        public new FpStatusRequestData Data { get; set; }
    }

    /// <summary>
    /// FP Status request data
    /// </summary>
    public class FpStatusRequestData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Open FP request
    /// </summary>
    public class OpenFpRequest : JplRequest
    {
        public OpenFpRequest() : base("open_Fp_req") { }

        public new OpenFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Open FP request data
    /// </summary>
    public class OpenFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
        public int FpOperationModeNo { get; set; }
    }

    /// <summary>
    /// Close FP request
    /// </summary>
    public class CloseFpRequest : JplRequest
    {
        public CloseFpRequest() : base("close_Fp_req") { }

        public new CloseFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Close FP request data
    /// </summary>
    public class CloseFpRequestData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Authorize FP request
    /// </summary>
    public class AuthorizeFpRequest : JplRequest
    {
        public AuthorizeFpRequest() : base("authorize_Fp_req") { }

        public new AuthorizeFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP request data
    /// </summary>
    public class AuthorizeFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// Authorize FP with preset request
    /// </summary>
    public class AuthorizeFpPresetRequest : JplRequest
    {
        public AuthorizeFpPresetRequest() : base("authorize_Fp_req", "01H") { }

        public new AuthorizeFpPresetRequestData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP with preset request data
    /// </summary>
    public class AuthorizeFpPresetRequestData : AuthorizeFpRequestData
    {
        public string PresetType { get; set; }
        public string VoidPresetLimit { get; set; }
        public string VolumePresetLimit { get; set; }
        public string MoneyPresetLimit { get; set; }
        public string FloorPresetLimit { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization request
    /// </summary>
    public class CancelFpAuthRequest : JplRequest
    {
        public CancelFpAuthRequest() : base("cancel_FpAuth_req") { }

        public new CancelFpAuthRequestData Data { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization request data
    /// </summary>
    public class CancelFpAuthRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// Emergency stop FP request
    /// </summary>
    public class EstopFpRequest : JplRequest
    {
        public EstopFpRequest() : base("estop_Fp_req") { }

        public new EstopFpRequestData Data { get; set; }
    }

    /// <summary>
    /// Emergency stop FP request data
    /// </summary>
    public class EstopFpRequestData
    {
        public string FpId { get; set; }
        public string PosId { get; set; }
    }

    /// <summary>
    /// FP supervised transaction request
    /// </summary>
    public class FpSupTransRequest : JplRequest
    {
        public FpSupTransRequest() : base("FpSupTrans_req") { }

        public new FpSupTransRequestData Data { get; set; }
    }

    /// <summary>
    /// FP supervised transaction request data
    /// </summary>
    public class FpSupTransRequestData
    {
        public string FpId { get; set; }
        public string TransSeqNo { get; set; }
        public string PosId { get; set; }
        public string[] TransParId { get; set; }
    }
}
