// Create this file in the Utilities directory
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Utilities
{
    /// <summary>
    /// Factory for creating and deserializing JPL messages
    /// </summary>
    public class JplMessageFactory
    {
        private static readonly Dictionary<string, Type> _requestTypes = new Dictionary<string, Type>();
        private static readonly Dictionary<string, Type> _responseTypes = new Dictionary<string, Type>();
        private static readonly JsonSerializerOptions _jsonOptions;
        private static bool _isInitialized = false;
        private static readonly object _initLock = new object();

        static JplMessageFactory()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        /// <summary>
        /// Initializes the message factory by scanning assemblies for message types
        /// </summary>
        public static void Initialize()
        {
            lock (_initLock)
            {
                if (_isInitialized)
                    return;

                RegisterMessagesFromAssembly(typeof(JplMessageFactory).Assembly);
                _isInitialized = true;
            }
        }

        /// <summary>
        /// Registers all message types from the specified assembly
        /// </summary>
        public static void RegisterMessagesFromAssembly(Assembly assembly)
        {
            // Find all types that derive from JplRequest
            var requestTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && typeof(JplRequest).IsAssignableFrom(t));

            foreach (var type in requestTypes)
            {
                // Create an instance to get the default name and subcode
                try
                {
                    var instance = Activator.CreateInstance(type) as JplRequest;
                    if (instance != null)
                    {
                        // Use reflection to set required properties to avoid compiler errors
                        var nameProperty = type.GetProperty("Name");
                        var subcodeProperty = type.GetProperty("SubCode");

                        if (nameProperty != null && subcodeProperty != null)
                        {
                            // Try to determine the name from the class name
                            string messageName = type.Name;
                            if (messageName.EndsWith("Request"))
                                messageName = messageName.Substring(0, messageName.Length - 7) + "_req";

                            string subCode = JplConstants.SubCodes.SUBC_00H;
                            
                            nameProperty.SetValue(instance, messageName);
                            subcodeProperty.SetValue(instance, subCode);

                            RegisterRequestType(messageName, subCode, type);
                        }
                    }
                }
                catch
                {
                    // Skip types that can't be instantiated
                }
            }

            // Find all types that derive from JplResponse
            var responseTypes = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && typeof(JplResponse).IsAssignableFrom(t));

            foreach (var type in responseTypes)
            {
                // Create an instance to get the default name and subcode
                try
                {
                    var instance = Activator.CreateInstance(type) as JplResponse;
                    if (instance != null)
                    {
                        // Use reflection to set required properties to avoid compiler errors
                        var nameProperty = type.GetProperty("Name");
                        var subcodeProperty = type.GetProperty("SubCode");

                        if (nameProperty != null && subcodeProperty != null)
                        {
                            // Try to determine the name from the class name
                            string messageName = type.Name;
                            if (messageName.EndsWith("Response"))
                                messageName = messageName.Substring(0, messageName.Length - 8) + "_resp";

                            string subCode = JplConstants.SubCodes.SUBC_00H;
                            
                            nameProperty.SetValue(instance, messageName);
                            subcodeProperty.SetValue(instance, subCode);

                            RegisterResponseType(messageName, subCode, type);
                        }
                    }
                }
                catch
                {
                    // Skip types that can't be instantiated
                }
            }
        }

        /// <summary>
        /// Registers a request message type with the factory
        /// </summary>
        public static void RegisterRequestType(string messageName, string subCode, Type messageType)
        {
            if (!typeof(JplRequest).IsAssignableFrom(messageType))
                throw new ArgumentException($"Type {messageType.Name} must derive from JplRequest");

            string key = GetMessageKey(messageName, subCode);
            _requestTypes[key] = messageType;
        }

        /// <summary>
        /// Registers a response message type with the factory
        /// </summary>
        public static void RegisterResponseType(string messageName, string subCode, Type messageType)
        {
            if (!typeof(JplResponse).IsAssignableFrom(messageType))
                throw new ArgumentException($"Type {messageType.Name} must derive from JplResponse");

            string key = GetMessageKey(messageName, subCode);
            _responseTypes[key] = messageType;
        }

        /// <summary>
        /// Manually register a specific message type
        /// </summary>
        public static void RegisterMessageType<T>(string messageName, string subCode) where T : JplMessage
        {
            Type messageType = typeof(T);
            if (typeof(JplRequest).IsAssignableFrom(messageType))
            {
                RegisterRequestType(messageName, subCode, messageType);
            }
            else if (typeof(JplResponse).IsAssignableFrom(messageType))
            {
                RegisterResponseType(messageName, subCode, messageType);
            }
            else
            {
                throw new ArgumentException($"Type {messageType.Name} must derive from JplRequest or JplResponse");
            }
        }

        /// <summary>
        /// Creates a new request message instance of the specified type
        /// </summary>
        public static T CreateRequest<T>(string subCode = JplConstants.SubCodes.SUBC_00H) where T : JplRequest, new()
        {
            var request = new T
            {
                SubCode = subCode
            };
            
            // If Name is not set, try to derive it from the type name
            if (string.IsNullOrEmpty(request.Name))
            {
                string typeName = typeof(T).Name;
                if (typeName.EndsWith("Request"))
                    typeName = typeName.Substring(0, typeName.Length - 7);
                
                request.Name = $"{typeName}_req";
            }
            
            return request;
        }

        /// <summary>
        /// Deserializes a JSON message into the appropriate message type
        /// </summary>
        public static JplMessage DeserializeMessage(string messageJson)
        {
            if (!_isInitialized)
                Initialize();

            try
            {
                // First parse just to get the name and subcode
                using (JsonDocument document = JsonDocument.Parse(messageJson))
                {
                    var root = document.RootElement;
                    
                    if (root.TryGetProperty("name", out var nameElement) &&
                        root.TryGetProperty("subCode", out var subCodeElement))
                    {
                        string messageName = nameElement.GetString();
                        string subCode = subCodeElement.GetString();

                        // Handle heartbeat message separately
                        if (messageName == JplConstants.MessageNames.HEARTBEAT)
                            return JsonSerializer.Deserialize<HeartbeatMessage>(messageJson, _jsonOptions);

                        // Determine if it's a response message (has solicited property)
                        bool isResponse = root.TryGetProperty("solicited", out _);

                        string key = GetMessageKey(messageName, subCode);
                        
                        if (isResponse)
                        {
                            if (_responseTypes.TryGetValue(key, out Type responseType))
                            {
                                return (JplMessage)JsonSerializer.Deserialize(messageJson, responseType, _jsonOptions);
                            }
                            else
                            {
                                // Fall back to generic response
                                return JsonSerializer.Deserialize<JplResponse>(messageJson, _jsonOptions);
                            }
                        }
                        else
                        {
                            if (_requestTypes.TryGetValue(key, out Type requestType))
                            {
                                return (JplMessage)JsonSerializer.Deserialize(messageJson, requestType, _jsonOptions);
                            }
                            else
                            {
                                // Fall back to generic request
                                return JsonSerializer.Deserialize<JplRequest>(messageJson, _jsonOptions);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error deserializing message: {ex.Message}");
            }

            // Default fallback
            return JsonSerializer.Deserialize<JplMessage>(messageJson, _jsonOptions);
        }

        /// <summary>
        /// Gets the message key used for lookup in the type dictionaries
        /// </summary>
        private static string GetMessageKey(string messageName, string subCode)
        {
            return $"{messageName}:{subCode}";
        }
    }
}