﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Utilities;

namespace DomsJplProtocol.Network
{
    public class PssClient : IDisposable
    {
        private readonly PssConnectionConfig _config;
        private TcpClient _tcpClient;
        private Stream _stream;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Timer _heartbeatTimer;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplResponse>> _pendingRequests;
        private ConnectionState _connectionState;
        private DateTime _lastMessageReceived;
        private readonly object _stateLock = new object();
        private readonly JsonSerializerOptions _jsonOptions;

        public event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;
        public event EventHandler<MessageReceivedEventArgs> UnsolicitedMessageReceived;
        public event EventHandler<ErrorEventArgs> ErrorOccurred;

        public ConnectionState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _connectionState;
                }
            }
            private set
            {
                lock (_stateLock)
                {
                    var oldState = _connectionState;
                    _connectionState = value;
                    ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs
                    {
                        OldState = oldState,
                        NewState = value
                    });
                }
            }
        }

        public bool IsConnected => State == ConnectionState.Connected || State == ConnectionState.LoggedOn;
        public bool IsLoggedOn => State == ConnectionState.LoggedOn;

        public PssClient(PssConnectionConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _cancellationTokenSource = new CancellationTokenSource();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplResponse>>();
            _connectionState = ConnectionState.Disconnected;
            _lastMessageReceived = DateTime.UtcNow;

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            _heartbeatTimer = new Timer(OnHeartbeatTimer, null, Timeout.Infinite, Timeout.Infinite);
            
            // Register all message types
            MessageRegistry.RegisterAllMessageTypes();
        }

        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected)
                return true;

            try
            {
                State = ConnectionState.Connecting;

                _tcpClient = new TcpClient();
                var connectTask = _tcpClient.ConnectAsync(_config.HostName, _config.Port);
                await connectTask;

                if (_config.UseTls)
                {
                    var sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                    var clientCertificates = _config.ClientCertificate != null 
                        ? new X509CertificateCollection { _config.ClientCertificate } 
                        : new X509CertificateCollection();

                    await sslStream.AuthenticateAsClientAsync(_config.HostName, clientCertificates, 
                        System.Security.Authentication.SslProtocols.Tls12, false);
                    _stream = sslStream;
                }
                else
                {
                    _stream = _tcpClient.GetStream();
                }

                State = ConnectionState.Connected;
                _lastMessageReceived = DateTime.UtcNow;

                // Start reading messages
                Task.Run(async () => await ReadMessagesAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                // Start heartbeat timer
                _heartbeatTimer.Change(_config.HeartbeatIntervalMs, _config.HeartbeatIntervalMs);

                return true;
            }
            catch (Exception ex)
            {
                State = ConnectionState.Error;
                OnError(ex, "Failed to connect", true);
                return false;
            }
        }

        public async Task<FcLogonResponse> LogonAsync(string accessCode, string countryCode, string posVersionId, 
            CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Must be connected before logging on");

            var request = new FcLogonRequest
            {
                Name = JplConstants.MessageNames.FC_LOGON_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcLogonRequest.FcLogonData
                {
                    FcAccessCode = accessCode,
                    CountryCode = countryCode,
                    PosVersionId = posVersionId
                }
            };

            var response = await SendRequestAsync<FcLogonResponse>(request, cancellationToken);
            if (response != null)
            {
                State = ConnectionState.LoggedOn;
            }

            return response;
        }

        public async Task<T> SendRequestAsync<T>(JplRequest request, CancellationToken cancellationToken = default) 
            where T : JplResponse
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var correlationId = Guid.NewGuid().ToString();
            request.CorrelationId = correlationId;

            var tcs = new TaskCompletionSource<JplResponse>();
            _pendingRequests[correlationId] = tcs;

            try
            {
                await SendMessageAsync(request, cancellationToken);
                
                using (cancellationToken.Register(() => tcs.TrySetCanceled()))
                {
                    var response = await tcs.Task;
                    return response as T;
                }
            }
            finally
            {
                TaskCompletionSource<JplResponse> removed;
                _pendingRequests.TryRemove(correlationId, out removed);
            }
        }

        public async Task SendMessageAsync(JplMessage message, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var messageBytes = Encoding.UTF8.GetBytes(json);
            
            var frame = new byte[messageBytes.Length + 2];
            frame[0] = JplConstants.NetworkConstants.STX;
            Array.Copy(messageBytes, 0, frame, 1, messageBytes.Length);
            frame[frame.Length - 1] = JplConstants.NetworkConstants.ETX;

            await _stream.WriteAsync(frame, 0, frame.Length, cancellationToken);
            await _stream.FlushAsync(cancellationToken);
        }

        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
        {
            var heartbeat = new HeartbeatMessage
            {
                Name = JplConstants.MessageNames.HEARTBEAT,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new HeartbeatMessage.HeartbeatData()
            };

            await SendMessageAsync(heartbeat, cancellationToken);
        }

        public async Task DisconnectAsync()
        {
            State = ConnectionState.Disconnecting;
            
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _cancellationTokenSource?.Cancel();

            try
            {
                _stream?.Close();
                _tcpClient?.Close();
            }
            catch (Exception ex)
            {
                OnError(ex, "Error during disconnect", false);
            }
            finally
            {
                State = ConnectionState.Disconnected;
            }
        }

        private async Task ReadMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                    {
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    for (int i = 0; i < bytesRead; i++)
                    {
                        var currentByte = buffer[i];

                        if (currentByte == JplConstants.NetworkConstants.STX)
                        {
                            messageBuffer.Clear();
                        }
                        else if (currentByte == JplConstants.NetworkConstants.ETX)
                        {
                            if (messageBuffer.Count > 0)
                            {
                                var messageJson = Encoding.UTF8.GetString(messageBuffer.ToArray());
                                await ProcessReceivedMessage(messageJson);
                                messageBuffer.Clear();
                            }
                        }
                        else
                        {
                            messageBuffer.Add(currentByte);
                        }
                    }
                }
            }
            catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
            {
                OnError(ex, "Error reading messages", true);
            }
        }

        private async Task ProcessReceivedMessage(string messageJson)
        {
            try
            {
                // Use the message factory to deserialize the message
                var message = JplMessageFactory.DeserializeMessage(messageJson);
                
                if (message != null)
                {
                    // Notify listeners of all messages
                    MessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                    { 
                        Message = message, 
                        RawMessage = messageJson 
                    });

                    // Extract correlation ID if present
                    string correlationId = null;
                    using (JsonDocument document = JsonDocument.Parse(messageJson))
                    {
                        var root = document.RootElement;
                        if (root.TryGetProperty("correlationId", out var corrIdElement))
                        {
                            correlationId = corrIdElement.GetString();
                        }
                    }

                    // Complete pending request if this is a response
                    if (!string.IsNullOrEmpty(correlationId))
                    {
                        if (_pendingRequests.TryRemove(correlationId, out var tcs))
                        {
                            if (message is JplResponse response)
                            {
                                tcs.SetResult(response);
                            }
                        }
                    }
                    // Notify listeners of unsolicited messages
                    else if (message is JplResponse response && !response.Solicited)
                    {
                        UnsolicitedMessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                        { 
                            Message = message, 
                            RawMessage = messageJson 
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                OnError(ex, $"Error processing message: {messageJson}", false);
            }
        }

        private void OnHeartbeatTimer(object state)
        {
            try
            {
                if (!IsConnected)
                    return;

                var timeSinceLastMessage = DateTime.UtcNow - _lastMessageReceived;
                if (timeSinceLastMessage.TotalMilliseconds > (_config.HeartbeatIntervalMs * _config.MaxHeartbeatMissedCount))
                {
                    OnError(new TimeoutException("Heartbeat timeout"), "Connection lost - no heartbeat", true);
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        await SendHeartbeatAsync(_cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        OnError(ex, "Failed to send heartbeat", false);
                    }
                });
            }
            catch (Exception ex)
            {
                OnError(ex, "Error in heartbeat timer", false);
            }
        }

        private bool ValidateServerCertificate(object sender, X509Certificate certificate, 
            X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            if (!_config.ValidateServerCertificate)
                return true;

            return sslPolicyErrors == SslPolicyErrors.None;
        }

        private void OnError(Exception exception, string message, bool isFatal)
        {
            ErrorOccurred?.Invoke(this, new ErrorEventArgs
            {
                Exception = exception,
                Message = message,
                IsFatal = isFatal
            });

            if (isFatal)
            {
                State = ConnectionState.Error;
                Task.Run(async () => await DisconnectAsync());
            }
        }

        public void Dispose()
        {
            _heartbeatTimer?.Dispose();
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Dispose();
        }
    }
}
