﻿using System;

namespace DomsJplProtocol.Core
{
    public static class JplConstants
    {
        // Message Types
        public static class MessageNames
        {
            // General FC Messages
            public const string FC_LOGON_REQ = "FcLogon_req";
            public const string FC_LOGON_RESP = "FcLogon_resp";
            public const string FC_STATUS_REQ = "FcStatus_req";
            public const string FC_STATUS_RESP = "FcStatus_resp";
            public const string FC_DATE_TIME_REQ = "FcDateAndTime_req";
            public const string FC_DATE_TIME_RESP = "FcDateAndTime_resp";
            public const string CHANGE_FC_DATE_TIME_REQ = "change_FcDateAndTime_req";
            public const string CHANGE_FC_DATE_TIME_RESP = "change_FcDateAndTime_resp";
            
            // Status Messages
            public const string FC_INSTALL_STATUS_REQ = "FcInstallStatus_req";
            public const string FC_INSTALL_STATUS_RESP = "FcInstallStatus_resp";
            public const string FC_PRICE_SET_STATUS_REQ = "FcPriceSetStatus_req";
            public const string FC_PRICE_SET_STATUS_RESP = "FcPriceSetStatus_resp";
            public const string FC_OPERATION_MODE_STATUS_REQ = "FcOperationModeStatus_req";
            public const string FC_OPERATION_MODE_STATUS_RESP = "FcOperationModeStatus_resp";
            
            // Service Messages
            public const string FC_SERVICE_MSG_REQ = "FcServiceMsg_req";
            public const string FC_SERVICE_MSG_RESP = "FcServiceMsg_resp";
            public const string CLEAR_FC_SERVICE_MSG_REQ = "clear_FcServiceMsg_req";
            public const string CLEAR_FC_SERVICE_MSG_RESP = "clear_FcServiceMsg_resp";
            
            // Fuelling Point Messages
            public const string FP_STATUS_REQ = "FpStatus_req";
            public const string FP_STATUS_RESP = "FpStatus_resp";
            public const string OPEN_FP_REQ = "open_Fp_req";
            public const string OPEN_FP_RESP = "open_Fp_resp";
            public const string CLOSE_FP_REQ = "close_Fp_req";
            public const string CLOSE_FP_RESP = "close_Fp_resp";
            public const string AUTH_FP_REQ = "auth_Fp_req";
            public const string AUTH_FP_RESP = "auth_Fp_resp";
            
            // Price Messages
            public const string CHANGE_FC_PRICE_SET_REQ = "change_FcPriceSet_req";
            public const string CHANGE_FC_PRICE_SET_RESP = "change_FcPriceSet_resp";
            
            // Tank Messages
            public const string TANK_GAUGE_STATUS_REQ = "TankGaugeStatus_req";
            public const string TANK_GAUGE_STATUS_RESP = "TankGaugeStatus_resp";
            public const string TANK_GAUGE_DATA_REQ = "TankGaugeData_req";
            public const string TANK_GAUGE_DATA_RESP = "TankGaugeData_resp";
            
            // Utility
            public const string CHANGE_FC_STATUS_UPDATE_MODE_REQ = "change_FcStatusUpdateMode_req";
            public const string CHANGE_FC_STATUS_UPDATE_MODE_RESP = "change_FcStatusUpdateMode_resp";
            public const string ECHO_REQ = "echo_req";
            public const string ECHO_RESP = "echo_resp";
            public const string HEARTBEAT = "heartbeat";
            public const string MULTI_MESSAGE_RESP = "MultiMessage_resp";
            public const string REJECT_MESSAGE_RESP = "RejectMessage_resp";
            public const string JPL = "jpl";
        }

        public static class SubCodes
        {
            public const string SUBC_00H = "00H";
            public const string SUBC_01H = "01H";
            public const string SUBC_02H = "02H";
            public const string SUBC_03H = "03H";
        }

        public static class NetworkConstants
        {
            public const int UNENCRYPTED_PORT = 8888;
            public const int ENCRYPTED_TLS_PORT = 8889;
            public const byte STX = 2;
            public const byte ETX = 3;
        }
        
        public static class FuellingPointStates
        {
            public const string Closed = "00H";
            public const string Idle = "01H";
            public const string Calling = "02H";
            public const string Authorized = "03H";
            public const string Fuelling = "04H";
            public const string PreAuthorized = "05H";
            public const string PostPaid = "06H";
            public const string Suspended = "07H";
            public const string Stopped = "08H";
            public const string EndOfFuelling = "09H";
            public const string Reserved = "0AH";
            public const string Inoperative = "0BH";
        }
    }
}
