﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common;
using Triquestra.Common.GUI;
using System.Windows.Forms;

namespace Triquestra.Common.PumpEsm
{
    static class GuiHelper
    {
        public static void ShowMessage(string text,MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            NLog.LogManager.GetCurrentClassLogger().Debug("Dialog shown: "+text);
            Dialogs.ShowMessage(Mode.Till, text, "Infinity Point Of Sale", System.Windows.Forms.MessageBoxButtons.OK, icon);
        }
    }
}
