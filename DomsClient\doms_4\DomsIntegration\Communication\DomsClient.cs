﻿using System;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using Newtonsoft.Json;
using DomsIntegration.Models;
using DomsIntegration.Exceptions;
using DomsIntegration.Utilities;

namespace DomsIntegration.Communication
{
    /// <summary>
    /// Implementation of DOMs controller client
    /// </summary>
    public class DomsClient : IDomsClient
    {
        private TcpClient _tcpClient;
        private NetworkStream _stream;
        private bool _disposed;
        private readonly SemaphoreSlim _sendSemaphore;
        private readonly ILogger _logger;

        /// <inheritdoc />
        public bool IsConnected => _tcpClient?.Connected ?? false;

        /// <inheritdoc />
        public int ConnectionTimeout { get; set; } = 30000;

        /// <inheritdoc />
        public event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;

        /// <inheritdoc />
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsClient"/> class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        public DomsClient(ILogger logger = null)
        {
            _logger = logger ?? new NullLogger();
            _sendSemaphore = new SemaphoreSlim(1, 1);
        }

        /// <inheritdoc />
        public async Task ConnectAsync(DomsEndpoint endpoint, CancellationToken cancellationToken = default)
        {
            if (endpoint == null)
                throw new ArgumentNullException(nameof(endpoint));

            try
            {
                _logger.LogInfo($"Connecting to DOMs controller at {endpoint.Host}:{endpoint.Port}");

                _tcpClient = new TcpClient();
                
                var connectTask = _tcpClient.ConnectAsync(endpoint.Host, endpoint.Port);
                var timeoutTask = Task.Delay(ConnectionTimeout, cancellationToken);
                
                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                    throw new DomsConnectionException("Connection timeout");

                await connectTask;

                _stream = _tcpClient.GetStream();
                _stream.ReadTimeout = ConnectionTimeout;
                _stream.WriteTimeout = ConnectionTimeout;

                OnConnectionStatusChanged(true);
                _logger.LogInfo("Successfully connected to DOMs controller");

                // Start listening for incoming messages
                _ = Task.Run(() => ListenForMessages(cancellationToken), cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to connect to DOMs controller: {ex.Message}");
                await DisconnectAsync(cancellationToken);
                throw new DomsConnectionException("Failed to connect to DOMs controller", ex);
            }
        }

        /// <inheritdoc />
        public async Task DisconnectAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInfo("Disconnecting from DOMs controller");

                _stream?.Close();
                _tcpClient?.Close();

                OnConnectionStatusChanged(false);
                _logger.LogInfo("Disconnected from DOMs controller");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error during disconnection: {ex.Message}");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<TResponse> SendMessageAsync<TResponse>(IDomsMessage request, CancellationToken cancellationToken = default)
            where TResponse : class, IDomsMessage
        {
            if (!IsConnected)
                throw new DomsConnectionException("Not connected to DOMs controller");

            await _sendSemaphore.WaitAsync(cancellationToken);
            try
            {
                var requestJson = JsonConvert.SerializeObject(request);
                var requestBytes = Encoding.UTF8.GetBytes(requestJson + "\n");

                _logger.LogDebug($"Sending message: {requestJson}");

                await _stream.WriteAsync(requestBytes, 0, requestBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);

                // Wait for response (simplified - in real implementation you'd need proper message correlation)
                var responseBuffer = new byte[4096];
                var bytesRead = await _stream.ReadAsync(responseBuffer, 0, responseBuffer.Length, cancellationToken);
                
                var responseJson = Encoding.UTF8.GetString(responseBuffer, 0, bytesRead).Trim();
                _logger.LogDebug($"Received response: {responseJson}");

                return JsonConvert.DeserializeObject<TResponse>(responseJson);
            }
            finally
            {
                _sendSemaphore.Release();
            }
        }

        /// <inheritdoc />
        public async Task SendMessageAsync(IDomsMessage message, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new DomsConnectionException("Not connected to DOMs controller");

            await _sendSemaphore.WaitAsync(cancellationToken);
            try
            {
                var messageJson = JsonConvert.SerializeObject(message);
                var messageBytes = Encoding.UTF8.GetBytes(messageJson + "\n");

                _logger.LogDebug($"Sending message: {messageJson}");

                await _stream.WriteAsync(messageBytes, 0, messageBytes.Length, cancellationToken);
                await _stream.FlushAsync(cancellationToken);
            }
            finally
            {
                _sendSemaphore.Release();
            }
        }

        private async Task ListenForMessages(CancellationToken cancellationToken)
        {
            var buffer = new byte[4096];
            
            while (IsConnected && !cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        var messageJson = Encoding.UTF8.GetString(buffer, 0, bytesRead).Trim();
                        OnMessageReceived(messageJson);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error listening for messages: {ex.Message}");
                    break;
                }
            }
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            ConnectionStatusChanged?.Invoke(this, new ConnectionStatusChangedEventArgs(isConnected));
        }

        private void OnMessageReceived(string messageJson)
        {
            MessageReceived?.Invoke(this, new MessageReceivedEventArgs(messageJson));
        }

        /// <inheritdoc />
        public void Dispose()
        {
            if (!_disposed)
            {
                DisconnectAsync().Wait(5000);
                _sendSemaphore?.Dispose();
                _disposed = true;
            }
        }
    }
}
