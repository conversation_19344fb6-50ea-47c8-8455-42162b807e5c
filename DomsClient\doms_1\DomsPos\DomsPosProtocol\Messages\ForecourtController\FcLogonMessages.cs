﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Messages.ForecourtController
{
    // FC Logon Messages
    public class FcLogonRequest : JplRequest
    {
        public class FcLogonData
        {
            [JsonPropertyName("FcAccessCode")]
            public string FcAccessCode { get; set; }

            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("PosVersionId")]
            public string PosVersionId { get; set; }

            [JsonPropertyName("FcLogonPars")]
            public FcLogonParameters FcLogonPars { get; set; }
        }

        public class FcLogonParameters
        {
            [JsonPropertyName("UnsolMsgList")]
            public List<UnsolicitedMessage> UnsolMsgList { get; set; }
        }

        public class UnsolicitedMessage
        {
            [JsonPropertyName("ExtMsgCode")]
            public string ExtMsgCode { get; set; }

            [JsonPropertyName("MsgSubc")]
            public string MsgSubc { get; set; }
        }
    }

    public class FcLogonResponse : JplResponse
    {
        public class FcLogonResponseData
        {
            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("FcHwType")]
            public int FcHwType { get; set; }

            [JsonPropertyName("FcHwVersionNo")]
            public string FcHwVersionNo { get; set; }

            [JsonPropertyName("FcSwType")]
            public int FcSwType { get; set; }

            [JsonPropertyName("FcSwVersionNo")]
            public string FcSwVersionNo { get; set; }

            [JsonPropertyName("FcSwDate")]
            public string FcSwDate { get; set; }

            [JsonPropertyName("FcSwBlocks")]
            public List<FcSwBlock> FcSwBlocks { get; set; }

            [JsonPropertyName("UnsolMessages")]
            public List<FcLogonRequest.UnsolicitedMessage> UnsolMessages { get; set; }
        }

        public class FcSwBlock
        {
            [JsonPropertyName("FcSwMainBlockId")]
            public string FcSwMainBlockId { get; set; }

            [JsonPropertyName("FcSwSubBlockId")]
            public string FcSwSubBlockId { get; set; }

            [JsonPropertyName("FcSwBlockReleaseNo")]
            public string FcSwBlockReleaseNo { get; set; }

            [JsonPropertyName("FcSwBlockCheckCode")]
            public string FcSwBlockCheckCode { get; set; }
        }
    }
}
