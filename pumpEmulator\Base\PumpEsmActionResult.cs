﻿using System;

namespace Triquestra.Common.PumpEsm.Base
{
    /// <summary>
    /// Pump Esm result
    /// </summary>
    public class PumpEsmActionResult
    {
        public bool Result { get; set; }
        public virtual string ResultText { get; set; }

        public PumpEsmActionResult()
        { }
        public PumpEsmActionResult(bool result, string replyString)
        {
            this.ResultText = replyString;
            this.Result = result;
        }
    }

}
