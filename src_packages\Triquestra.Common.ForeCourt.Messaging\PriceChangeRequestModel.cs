﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public class PriceChangeRequestModel
    {
        public PriceChangeRequestModel() { }
        public PriceChangeRequestModel(int blendId,int priceId,decimal targetPrice) 
        {
            BlendID = blendId;
            PriceID = priceId;
            TargetPrice = targetPrice;
        }
        public int BlendID { get; set; }
        public int PriceID { get; set; }
        public decimal TargetPrice { get; set; }
    }
}
