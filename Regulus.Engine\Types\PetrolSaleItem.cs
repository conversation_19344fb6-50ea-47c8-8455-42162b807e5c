﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Types
{
    [Serializable]
    public class PetrolSaleItem//: IDelivery
    {
        public int SaleID { get; set; }
        public string UPC { get; private set; }
        public bool IsProcessed { get; set; }
        #region IDelivery interface
        public int DeliveryId { get; private set; }
        public int DispenserId { get; private set; }
        public int NozzleId { get; private set; }
        public int BlendId { get; private set; }
        public int TankId { get; private set; }
        public AuthModes Mode { get; private set; }
        public DeliveryStates State { get; private set; }
        public LimitTypes LimitType { get; private set; }
        public decimal Limit { get; private set; }
        public decimal Volume { get; private set; }
        public decimal Amount { get; private set; }
        public decimal Price { get; private set; }
        public int PriceId { get; private set; }
        public int PriceLevel { get; private set; }
        public DateTime Timestamp { get; private set; }
        public bool IsCurrent { get; private set; }
        #endregion
        /// <summary>
        /// Unique identification
        /// </summary>
        public int Handle { get; set; }

        public PetrolSaleItem(string upc, IDelivery delivery) : this(0, delivery) { UPC = upc; }
        protected PetrolSaleItem(int saleId,IDelivery delivery)
        {
            SaleID = saleId;
            DeliveryId = delivery.DeliveryId;
            Handle = delivery.DeliveryId;
            DispenserId = delivery.DispenserId;
            NozzleId = delivery.NozzleId;
            BlendId = delivery.BlendId;
            TankId = delivery.TankId;
            Mode = delivery.Mode;
            State = delivery.State;
            LimitType = delivery.LimitType;
            Limit = delivery.Limit;
            Volume = delivery.Volume;
            Amount = delivery.Amount;
            Price = delivery.Price;
            PriceId = delivery.PriceId;
            PriceLevel = delivery.PriceLevel;
            Timestamp = delivery.Timestamp;
            IsCurrent = delivery.IsCurrent;
        }

        public string GetSaleItemString()
        {
            var outputStr = new StringBuilder();
            outputStr.Append("SaleID: " + SaleID + ", ");
            outputStr.Append("UPC: " + UPC + ", ");
            outputStr.Append("IsProcessed: " + (IsProcessed ? "true" : "false") + ", ");
            outputStr.Append("DeliveryId: " + DeliveryId + ", ");
            outputStr.Append("DispenserId: " + DispenserId + ", ");
            outputStr.Append("NozzleId: " + NozzleId + ", ");
            outputStr.Append("BlendId: " + BlendId + ", ");
            outputStr.Append("TankId: " + TankId + ", ");
            outputStr.Append("Mode: " + Mode.ToString() + ", ");
            outputStr.Append("State: " + State.ToString() + ", ");
            outputStr.Append("LimitType: " + LimitType.ToString() + ", ");
            outputStr.Append("Limit: " + Limit + ", ");
            outputStr.Append("Volume: " + Volume + ", ");
            outputStr.Append("Price: " + Price + ", ");
            outputStr.Append("PriceId: " + PriceId + ", ");
            outputStr.Append("PriceLevel: " + PriceLevel + ", ");
            outputStr.Append("Timestamp: " + Timestamp.ToLongTimeString() + ", ");
            outputStr.Append("IsCurrent: " + (IsCurrent ? "true" : "false"));

            return outputStr.ToString();
        }
    }
}
