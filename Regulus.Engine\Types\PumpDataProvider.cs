﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Triquestra.Common.Database;
using Triquestra.Common.PumpEsm.DataAccess;
using System.Data.SqlClient;

namespace Triquestra.Common.PumpEsm.Types
{
    class PumpDataProvider : IPumpDataProvider
    {
        public const string PrepayBlocked_Param = "PrepayBlocked";
        public const string PREPAY = "prepay";

        private const ushort DEFAULT_REGULUS_UDP_PORT = 8051;
        private const ushort DEFAULT_REGULUS_TCP_PORT = 6050;
        private static readonly string PREPAY_DENOMINATIONS = "PrepayDenominations";
        private static readonly string PREPAY_DENOMINATIONS_DEFAULT = "5,10,20,30,50,80,100";

        NLog.Logger _log = NLog.LogManager.GetCurrentClassLogger();
        private IDbConnection _dbConn;
        private Dictionary<int, string> _itemDictionary;
        private Dictionary<string, Fuel_Config> _configOptions;
        private string _tcpHost = "*************";
        private int _tcpPort = 6050;
        private string _udpHost = "**********";
        private int _udpPort = 8051;
        private int _workstationId = 8;
        private HashSet<int> _validPumps;
        private List<Fuel_Pump> _dispensers;
        private List<Fuel_Hose> _nozzles;
        private List<Fuel_Tank> _tanks;

        private bool _isPrepayBlocked;
        private string _customTextPrepay;

        public PumpDataProvider(IDbConnection dbConn)
        {
            // assume the data is static => we can cache it.
            _dbConn = dbConn;
            //by AC: we need to investigate this case 
            //if (dbConn.State != ConnectionState.Open)
            //    dbConn.Open();

            _itemDictionary = new Dictionary<int, string>();
            _workstationId = Triquestra.Common.PumpEsm.Base.Main.Station;

            List<Items> petrolItemList;
            if (dbConn.State != ConnectionState.Open)
                dbConn.Open();
            using (var dbCtx = new AKPOSDataContext(dbConn))
            {
                petrolItemList = dbCtx.Items.Where(w => w.ItemType == 'P' && !w.InActive).ToList();
                var cd2Title = dbCtx.Config.SingleOrDefault(c => c.ID == 137);
                _digifortTitle = (cd2Title != null) ? cd2Title.Value : string.Empty;

            }

            using (var dbCtx = new PumpDataContext(dbConn))
            {
                _configOptions = dbCtx.Fuel_Configs.ToDictionary(lp => lp.Parameter, StringComparer.OrdinalIgnoreCase);
                //_pumpConfig = dbCtx.Fuel_Pumps.ToList();
                _dispensers = dbCtx.Fuel_Pumps.ToList();
                _nozzles = dbCtx.Fuel_Hoses.ToList();
                _tanks = dbCtx.Fuel_Tanks.ToList();
                _validPumps = new HashSet<int>(_dispensers.Where(w => w.CategoryType == "1").Select(s => s.ID));

            }

            CheckDenominationsConfigExists(dbConn);
            PopulateDenominations();

            var val = GetPumpConfig("FuelControllerIPAddress", 3);
            var parts = (val ?? string.Empty).Split(',');
            var tcpPart = parts.Length > 0 ? parts[0].Trim() : string.Empty;
            var udpPart = parts.Length > 1 ? parts[1].Trim() : string.Empty;

            ParseAddressAndPort(tcpPart, DEFAULT_REGULUS_TCP_PORT, out _tcpHost, out _tcpPort);
            ParseAddressAndPort(udpPart, DEFAULT_REGULUS_UDP_PORT, out _udpHost, out _udpPort);

            foreach (var item in petrolItemList)
            {
                int blendId;
                if (int.TryParse(item.SKU, out blendId))
                {
                    _itemDictionary[blendId] = item.UPC;
                }
                else
                    _log.Warn("Unexpected petrol item: ({0}){1}. The SKU {2} is not a valid BlendID", item.UPC, item.Description, item.SKU);
            }
        }

        private void PopulateDenominations()
        {
            Fuel_Config denominations;
            string str;
            if (_configOptions.TryGetValue(PREPAY_DENOMINATIONS, out denominations))
            {
                str = denominations.Value;
            }
            else
            {
                str = PREPAY_DENOMINATIONS_DEFAULT;
            }
            _denominations = new List<int>(str.Split(',').Select(s => int.Parse(s)));
        }

        private List<int> _denominations;
        private string _digifortTitle;

        public List<int> Denominations
        {
            get
            {
                return _denominations;
            }
        }

        private void CheckDenominationsConfigExists(IDbConnection conn)
        {
            Fuel_Config fuelDenominations = new Fuel_Config()
            {
                Parameter = PREPAY_DENOMINATIONS,
                GroupID = 200,
                Value = " 5,10,20,30,50,80,100",
                Description = "Denominations for quick select pre pay amounts",
                ValueNotes = null,
                BVisible = true,
            };
            CheckConfigExists(conn, fuelDenominations);
        }

        private void CheckConfigExists(IDbConnection conn, Fuel_Config fuelConfig)
        {
            if (!_configOptions.ContainsKey(fuelConfig.Parameter))
            {
                CreateConfigValue(conn, fuelConfig);
            }
        }

        private void CreateConfigValue(IDbConnection conn, Fuel_Config fuelConfig)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            using (var pumpCtx = new PumpDataContext(conn))
            {
                pumpCtx.Fuel_Configs.InsertOnSubmit(fuelConfig);
                pumpCtx.SubmitChanges();
                _configOptions = pumpCtx.Fuel_Configs.ToDictionary(lp => lp.Parameter, StringComparer.OrdinalIgnoreCase);
            }
        }

        private void ParseAddressAndPort(string val, ushort defaultPort, out string ipaddr, out int port)
        {
            if (!string.IsNullOrEmpty(val))
            {
                var idx = val.IndexOf(':');

                if (idx < 0)
                {
                    ipaddr = val.Trim();
                    port = defaultPort;
                }
                else
                {
                    ipaddr = ((idx > 0) ? val.Substring(0, idx) : string.Empty).Trim();
                    var portStr = ((idx < val.Length - 1) ? val.Substring(idx + 1) : string.Empty).Trim();
                    if (!int.TryParse(portStr, out port))
                        port = defaultPort;
                }
            }
            else
            {
                ipaddr = "127.0.0.1";
                port = defaultPort;
            }
        }

        public decimal GetTaxRate(int taxId)
        {
            using (var dbCtx = new AKPOSDataContext(_dbConn))
            {
                var tax = dbCtx.Taxes.SingleOrDefault(w => w.ID == taxId);
                if (tax != null)
                    return tax.Rate ?? 0m;
                else
                    return 0m;
            }
        }

        public string GetUPCforBlendId(int blendId)
        {
            string item;
            if (_itemDictionary.TryGetValue(blendId, out item))
                return item;
            else
                return null;
        }

        public string GetPumpConfig(string param, int group)
        {
            Fuel_Config val;
            if (_configOptions.TryGetValue(param, out val) /*&& val.GroupID == group*/)
                return val.Value;
            else
                return null;
        }

        public void ReportPumpSuspendAction(bool isSuspend, int pumpNo)
        {
            const int EVT_SUSPENDRESUME = 30148;
            if (_dbConn.State != ConnectionState.Open)
                _dbConn.Open();

            string pumpInfo = string.Format("{0} {1}",
                    isSuspend ? "Stop" : "Play",
                    (pumpNo > 0) ? string.Format("Pump {0:00}", pumpNo) : "All");

            using (var cmd = _dbConn.CreateCommand())
            {
                cmd.CommandText = @"
INSERT INTO [dbo].[EventLog]
           ([Operation]
           ,[Logged]
           ,[TheUser]
           ,[Station]
           ,[Branch]
           ,[TransNo]
           ,[CurrTransTotal]
           ,[AuthUser]
           ,[ParameterStr]
           ,[ParameterInt]
           ,[ParameterDec1]
           ,[ParameterDec2])
     VALUES
           (@Operation,
           GetDate(),
           '',
           @Station, 
           @Branch, 
           0,
           0,
           '',
           @Info,
           @PumpNo,
           @Susp,
           0)";
                cmd.Parameters.Add(new SqlParameter("@Operation", EVT_SUSPENDRESUME));
                cmd.Parameters.Add(new SqlParameter("@Branch", Triquestra.Common.Global.Database.Branch));
                cmd.Parameters.Add(new SqlParameter("@Station", WorkstationId));
                cmd.Parameters.Add(new SqlParameter("@Info", pumpInfo));
                cmd.Parameters.Add(new SqlParameter("@PumpNo", pumpNo));
                cmd.Parameters.Add(new SqlParameter("@Susp", isSuspend ? 1 : 2));
                cmd.ExecuteNonQuery();
            }
        }

        public Triquestra.Common.Database.Items GetItemByBlendId(int blendId)
        {
            string item;
            if (_itemDictionary.TryGetValue(blendId, out item))
            {
                if (_dbConn.State != ConnectionState.Open)
                    _dbConn.Open();
                using (var dbCtx = new AKPOSDataContext(_dbConn))
                {
                    var item2 = dbCtx.Items.SingleOrDefault(w => w.ItemType == 'P' && !w.InActive && w.UPC == item);
                    return item2;
                }
            }
            else
                return null;
        }

        public bool PumpAvailable(int pumpId)
        {
            return _validPumps.Contains(pumpId);
        }

        public decimal GetConsigmentComission(string upc)
        {
            if (_dbConn.State != ConnectionState.Open)
                _dbConn.Open();

            // if the consigment is yes - return the commission
            var sql = @"declare @fee money;
declare @consigment varchar(20);
declare @branch int;
select @branch = cast(Value as int) from S_This where Parameter = 'Branch';
select @fee = 0;
select @consigment = val.Value from Items i outer apply dbo.GetExtendedData('Items',i.UPC,0,'Consignment',@branch) as val
	where i.InActive = 0 and i.UPC = @upc
if @consigment = 'yes'
begin
select @fee = cast(val.Value as money) from Items i outer apply dbo.GetExtendedData('Items',i.UPC,0,'Commission',@branch) as val
	where i.InActive = 0 and i.UPC = @upc and ISNUMERIC(val.Value) = 1
end;
select isnull(@fee,0) as value";
            using (var cmd = _dbConn.CreateCommand())
            {
                cmd.CommandText = sql;
                cmd.Parameters.Add(new SqlParameter("@upc", upc));
                var res = Convert.ToDecimal(cmd.ExecuteScalar());
                return res;
            }
        }

        public bool IsPrepayBlocked()
        {
            string _isPrepayBlocked = string.Empty;
            using (var dbCtx = new AKPOSDataContext(_dbConn))
            {
                var res = dbCtx.InterfaceData.Where(d => d.Parameter.Equals(PrepayBlocked_Param)).FirstOrDefault();
                if (res != null)
                    _isPrepayBlocked = res.Value;
            }
            return !string.IsNullOrEmpty(_isPrepayBlocked);
        }

        public string GetCustomTextPrepay()
        {
            string _customTextPrepay = string.Empty;
            using (var dbCtx = new AKPOSDataContext(_dbConn))
            {
                _customTextPrepay = dbCtx.Translate.Where(t => t.Standard.Equals(PREPAY)).FirstOrDefault().Custom;
            }
            return _customTextPrepay;
        }

        public HashSet<int> RetailPumps { get { return _validPumps; } }
        public IEnumerable<Fuel_Tank> Tanks { get { return _tanks; } }
        public IEnumerable<Fuel_Pump> Dispensers { get { return _dispensers; } }
        public IEnumerable<Fuel_Hose> Nozzles { get { return _nozzles; } }
        public int WorkstationId { get { return _workstationId; } }
        public string TcpAddress { get { return _tcpHost; } }
        public string UdpAddress { get { return _udpHost; } }
        public int TcpPort { get { return _tcpPort; } }
        public int UdpPort { get { return _udpPort; } }


        public string DigifortTitle
        {
            get { return _digifortTitle; }
        }
    }

    public class FuelConfig
    {
        public string Parameter { get; set; }
        public int GroupID { get; set; }
        public string Value { get; set; }
        public string Description { get; set; }
        public string ValueNotes { get; set; }
        public bool BVisible { get; set; }
    }
}
