﻿using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Tests
{
    [TestClass]
    public class MessageSerializationTests
    {
        [TestMethod]
        public void FcLogonRequest_Serialization_ShouldMatch()
        {
            // Arrange
            var request = new FcLogonRequest
            {
                Name = JplConstants.MessageNames.FC_LOGON_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcLogonRequest.FcLogonData
                {
                    FcAccessCode = "POS,APPL_ID=TEST,RI",
                    CountryCode = "0000",
                    PosVersionId = "TEST_1.0"
                }
            };
            
            // Act
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
            var json = JsonSerializer.Serialize(request, options);
            
            // Assert
            Assert.IsTrue(json.Contains("FcLogon_req"));
            Assert.IsTrue(json.Contains("POS,APPL_ID=TEST,RI"));
            Assert.IsTrue(json.Contains("TEST_1.0"));
        }
        
        [TestMethod]
        public void FcDateTimeResponse_Deserialization_ShouldWork()
        {
            // Arrange
            string json = @"{
                ""name"": ""FcDateAndTime_resp"",
                ""subCode"": ""00H"",
                ""solicited"": true,
                ""data"": {
                    ""FcDateAndTime"": ""20230215123456"",
                    ""LastDateAndTimeSetting"": ""20230215000000""
                }
            }";
            
            // Act
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var response = JsonSerializer.Deserialize<FcDateTimeResponse>(json, options);
            
            // Assert
            Assert.IsNotNull(response);
            Assert.AreEqual(JplConstants.MessageNames.FC_DATE_TIME_RESP, response.Name);
            Assert.AreEqual(JplConstants.SubCodes.SUBC_00H, response.SubCode);
            Assert.IsTrue(response.Solicited);
            Assert.IsNotNull(response.Data);
        }
    }
}
