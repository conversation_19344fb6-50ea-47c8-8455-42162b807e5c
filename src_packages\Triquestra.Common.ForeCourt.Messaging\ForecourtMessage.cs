﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;
using Triquestra.Common.PumpEsm.Messaging.Event;
using Triquestra.Common.PumpEsm.Messaging.Response;
using Triquestra.Common.PumpEsm.Messaging.SystemMessages;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public class ForecourtMessage:IForecourtMessage
    {
        public int SequenceNo { get; set; }

        public ForecourtMessageClasses MessageClass { get; private set; }

        public ForecourtMessage(ForecourtMessageClasses messageClass, int sequenceNo)
        {
            SequenceNo = sequenceNo;
            MessageClass = messageClass;
        }

        public virtual XDocument Serialise()
        {
            var xdoc = new XDocument(
                new XElement("FCCMessage",
                    new XElement("Header",
                        new XAttribute("MessageType", MessageClass),
                        new XAttribute("SeqNo", SequenceNo)
                        )
                    )
                );

            return xdoc;
        }


        public static ForecourtMessage Deserialise(string message)
        {
            var xdoc = XDocument.Parse(message);
            return Deserialise(xdoc);
        }


        public static ForecourtMessage Deserialise(XDocument message)
        {
            var messageNode = message.Element("FCCMessage");
            if (messageNode == null)
            {
                throw new XmlSchemaException("XDocument message does not have <FCCMessage> node ");
            }
            var headerNode = messageNode.Element("Header");
            if (headerNode == null)
            {
                throw new XmlSchemaException("<FCCMessage> does not have <Header> node ");
            }

            var messageTypeNode = headerNode.Attribute("MessageType");

            if (messageTypeNode == null)
            {
                throw new XmlSchemaException("<Header> does not have <MessageType> attribute ");
            }

            var seqNo = 0;


            if (headerNode.Attribute("SeqNo") != null)
            {
                int.TryParse(headerNode.Attribute("SeqNo").Value, out seqNo);
            }

            var targetId = 0;
            var sourceId = 0;

            if (headerNode.Attribute("SourceID") != null)
            {
                int.TryParse(headerNode.Attribute("SourceID").Value, out sourceId);
            }

            if (headerNode.Attribute("TargetID") != null)
            {
                int.TryParse(headerNode.Attribute("TargetID").Value, out targetId);
            }

            ForecourtMessageClasses mc;

            mc = (ForecourtMessageClasses)Enum.Parse(typeof(ForecourtMessageClasses),messageTypeNode.Value,true);
            switch (mc)
            {
                case ForecourtMessageClasses.COMMAND_RESPONSE:
                    return CommandResponse.Deserialise(messageNode, seqNo, sourceId, targetId);

                case ForecourtMessageClasses.EVENT:
                    return ForecourtEventMessage.Deserialise(messageNode, seqNo, sourceId, targetId);

                case ForecourtMessageClasses.CONFIG_RESPONSE:
                    return ConfigResponse.Deserialise(messageNode);

                case ForecourtMessageClasses.SYSMGT_RESPONSE:
                    return ForecourtSystemResponse.Deserialise(messageNode, seqNo);

            }

            return new ForecourtControlMessage(seqNo, sourceId, targetId, mc);
        }

        public override string ToString()
        {
            return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                   Serialise().ToString().Replace(Environment.NewLine, "");
        }

        public void Validate()
        {
            if (this is CommandResponse)
            {
                var forecourtCommandResponse = this as CommandResponse;
                if (forecourtCommandResponse != null && forecourtCommandResponse.CommandResult != ForecourtCommandResults.SUCCESS)
                {
                    throw new Exception("Forecourt Message Failed:" + forecourtCommandResponse.CommandResult + Environment.NewLine +
                    forecourtCommandResponse);
                }
            }
        }
    }
}
