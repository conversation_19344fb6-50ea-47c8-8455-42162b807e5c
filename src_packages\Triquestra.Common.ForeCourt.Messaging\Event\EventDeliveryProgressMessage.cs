﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryProgressMessage:ForecourtEventMessage
    {
        public int DeliveryId { get; set; }
        public int NozzleId { get; set; }
        public int TankId { get; set; }
        public decimal Amount { get; set; }
        public decimal Volume { get; set; }

        public EventDeliveryProgressMessage(int sequenceNo, int sourceId, int targetId,  int deliveryId, 
            int nozzleId, int tankId, decimal amount, decimal volume) : 
            base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_PROGRESS)
        {
            DeliveryId = deliveryId;
            NozzleId = nozzleId;
            TankId = tankId;
            Amount = amount;
            Volume = volume;
        }

        public static EventDeliveryProgressMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryProgressNode = eventNode.Element("DeliveryProgress");

            if (deliveryProgressNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryProgress> node");
            }
            var deliveryId = int.Parse(deliveryProgressNode.Attribute("DeliveryID").Value);
            var nozzleId = int.Parse(deliveryProgressNode.Attribute("NozzleID").Value);
            var tankId = int.Parse(deliveryProgressNode.Attribute("TankID").Value);
            var amount = decimal.Parse(deliveryProgressNode.Attribute("Amount").Value);
            var volume = decimal.Parse(deliveryProgressNode.Attribute("Volume").Value);
            return new EventDeliveryProgressMessage(seqNo, sourceId, targetId, deliveryId, nozzleId,
                tankId, amount, volume);
        }
    }
}
