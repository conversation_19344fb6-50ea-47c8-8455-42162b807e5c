﻿using System;
using System.Xml;

namespace Triquestra.Common.PumpEsm.Base
{
    public class PumpEsmNotifyRequest: PumpEsmRequest
    {
        /// <summary>
        /// Creates an instance of the object.
        /// </summary>
        /// <param name="requestDoc">The request in XML format.</param>
        public PumpEsmNotifyRequest(XmlDocument requestDoc)
            :base(requestDoc)
        {
            ParseSaleNotify();
        }

        internal void ParseSaleNotify()
        {
            XmlNode msgNode = request.SelectSingleNode("action");
            if (null == msgNode)
                return;
#if DEBUG
            if (msgNode.InnerText != "notify")
                throw new ArgumentException("The xml is not a notify-type");
#endif
            string s = Message;
            string sId = s.Substring(2, s.Length - 2);

            int i = sId.IndexOf(',');
            int iD;
            int transID;
            if (i >= 0)
            {
                if (int.TryParse(sId.Substring(0, i), out iD))
                    SaleID = iD;
                if (int.TryParse(sId.Substring(i + 1), out transID))
                    TransactionID = transID;                                
            }
            else
            {
                if (int.TryParse(sId, out iD))
                    SaleID = iD;
                transID = 0;
            }
        }

        /// <summary>
        /// It is SaleNo for <see cref="Triquestra.Common.PumpEsm.Base.PumpEsmBase.OnSaleVoided"></see> and <see cref="Triquestra.Common.PumpEsm.Base.PumpEsmBase.OnSaleCompleted">OnSaleCompleted</see> and PumpHandle in <see cref="Triquestra.Common.PumpEsm.Base.PumpEsmBase.OnItemVoided">OnItemVoided</see>
        /// </summary>
        public int? SaleID { get; private set; }
        /// <summary>
        /// This is TransactionID
        /// </summary>
        public  int? TransactionID { get; private set; }

        public PumpNotifications NotifyAction
        {
            get
            {
                string s = Message;
                if (!string.IsNullOrEmpty(s))
                    return (PumpNotifications)s[0];
                else
                    return PumpNotifications.Unknown;
            }
        }
    }
}
