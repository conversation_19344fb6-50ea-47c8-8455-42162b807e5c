﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using RGiesecke.DllExport;
using System.Xml;
using System.Text;
using System.Data.Common;
using Triquestra.Common.PumpEsm.Base.Helpers;
using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Base
{
    /// <summary>
    /// This class contains the entry-point for core to call in with requests.
    /// </summary>
    public static class Main 
    {
        #region private declaration
        /// <summary>
        /// The file name of the current module
        /// </summary>
        const string DLL_ME = "pumpesmbase.dll";
        /// <summary>
        /// List of Dll's to skip from examination
        /// </summary>
        static readonly string[] DLLNAMESTOSKIP = { DLL_ME };

        static NLog.Logger _logger = NLog.LogManager.GetCurrentClassLogger();
        /// <summary>
        /// Maximum ESM reply length (not used here)
        /// </summary>
        const int MAX_REPLY_LENGTH = 1024 * 4;
        /// <summary>
        /// Maximum buffer length
        /// </summary>
        private const int PARAMETER_BUFFER_LENGTH = 1024 * 3 + 128;
        #endregion

        public static int Station;
        public static PumpEsmBase PumpEsm = null;
        
        /// <summary>
        /// Load and link PumpEsm assembly
        /// </summary>
        static void Bootstrap()
        {
            _logger.Trace("Entering PumpEsmBase.Bootstrap");
            
            // do this after runonce, to avoid loading assemblies that runonce wants to delete.
            FileInfo assemblyFolder = new FileInfo(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
            
            string filename = Path.Combine(assemblyFolder.DirectoryName, "posrun.ini");
            _logger.Debug("Reading " + filename);
            Station = Native.GetPrivateProfileInt("StartUp", "StationID", -1, filename);
            _logger.Debug("Station number: " + Station);
            
            CreatePumpEsmInstance(typeof(Main).Assembly);
            //PumpEsmLookup(assemblyFolder);
            _logger.Trace("Leaving PumpEsmBase.Bootstrap");
        }
        
#if false
        // left here for a reference purpose
        private static void PumpEsmLookup(FileInfo assemblyFolder)
        {
            DirectoryInfo di = assemblyFolder.Directory;

            FileInfo[] files = di.GetFiles("pump*.dll", SearchOption.AllDirectories); // Changed to search directories.  CR12597

            string esmFileName = null;
            HashSet<string> listToSkip = new HashSet<string>(DLLNAMESTOSKIP, StringComparer.OrdinalIgnoreCase);

            //grab the first file, which is not in DLLNAMESTOSKIP   
            foreach (FileInfo file in files)
            {
                if (!listToSkip.Contains(file.Name))
                {
                    esmFileName = file.FullName;
                    break;
                }
            }

            if (!string.IsNullOrEmpty(esmFileName))
            {
                Assembly esmAssembly = Assembly.LoadFrom(esmFileName);

                // find an PumpEsmBase and save it
                CreatePumpEsmInstance(esmAssembly);
            }
            else
            {
                _logger.Debug("No pumpesm services found!");
            }
        }

        private static void CreatePumpEsmInstance(Assembly esmAssembly)
        {
            Type[] types = esmAssembly.GetTypes();
            foreach (Type t in types)
            {
                if (t.IsClass && t.IsSubclassOf(typeof(PumpEsmBase)) && !t.IsAbstract)
                {
                    PumpEsm = esmAssembly.CreateInstance(t.FullName) as PumpEsmBase;
                    _logger.Debug(t.FullName + " found and " + ((null == PumpEsm) ? "NOT" : string.Empty) + " loaded");
                    break;
                }
            }
        }
#else
        private static void CreatePumpEsmInstance(Assembly esmAssembly)
        {
            PumpEsm = new Triquestra.Common.PumpEsm.PumpEmulatorEsm();
        }
#endif
        /// <summary>
        /// Execute PumpEsm action
        /// </summary>
        /// <param name="request">XML string</param>
        /// <returns>XML string</returns>
        public static void DoESMAction(string request, out bool res,out string retval)
        {
            retval = string.Empty;
            res = false;
            try
            {
                if (PumpEsm != null)
                {
                    _logger.Trace("DoESMAction: {0}",request);
                    PumpEsmActionResult result = PumpEsm.PerformESMAction(request);
                    if (result != null)
                    {
                        res = result.Result;
                        retval = result.ResultText;
                    }
                                        
                }
                else
                {
                    _logger.Error(@"Attempting to call pumpesm assembly, but it hasn't been loaded");
                }

            }
            catch (Exception ex)
            {
                retval = string.Empty;
                res = false;
            }
            _logger.Trace("Aquired reply: {0}-{1}",res,retval);
        }

        static Main()
        {
            _logger.Trace(@"Entered static constructor.");
            try
            {
                try
                {
                    Bootstrap();
                }
                catch (Exception ex)
                {
                    _logger.Error("Bootstrap failed.", ex);
                }
            }
            finally 
            { 
                _logger.Trace("Leaving static constructor."); 
            }
        }

        private static StringBuilder BodgeyFixTagContent(StringBuilder aString, string TagName) 
        {
// B9245: "fix" the content of a given tag, using string replace.
// Core doesn't xml-encode entities in the xml it passes (in fact, it decodes
// entities! If you put &amp; in your store name, you'll get & passed in the xml).
// Because it doesn't encode entities, you could end up with something like:
// <storename>Super <3 Heart</storename>
// which we cannot parse as XML, because of the '<'
// So we fix it using string search/replace.
// This function finds the first instance of the given tag, and "fixes" it.
// It does nothing if the tag is not found.
            int i1;
            int i2;
            string openTag, closeTag;
            StringBuilder Result = aString;
            
            openTag = "<" + TagName + ">";
            closeTag = "</" + TagName + ">";
            i1 = Triquestra.Common.PumpEsm.Base.Helpers.StringBuilderHelper.IndexOf(aString, openTag);
            //i1 = aString.IndexOf(openTag);

            if (i1<0) return Result; 

            i1 += openTag.Length;
          //  i2 = aString.IndexOf(closeTag,i1);
            i2 = Triquestra.Common.PumpEsm.Base.Helpers.StringBuilderHelper.IndexOf(aString, closeTag, i1);


            if (i2<0) return Result; //however, it seems to be incorrect...

            int oldLen = i2 - i1;
            StringBuilder content = new StringBuilder(aString.ToString(i1, i2 - i1));

            content = content.Replace( "&", "&amp;").Replace( "<", "&lt;")
                .Replace( ">", "&gt;").Replace( "'", "&apos;")
                .Replace( "\"", "&quot;");

            if (content.Length != oldLen)
            {
                Result.Remove(i1, oldLen);
                Result.Insert(i1, content);
            }
            return Result;
        }

        private unsafe static bool InfDllActionInternal(char* buffer, IntPtr conn, IntPtr app)
        {            
            string request = new string(buffer);
            bool res = false;

#if DEBUG
            _logger.Info("Request: \"{0}\"", request);
#else
            _logger.Debug("Request: \"{0}\"", request);
#endif
            string reply = string.Empty;
            try
            {
                StringBuilder sXmlFromPos = new StringBuilder(request);

                //#region B9245: xml special characters from pos are not encoded
                //// Convert given string to appropriate XML
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "storename");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "dirhost");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "account");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "account2");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address1");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address2");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address3");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "city");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "state");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "postcode");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "phone1");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "taxnumber");
                //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "companynumber");
                //#endregion

                //Add opening and closing tags
                sXmlFromPos.Insert(0, "<pumpesmrequest>");
                sXmlFromPos.Append("</pumpesmrequest>");

                var dstXml = sXmlFromPos.ToString();
                string lActionNode = PseudoXmlHelper.GetXMLField(dstXml,"action");

                if (null != lActionNode)
                {
                    DoESMAction(dstXml, out res, out reply);


                    if (lActionNode == "deinit")
                    {
                        // finalisation
                        PumpEsm = null;
                        lActionNode = null;
                        GC.Collect();
                        reply = string.Empty;
                    }
                }
                else
                    reply = string.Empty;


                _logger.Debug("PumpESM Response: \"{0}\"", reply);

                if (reply.Length > PARAMETER_BUFFER_LENGTH)
                {
                    throw new ArgumentOutOfRangeException(string.Format(@"The ESM response length {0} is out of range. The response is '{1}'",reply.Length,reply));
                }

            }
            catch (Exception ex)
            {
                _logger.Error("Error processing request. Exception: {0}", ex.ToString());
                // todo: is this an appropriate return string? perhaps should be <reply action=\"ignore\"></reply>
                reply = string.Empty;
                res = false;
            }

            Marshal.Copy(reply.ToCharArray(), 0, (IntPtr)buffer, reply.Length);
            buffer[reply.Length] = '\0';
            return res;
        }

        [DllExport("InfDllActionStd", CallingConvention.StdCall)]
        public unsafe static bool InfDllActionStdCall(char* buffer)
        {
            _logger.Trace("Entered InfDllActionStd.");
            try
            {                                
                return InfDllActionInternal(buffer, IntPtr.Zero, IntPtr.Zero);
            }
            catch (Exception ex)
            {
                _logger.Error("Fatal exception in InfDllActionStd.", ex);
                return false;
            }
            finally
            {
                _logger.Trace("Exiting InfDllActionStd.");
            }

        }


       
    }
}
