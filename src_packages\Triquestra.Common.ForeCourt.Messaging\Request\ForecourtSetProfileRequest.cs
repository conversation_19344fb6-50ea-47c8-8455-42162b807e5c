﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class ForecourtSetProfileRequest : ForecourtConfigRequest
    {
        public int CurrentProfileIndex { get; set; }

        public ForecourtSetProfileRequest(int profileIndex)
            : base(ForecourtConfigurationTypes.SET_CONFIG)
        {
            CurrentProfileIndex = profileIndex;
        }

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            xdoc.Root.Add(new XElement("AuthModeProfiles",
                new XAttribute("Action", "UPDATE"),
                new XAttribute("CurrentProfileIndex", CurrentProfileIndex)));
            return xdoc;
        }
    }
}
