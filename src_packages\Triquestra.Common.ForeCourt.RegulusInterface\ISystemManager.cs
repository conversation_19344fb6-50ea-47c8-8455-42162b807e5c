﻿using System;
using System.Collections.Generic;
using Triquestra.Common.PumpEsm.Comms;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public interface ISystemManager:IForecourtConnection
    {
        void GetStatus(string applicationName);
        
        void StartApplication(string applicationName);
        
        void StopApplication(string applicationName);

        event EventHandler ApplicationStarted;

        event EventHandler ApplicationStopped;

        void ChangeFuelPrice(int blendId, int priceId, decimal newPrice);

        void ChangeFuelPrice(List<PriceChangeRequestModel> prices);
    }
}
