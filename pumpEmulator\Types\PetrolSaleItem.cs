﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Types
{
    public class PetrolSaleItem//: IDelivery
    {
        public int SaleID { get; set; }
        public bool IsProcessed { get; set; }
        #region IDelivery interface
        public int DeliveryId { get;  set; }
        public int DispenserId { get;  set; }
        public int NozzleId { get;  set; }
        public int BlendId { get;  set; }

        public AuthModes Mode { get;  set; }


        public decimal Limit { get;  set; }
        public decimal Volume { get;  set; }
        public decimal Amount { get;  set; }
        public decimal Price { get;  set; }

        #endregion
        
        
    }
}
