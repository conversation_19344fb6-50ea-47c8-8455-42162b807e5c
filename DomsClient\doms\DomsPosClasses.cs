using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace JPL_Demo_POS_CSharp
{
    #region Shared Types

    public class BitFlagType<T>
    {
        public int value { get; set; }
        public T bits { get; set; }
    }

    public class EnumType<T>
    {
        public string value { get; set; }
        [JsonProperty("enum")]
        public T @enum { get; set; }

        public EnumType()
        {
        }

        public EnumType(string value)
        {
            this.value = value;
        }
    }

    public class DomsPosBaseType
    {
        public string name { get; set; }
        public string subCode { get; set; }

        public DomsPosBaseType()
        {
        }

        public DomsPosBaseType(string name, string subCode)
        {
            this.name = name;
            this.subCode = subCode;
        }
    }

    public class DomsPosResponseType : DomsPosBaseType
    {
        public bool solicited { get; set; }
    }

    #endregion

    #region RejectMessage

    public class RejectMessageRespType : DomsPosResponseType
    {
        public class RejectCodeType
        {
            public string unknown_MsgCode { get; set; }
            public string syntax_error { get; set; }
            public string access_error { get; set; }
        }

        public class RejectMessageRespDataType
        {
            public string RejectedMsgCode { get; set; }
            public string RejectedExtendedMsgCode { get; set; }
            public string RejectedMsgSubc { get; set; }
            public EnumType<RejectCodeType> RejectCode { get; set; }
            public string RejectInfo { get; set; }
            public string RejectInfoText { get; set; }
        }

        public RejectMessageRespDataType data { get; set; }
    }

    #endregion

    #region MultiMessage

    public class DomsPosResponseWithData : DomsPosResponseType
    {
        public object data { get; set; }
    }

    public class MultiMessageRespType : DomsPosResponseType
    {
        public class MultiMessageDataType
        {
            public List<DomsPosResponseWithData> messages { get; set; }
        }

        public MultiMessageDataType data { get; set; }
    }

    #endregion

    #region Heartbeat

    public class HearBeatRespType : DomsPosBaseType
    {
        public object data { get; set; } = new object();

        public HearBeatRespType() : base("heartbeat", "00H")
        {
        }
    }

    #endregion

    #region Passthrough

    public class PassthroughReqType : DomsPosBaseType
    {
        public class PassthroughReqDataType
        {
            public string dppMsg { get; set; }
        }

        public PassthroughReqDataType data { get; set; }

        public PassthroughReqType(byte[] dppMsg) : base("passthrough_req", "00H")
        {
            this.data = new PassthroughReqDataType();
            this.data.dppMsg = Convert.ToBase64String(dppMsg);
        }
    }

    public class PassThroughRespType : DomsPosResponseType
    {
        public class PassThroughRespDataType
        {
            public string dppMsg { get; set; }
        }

        public PassThroughRespDataType data { get; set; }

        public byte[] ToByteArray()
        {
            if (this.data != null && !string.IsNullOrEmpty(this.data.dppMsg))
            {
                return Convert.FromBase64String(this.data.dppMsg);
            }
            else
            {
                return null;
            }
        }
    }

    #endregion

    #region BackOfficeRecords

    public class BackOfficeRecordReqType : DomsPosBaseType
    {
        public object data { get; set; } = new object();

        public BackOfficeRecordReqType() : base("BackOfficeRecord_req", "02H")
        {
        }
    }

    public class BackOfficeRecordRespType : DomsPosResponseType
    {
        public class BackOfficeRecordRespDataType
        {
            public class BorFormatIdType
            {
                public string XML_BOR_FORMAT { get; set; }
            }

            public string BorSeqNo { get; set; }
            public EnumType<BorFormatIdType> BorFormatId { get; set; }
            public string BorData { get; set; }
        }

        public BackOfficeRecordRespDataType data { get; set; }
    }

    public class ClearBackOfficeRecordReqType : DomsPosBaseType
    {
        public class ClearBackOfficeRecordReqDataType
        {
            public string BorSeqNo { get; set; }
        }

        public ClearBackOfficeRecordReqDataType data { get; set; }

        public ClearBackOfficeRecordReqType(string borSeqNo) : base("clear_BackOfficeRecord_req", "00H")
        {
            this.data = new ClearBackOfficeRecordReqDataType();
            this.data.BorSeqNo = borSeqNo;
        }
    }

    public class ClearBackOfficeRecordRespType : DomsPosResponseType
    {
        public class ClearBackOfficeRecordRespDataType
        {
            public class BorBufferStatusType
            {
                public int BufferNotEmpty { get; set; }
            }

            public BitFlagType<BorBufferStatusType> BorBufferStatus { get; set; }
        }

        public ClearBackOfficeRecordRespDataType data { get; set; }
    }

    #endregion

    #region FcStatus

    public class FcStatusRespType : DomsPosResponseType
    {
        public class FcStatusRespDataType
        {
            public class FcStatus1FlagsType
            {
                public int PumpTotalsReady { get; set; }
                public int InstallationDataReceived { get; set; }
                public int FallbackMode { get; set; }
                public int FallbackTotalsNonZero { get; set; }
                public int RamErrorDetectedInFc { get; set; }
                public int OpWithStoredTransDisabled { get; set; }
                public int OptSaleDisabled { get; set; }
                public int CurrencyCodeIsEuro { get; set; }
            }

            public class FcStatus2FlagsType
            {
                public int ServiceMsgReady { get; set; }
                public int UnsolicitedStatusUpdateOn { get; set; }
                public int HwSwIncompatibilityWithinFc { get; set; }
                public int RtcError { get; set; }
                public int NoAdditionalParametersAssignedToGrades { get; set; }
                public int BackOfficeRecordExists { get; set; }
            }

            public BitFlagType<FcStatus1FlagsType> FcStatus1Flags { get; set; }
            public BitFlagType<FcStatus2FlagsType> FcStatus2Flags { get; set; }

            public string FcServiceMsgSeqNo { get; set; }
            public string FcMasterResetDateAndTime { get; set; }
            public string FcMasterResetCode { get; set; }
            public string FcResetDateAndTime { get; set; }
            public string FcResetCode { get; set; }
        }

        public FcStatusRespDataType data { get; set; }
    }

    #endregion

    #region FcInstallStatus

    public class FcInstallStatusRespType : DomsPosResponseType
    {
        public class InstalledFcDeviceGroupsType
        {
            public string ExtendedInstallMsgCode { get; set; }
            public List<string> FcDeviceId { get; set; }
        }

        public class FcInstallStatusRespDataType
        {
            public List<InstalledFcDeviceGroupsType> InstalledFcDeviceGroups { get; set; }
        }

        public FcInstallStatusRespDataType data { get; set; }
    }

    #endregion

    #region FcPriceSet

    public class FcPriceSetDataType
    {
        public string UserId { get; set; }
        public string FcPriceSetId { get; set; }
        public List<string> FcPriceGroupId { get; set; }
        public List<string> FcGradeId { get; set; }
        public List<List<string>> FcPriceGroups { get; set; }
        public string FcPriceSetDateAndTime { get; set; }
        public string PriceSetActivationDateAndTime { get; set; } = "00000000000000";
    }

    public class FcPriceSetReqType : DomsPosBaseType
    {
        public class FcPriceSetReqDataType
        {
            public string PriceSetType { get; set; }
        }

        public FcPriceSetReqDataType data { get; set; }

        public FcPriceSetReqType() : base("FcPriceSet_req", "03H")
        {
            this.data = new FcPriceSetReqDataType();
            this.data.PriceSetType = "00H";
        }
    }

    public class FcPriceSetRespType : DomsPosResponseType
    {
        public FcPriceSetDataType data { get; set; }
    }

    public class ChangeFcPriceSetReqType : DomsPosBaseType
    {
        public FcPriceSetDataType data { get; set; }

        public ChangeFcPriceSetReqType(FcPriceSetDataType data) : base("change_FcPriceSet_req", "03H")
        {
            this.data = data;
        }
    }

    #endregion

    #region FcLogon

    public class FcLogonReqType : DomsPosBaseType
    {
        public class FcLogonReqDataType
        {
            public class UnsolgMsgType
            {
                public string ExtMsgCode { get; set; }
                public string MsgSubc { get; set; }

                public UnsolgMsgType(string extMsgCode, string msgSubc)
                {
                    this.ExtMsgCode = extMsgCode;
                    this.MsgSubc = msgSubc;
                }
            }

            public class FcLogonParsType
            {
                public List<UnsolgMsgType> UnsolMsgList { get; set; }
            }

            public string FcAccessCode { get; set; }
            public string CountryCode { get; set; }
            public string PosVersionId { get; set; }
            public int[] UnsolicitedApcList { get; set; }
            public FcLogonParsType FcLogonPars { get; set; }
            public bool AllowFutures { get; set; } = true;
        }

        public FcLogonReqDataType data { get; set; }

        public FcLogonReqType(string fcAccessCode, string countryCode, string posVersionId, int[] unsolicitedApcList, List<FcLogonReqDataType.UnsolgMsgType> unSolMsgList = null)
            : base("FcLogon_req", unSolMsgList == null ? "00H" : "01H")
        {
            this.data = new FcLogonReqDataType();

            this.data.FcAccessCode = fcAccessCode;
            this.data.CountryCode = countryCode;
            this.data.PosVersionId = posVersionId;
            this.data.UnsolicitedApcList = unsolicitedApcList;

            if (unSolMsgList != null)
            {
                this.data.FcLogonPars = new FcLogonReqDataType.FcLogonParsType();
                this.data.FcLogonPars.UnsolMsgList = unSolMsgList;
            }
        }
    }

    #endregion

    #region Fuelling point related messages

    #region FpStatus

    public class FpStatusRespType : DomsPosResponseType
    {
        public class FpStatusRespDataType
        {
            public class FpSubStateFlags
            {
                public int IsLockedByPos { get; set; }
                public int IsSupervised { get; set; }
                public int IsOnline { get; set; }
                public int IsEstopped { get; set; }
                public int HasFreeBuffer { get; set; }
                public int IsInErrorState { get; set; }
                public int HasActiveGrades { get; set; }
                public int IsPreset { get; set; }
            }

            public class FpMainStateType
            {
                public string Unconfigured { get; set; }
                public string Closed { get; set; }
                public string Idle { get; set; }
                public string Error { get; set; }
                public string Calling { get; set; }
                public string PreAuthorized { get; set; }
                public string Starting { get; set; }
                public string Starting_paused { get; set; }
                public string Starting_terminated { get; set; }
                public string Fuelling { get; set; }
                public string Fuelling_paused { get; set; }
                public string Fuelling_terminated { get; set; }
                public string Unavailable { get; set; }
                public string Unavailable_and_calling { get; set; }
            }

            public class FpSupplStatusParsType
            {
                public string FuellingDataVol_e { get; set; }
                public string FuellingDataMon_e { get; set; }
            }

            public string FpId { get; set; }
            public string SmId { get; set; }
            public EnumType<FpMainStateType> FpMainState { get; set; }
            public BitFlagType<FpSubStateFlags> FpSubStates { get; set; }
            public FpSupplStatusParsType FpSupplStatusPars { get; set; }
        }

        public FpStatusRespDataType data { get; set; }

        public string MainStateText()
        {
            string s = null;

            switch (this.data.FpMainState.value)
            {
                case "00H": s = "Unconfigured"; break;
                case "01H": s = "Closed"; break;
                case "02H": s = "Idle"; break;
                case "03H": s = "Error"; break;
                case "04H": s = "Calling"; break;
                case "05H": s = "PreAuthorized"; break;
                case "06H": s = "Starting"; break;
                case "07H": s = "Starting paused"; break;
                case "08H": s = "Starting terminated"; break;
                case "09H": s = "Fuelling"; break;
                case "0AH": s = "Fuelling Paused"; break;
                case "0BH": s = "Fuelling Terminated"; break;
                case "0CH": s = "Unavailable"; break;
                case "0DH": s = "Unavailable and Calling"; break;
            }

            return s;
        }
    }

    #endregion

    #region FpError

    public class FpErrorRespType : DomsPosResponseType
    {
        public class FpErrorType
        {
            public string value { get; set; }
        }

        public class FpErrorRespDataType
        {
            public string FpId { get; set; }
            public EnumType<FpErrorType> FpErrorCode { get; set; }
            public string FpErrorDateAndTime { get; set; }
            public string PumpProtocolId { get; set; }
            public string PumpErrorCode { get; set; }
        }

        public FpErrorRespDataType data { get; set; }

        public string ErrorText()
        {
            string s = null;

            switch (this.data.FpErrorCode.value)
            {
                case "00": s = "No Error"; break;
                case "01": s = "Unspecified HW errror"; break;
                case "02": s = "Unspecified SW error"; break;
                case "03": s = "PROM Error"; break;
                case "04": s = "RAM Error"; break;
                case "06": s = "Pulse Error"; break;
                case "07": s = "Display Error"; break;
                case "08": s = "Output Control Error"; break;
                case "10": s = "Preset Overrun Error"; break;
                case "11": s = "Preset Grade Error"; break;
                case "12": s = "calculation Error"; break;
                case "13": s = "Blend Error"; break;
                case "14": s = "unexpected Pump Start"; break;
                case "15": s = "Transaction data error"; break;
                case "16": s = "Pump Data Sequence Error"; break;
                case "17": s = "Fp and Pump Installation Mismatch"; break;
                case "18": s = "Error Code Unavailable"; break;
                case "19": s = "MKS Pumps"; break;
                case "20": s = "Security Telegram Error"; break;
                case "21": s = "Fp Reset from POS"; break;
                case "22": s = "Pump Totals Mismatch"; break;
                case "23": s = "Grade Mismatch"; break;
                case "24": s = "Sub Pump Error"; break;
                case "25": s = "Battery Error"; break;
                case "26": s = "TDS80 Pump Only"; break;
                case "27": s = "Fuelling Data Used Error"; break;
                case "28": s = "Max Number of Consecutive Zero Transactions Reached"; break;
                case "29": s = "Illegal Price"; break;
                case "58": s = "ATC Error"; break;
            }

            return s;
        }
    }

    public class FpErrorMsgReqType : DomsPosBaseType
    {
        public class FpErrorMsgReqDataType
        {
            public string FpId { get; set; }
        }

        public FpErrorMsgReqDataType data { get; set; }

        public FpErrorMsgReqType(int fpId) : base("FpErrorMsg_req", "00H")
        {
            this.data = new FpErrorMsgReqDataType();
            this.data.FpId = fpId.ToString();
        }
    }

    #endregion

    #region clear_FpError

    public class FpClearErrorReqType : DomsPosBaseType
    {
        public class FpClearErrorReqDataType
        {
            public string FpId { get; set; }
            public string FpErrorCode { get; set; }
        }

        public FpClearErrorReqDataType data { get; set; }

        public FpClearErrorReqType(int fpId, string fpErrorCode) : base("clear_FpError_req", "00H")
        {
            this.data = new FpClearErrorReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.FpErrorCode = fpErrorCode;
        }
    }

    #endregion

    #region authorize_Fp

    public class FpAuthReqType : DomsPosBaseType
    {
        public class FpAuthReqDataType
        {
            public string FpId { get; set; }
            public string PosId { get; set; }
        }

        public FpAuthReqDataType data { get; set; }

        public FpAuthReqType(int fpId, int posId) : base("authorize_Fp_req", "00H")
        {
            this.data = new FpAuthReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.PosId = posId.ToString();
        }
    }

    #endregion

    #region cancel_FpAuth

    public class FpCancelAuthReqType : DomsPosBaseType
    {
        public class FpCancelAuthReqDataType
        {
            public string FpId { get; set; }
            public string PosId { get; set; }
        }

        public FpCancelAuthReqDataType data { get; set; }

        public FpCancelAuthReqType(int fpId, int posId) : base("cancel_FpAuth_req", "00H")
        {
            this.data = new FpCancelAuthReqDataType();
            this.data.FpId = fpId.ToString();
            this.data.PosId = posId.ToString();
        }
    }

    #endregion

    #endregion
}
