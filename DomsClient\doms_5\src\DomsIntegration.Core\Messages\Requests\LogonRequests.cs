﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Models;

namespace DomsIntegration.Core.Messages.Requests
{
    /// <summary>
    /// FcLogon request message
    /// </summary>
    public class FcLogonRequest : JplRequest
    {
        public FcLogonRequest() : base("FcLogon_req") { }

        public new FcLogonRequestData Data { get; set; }
    }

    /// <summary>
    /// FcLogon request data
    /// </summary>
    public class FcLogonRequestData
    {
        public string FcAccessCode { get; set; }
        public string CountryCode { get; set; }
        public string PosVersionId { get; set; }
    }

    /// <summary>
    /// Extended FcLogon request message
    /// </summary>
    public class FcLogonExtendedRequest : JplRequest
    {
        public FcLogonExtendedRequest() : base("FcLogon_req", "01H") { }

        public new FcLogonExtendedRequestData Data { get; set; }
    }

    /// <summary>
    /// Extended FcLogon request data
    /// </summary>
    public class FcLogonExtendedRequestData : FcLogonRequestData
    {
        public FcLogonParameters FcLogonPars { get; set; }
    }

    /// <summary>
    /// Logon parameters
    /// </summary>
    public class FcLogonParameters
    {
        public UnsolicitedMessage[] UnsolMsgList { get; set; }
    }

    /// <summary>
    /// Unsolicited message configuration
    /// </summary>
    public class UnsolicitedMessage
    {
        public string ExtMsgCode { get; set; }
        public string MsgSubc { get; set; }
    }
}
