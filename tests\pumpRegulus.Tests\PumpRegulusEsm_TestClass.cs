﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using Triquestra.Common.PumpEsm;

namespace pumpRegulus.Tests
{
    [TestClass]
    public class PumpRegulusEsm_TestClass
    {
        [TestMethod]
        public void PumpRegulusEsm_TestClass_InvalidXML_in_request_reported_by_Bharath()
        {
            var esm = new PumpRegulusEsm();

            string message = @"<pumpesmrequest> DllType>DllPump</DllType><action>notify</action><message>N=1</message></pumpesmrequest>";

            var actual = esm.PerformESMAction(message);
        }
    }
}
