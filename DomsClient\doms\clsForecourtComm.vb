﻿Imports System.Net.Sockets
Imports System.Net
Imports System.Text
Imports System.Threading
Imports Newtonsoft.Json

Public Class clsForecourtComm

  Private WithEvents _Socket As Socket = Nothing

  Private Const START_FRAMING_CHAR As Char = Chr(2)
  Private Const END_FRAMING_CHAR As Char = Chr(3)

  Private _RxBuf() As Byte = New Byte(2000) {}
  Private _sb As New StringBuilder
  Private _ResponseReady As New ManualResetEventSlim()
  Private _Response As clsResponse
  Private _MsgTimeout As Integer = 10000
  Private _WaitingForResponse = False
  Private _LastRxTime As Date
 
  Public Event ConnectionLost()
  Public Event RequestSent(Request As String)
  Public Event ResponseReceived(Response As String)
  Public Event UnsolicitedMsgReceived(MsgName As String, Msg As String)

  Public Class clsResponse
    Public ResponseName As String
    Public Json As String
    Public IsReject As Boolean

    Public Sub New(ResponseName As String, Json As String)
      Me.IsReject = ResponseName = "RejectMessage_resp"
      Me.ResponseName = ResponseName
      Me.Json = Json
    End Sub
  End Class

  Public Function IsConnected()
    If _Socket IsNot Nothing AndAlso _Socket.Connected Then
      If Date.Now.Subtract(_LastRxTime).TotalSeconds > 30 Then
        Me.Disconnect()
        _FireConnectionLost()

        Return False
      Else
        Return True
      End If
    Else
      Return False
    End If
  End Function

  Public Sub Connect(Host As String)
    _WaitingForResponse = False
    _ResponseReady.Reset()

    _Socket = New Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp)
    _Socket.Connect(Host, My.Settings.PortNumber)
    _Socket.BeginReceive(_RxBuf, 0, _RxBuf.Length, SocketFlags.None, New AsyncCallback(AddressOf _ReceiveCallBack), Nothing)
    _LastRxTime = Date.Now
  End Sub

  Public Sub Disconnect()
    Try
      If _Socket IsNot Nothing Then
        If _Socket.Connected Then _Socket.Close()
        _Socket.Dispose()
        _Socket = Nothing

        _sb.Clear()
      End If
    Catch ex As Exception
    End Try
  End Sub

  Public Function SendMessage(Request As Object, Optional waitForResponse As Boolean = True) As clsResponse
    Static SerializerSettings As JsonSerializerSettings

    If SerializerSettings Is Nothing Then
      SerializerSettings = New JsonSerializerSettings
      SerializerSettings.NullValueHandling = NullValueHandling.Ignore
    End If

    If TypeOf Request IsNot DomsPosBaseType Then Throw New Exception("Request must be of type DomsPosBaseType ")
    Dim Req As String = JsonConvert.SerializeObject(Request, SerializerSettings)
    Return _InternalSendMessage(Req, waitForResponse)
  End Function

  Public Function SendRawMessage(Request As String) As clsResponse
    Return _InternalSendMessage(Request, True)
  End Function

  Private Sub _FireConnectionLost()
    Try
      RaiseEvent ConnectionLost()
    Catch ex As Exception
    End Try
  End Sub

  Private Sub _ReceiveCallBack(ByVal ar As IAsyncResult)
    Dim BytesRead As Integer
    Dim Messages() As String
    Dim Msg As String
    Dim DppMessage As DomsPosResponseType
    Dim MsgName As String = Nothing
    Dim RawMsgName As String = Nothing
    Dim IncompleteMsg As String = String.Empty

    Try
      If _Socket IsNot Nothing Then
        BytesRead = _Socket.EndReceive(ar)

        If BytesRead > 0 Then
          _sb.Append(Encoding.ASCII.GetString(_RxBuf, 0, BytesRead))

          If _sb.ToString.Contains(END_FRAMING_CHAR) Then
            Messages = _sb.ToString.Split(START_FRAMING_CHAR)

            For x As Integer = 1 To Messages.Length - 1
              Msg = Messages(x)

              If Msg.EndsWith(END_FRAMING_CHAR) Then
                _LastRxTime = Date.Now
                Msg = Msg.Substring(0, Msg.Length - 1)

                Try
                  RaiseEvent ResponseReceived(Msg)
                Catch ex As Exception
                End Try

                DppMessage = JsonConvert.DeserializeObject(Of DomsPosResponseType)(Msg)

                If DppMessage.solicited Then
                  _Response = New clsResponse(DppMessage.name, Msg)
                  _ResponseReady.Set()
                Else
                  If DppMessage.name <> "heartbeat" Then
                    Try
                      RaiseEvent UnsolicitedMsgReceived(DppMessage.name, Msg)
                    Catch ex As Exception
                    End Try
                  End If
                End If
              Else
                IncompleteMsg = Msg
              End If
            Next

            _sb.Clear()
            _sb.AppendFormat("{0}{1}", START_FRAMING_CHAR, IncompleteMsg)
          End If
        End If

        _Socket.BeginReceive(_RxBuf, 0, _RxBuf.Length, SocketFlags.None, New AsyncCallback(AddressOf _ReceiveCallBack), Nothing)
      End If
    Catch ex As Exception
      Disconnect()
      _FireConnectionLost()
    End Try
  End Sub

  Private Function _InternalSendMessage(Request As String, Optional waitForResponse As Boolean = True) As clsResponse
    Dim WaitResult As Boolean

    If _Socket IsNot Nothing AndAlso _Socket.Connected Then
      If _WaitingForResponse Then Throw New Exception("There is already a pending request")

      _WaitingForResponse = True
      _Response = Nothing
      _ResponseReady.Reset()

      _Socket.Send(Encoding.ASCII.GetBytes(String.Format("{0}{1}{2}", START_FRAMING_CHAR, Request, END_FRAMING_CHAR)))

      Try
        RaiseEvent RequestSent(Request)
      Catch ex As Exception
      End Try

      If Not waitForResponse Then
        _WaitingForResponse = False
        Return Nothing
      End If

      WaitResult = _ResponseReady.Wait(_MsgTimeout)
      _WaitingForResponse = False

      If WaitResult Then
        If _Response IsNot Nothing Then
          Return _Response
        Else
          Throw New Exception("Empty response")
        End If
      Else
        Throw New Exception("Message timeout")
      End If
    Else
      Throw New Exception("Socket not connected")
    End If
  End Function

End Class
