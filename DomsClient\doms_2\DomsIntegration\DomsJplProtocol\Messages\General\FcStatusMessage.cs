﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcStatusRequest : JplRequest
    {
        public class FcStatusData
        {
            [JsonPropertyName("NecessaryDevices")]
            public List<NecessaryDevice> NecessaryDevices { get; set; }
        }

        public class NecessaryDevice
        {
            [JsonPropertyName("NecessaryDeviceTypeId")]
            public string NecessaryDeviceTypeId { get; set; }

            [JsonPropertyName("NecessaryDeviceStatus")]
            public string NecessaryDeviceStatus { get; set; }
        }
    }

    public class FcStatusResponse : JplResponse
    {
        public class FcStatusResponseData
        {
            [JsonPropertyName("FcStatus1Flags")]
            public BitFlags FcStatus1Flags { get; set; }

            [JsonPropertyName("FcStatus2Flags")]
            public BitFlags FcStatus2Flags { get; set; }

            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }

            [JsonPropertyName("FcMasterResetDateAndTime")]
            public string FcMasterResetDateAndTime { get; set; }

            [JsonPropertyName("FcMasterResetCode")]
            public int FcMasterResetCode { get; set; }

            [JsonPropertyName("FcResetDateAndTime")]
            public string FcResetDateAndTime { get; set; }

            [JsonPropertyName("FcResetCode")]
            public string FcResetCode { get; set; }

            [JsonPropertyName("FcStatusPars")]
            public FcStatusParameters FcStatusPars { get; set; }
        }

        public class FcStatusParameters
        {
            [JsonPropertyName("FcShiftNo")]
            public string FcShiftNo { get; set; }

            [JsonPropertyName("FcShiftChangeDateAndTime")]
            public string FcShiftChangeDateAndTime { get; set; }

            [JsonPropertyName("VATRateSeqNo")]
            public string VATRateSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingSeqNo")]
            public string FcRTCSettingSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingDateAndTime")]
            public string FcRTCSettingDateAndTime { get; set; }

            [JsonPropertyName("FcCurrencySettings")]
            public FcCurrencySettings FcCurrencySettings { get; set; }
        }

        public class FcCurrencySettings
        {
            [JsonPropertyName("CurrencyCode")]
            public string CurrencyCode { get; set; }

            [JsonPropertyName("DecimalPositionInPrice")]
            public int DecimalPositionInPrice { get; set; }

            [JsonPropertyName("DecimalPositionInVol")]
            public int DecimalPositionInVol { get; set; }

            [JsonPropertyName("DecimalPositionInMoney")]
            public int DecimalPositionInMoney { get; set; }
        }
    }
}
