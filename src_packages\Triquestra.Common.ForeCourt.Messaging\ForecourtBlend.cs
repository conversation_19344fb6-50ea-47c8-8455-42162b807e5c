﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public class ForecourtBlend:IForecourtBlend
    {
        public ForecourtBlend(int id, string name)
        {
            BlendId = id;
            BlendName = name;
        }

        public int BlendId { get; set; }
        public int NozzleId { get; set; }
        public string BlendName { get; set; }
        public FuelTypes FuelType { get; set; }

        public static ForecourtBlend Deserialise(XElement element)
        {
            //<Blend ID='3011' Name='ZX Premium' />
            if(element.Attribute("ID")==null) throw new XmlSchemaException("<Blend> node does not have <ID> attribute");
            if (element.Attribute("Name") == null) throw new XmlSchemaException("<Blend> node does not have <Name> attribute");
            return new ForecourtBlend(int.Parse(element.Attribute("ID").Value), element.Attribute("Name").Value);
        }
    }
}
