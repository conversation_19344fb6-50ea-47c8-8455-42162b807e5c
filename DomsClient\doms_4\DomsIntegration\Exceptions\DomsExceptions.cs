﻿using System;

namespace DomsIntegration.Exceptions
{
    /// <summary>
    /// Base exception for all DOMs-related errors
    /// </summary>
    public class DomsException : Exception
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        public DomsException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when connection-related errors occur
    /// </summary>
    public class DomsConnectionException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        public DomsConnectionException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsConnectionException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsConnectionException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsConnectionException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when authentication-related errors occur
    /// </summary>
    public class DomsAuthenticationException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        public DomsAuthenticationException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsAuthenticationException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsAuthenticationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsAuthenticationException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when operation-related errors occur
    /// </summary>
    public class DomsOperationException : DomsException
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        public DomsOperationException() : base() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        public DomsOperationException(string message) : base(message) { }

        /// <summary>
        /// Initializes a new instance of the <see cref="DomsOperationException"/> class
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DomsOperationException(string message, Exception innerException) : base(message, innerException) { }
    }
}
