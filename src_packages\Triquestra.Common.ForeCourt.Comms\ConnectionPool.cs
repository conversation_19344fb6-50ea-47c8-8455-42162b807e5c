﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Triquestra.Common.PumpEsm.Comms
{
    [Obsolete("The class is redundant",false)]
    public static class ConnectionPool<T>
        where T : class,IForecourtConnection
    {        
        public static T GetCachedConnection(string tcpAddress, int tcpPort, string udpAddress, int udpPort, int workstationId, Func<T> initNewInstance)
        {
            return initNewInstance();            
        }
    }

    internal static class CPool<T>
        where T : class
    {
        private struct CacheKey
        {
            internal string tcpAddress;
            internal int tcpPort;
            internal string udpAddress;
            internal int udpPort;
            internal int workstationId;

            public override int GetHashCode()
            {
                return (tcpAddress ?? string.Empty).GetHashCode() ^ tcpPort.GetHashCode() ^ (udpAddress ?? string.Empty).GetHashCode() ^ udpPort.GetHashCode() ^ workstationId.GetHashCode();
            }
            public override bool Equals(object obj)
            {
                if (obj != null && (obj is <PERSON><PERSON><PERSON><PERSON>))
                {
                    CacheKey key = (Cache<PERSON>ey)obj;
                    return (tcpAddress == key.tcpAddress && tcpPort == key.tcpPort && udpAddress == key.udpAddress && udpPort == key.udpPort && workstationId == key.workstationId);
                }
                return false;
            }
        }
        private static object _lock = new Object();
        private static Dictionary<CacheKey, WeakReference> _cache = new Dictionary<CacheKey, WeakReference>();
        public static T GetCachedConnection(string tcpAddress, int tcpPort, string udpAddress, int udpPort, int workstationId, Func<T> initNewInstance)
        {
            CacheKey key;
            key.tcpAddress = tcpAddress;
            key.tcpPort = tcpPort;
            key.udpAddress = udpAddress;
            key.udpPort = udpPort;
            key.workstationId = workstationId;
            T res;
            WeakReference reference;
            lock (_lock)
            {
                if (_cache.TryGetValue(key, out reference) && reference.IsAlive && (res = (T)reference.Target) != null)
                {
#if DEBUG
                    ForecourtConnection.Log(@"c:\temp\comm\pooling.txt", string.Format("Found cached connection (TCP:{0}:{1} UDP:{2}:{3} WS: {4} Type:{5}). Returning",
                        tcpAddress, tcpPort, udpAddress, udpPort, workstationId, typeof(T).Name));
#endif
                    return (T)res;
                }
                else
                {
                    if (initNewInstance != null)
                    {
                        res = initNewInstance();
#if DEBUG
                        ForecourtConnection.Log(@"c:\temp\comm\pooling.txt", string.Format("New connection (TCP:{0}:{1} UDP:{2}:{3} WS: {4} Type:{5}) returned",
                            tcpAddress, tcpPort, udpAddress, udpPort, workstationId, typeof(T).Name));
#endif
                        _cache[key] = new WeakReference(res);
                        return (T)res;
                    }
                    return null;
                }
            }
        }
    }
}
