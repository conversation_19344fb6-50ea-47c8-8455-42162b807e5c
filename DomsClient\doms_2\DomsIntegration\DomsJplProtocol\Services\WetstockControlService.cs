﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.Wetstock;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class WetstockControlService
    {
        private readonly PssClient _client;

        public WetstockControlService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<TankGaugeStatusResponse> GetTankGaugeStatusAsync(string tankId = "00", 
            CancellationToken cancellationToken = default)
        {
            var request = new TankGaugeStatusRequest
            {
                Name = JplConstants.MessageNames.TANK_GAUGE_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new TankGaugeStatusRequest.TankGaugeStatusRequestData
                {
                    TankId = tankId
                }
            };

            return await _client.SendRequestAsync<TankGaugeStatusResponse>(request, cancellationToken);
        }
    }
}
