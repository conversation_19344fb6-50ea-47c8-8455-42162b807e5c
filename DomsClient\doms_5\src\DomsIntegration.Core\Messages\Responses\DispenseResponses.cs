﻿using DomsIntegration.Core.Messages;
using DomsIntegration.Core.Models;

namespace DomsIntegration.Core.Messages.Responses
{
    /// <summary>
    /// FP Status response
    /// </summary>
    public class FpStatusResponse : JplResponse
    {
        public FpStatusResponse() : base("FpStatus_resp") { }

        public new FpStatusResponseData Data { get; set; }
    }

    /// <summary>
    /// FP Status response data
    /// </summary>
    public class FpStatusResponseData
    {
        public string FpId { get; set; }
        public string SmId { get; set; }
        public JplEnum<FpMainState> FpMainState { get; set; }
        public JplBitFlags FpSubStates { get; set; }
        public string FpLockId { get; set; }
    }

    /// <summary>
    /// Open FP response
    /// </summary>
    public class OpenFpResponse : JplResponse
    {
        public OpenFpResponse() : base("open_Fp_resp") { }

        public new OpenFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Open FP response data
    /// </summary>
    public class OpenFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Close FP response
    /// </summary>
    public class CloseFpResponse : JplResponse
    {
        public CloseFpResponse() : base("close_Fp_resp") { }

        public new CloseFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Close FP response data
    /// </summary>
    public class CloseFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Authorize FP response
    /// </summary>
    public class AuthorizeFpResponse : JplResponse
    {
        public AuthorizeFpResponse() : base("authorize_Fp_resp") { }

        public new AuthorizeFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Authorize FP response data
    /// </summary>
    public class AuthorizeFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization response
    /// </summary>
    public class CancelFpAuthResponse : JplResponse
    {
        public CancelFpAuthResponse() : base("cancel_FpAuth_resp") { }

        public new CancelFpAuthResponseData Data { get; set; }
    }

    /// <summary>
    /// Cancel FP authorization response data
    /// </summary>
    public class CancelFpAuthResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// Emergency stop FP response
    /// </summary>
    public class EstopFpResponse : JplResponse
    {
        public EstopFpResponse() : base("estop_Fp_resp") { }

        public new EstopFpResponseData Data { get; set; }
    }

    /// <summary>
    /// Emergency stop FP response data
    /// </summary>
    public class EstopFpResponseData
    {
        public string FpId { get; set; }
    }

    /// <summary>
    /// FP supervised transaction response
    /// </summary>
    public class FpSupTransResponse : JplResponse
    {
        public FpSupTransResponse() : base("FpSupTrans_resp") { }

        public new FpSupTransResponseData Data { get; set; }
    }

    /// <summary>
    /// FP supervised transaction response data
    /// </summary>
    public class FpSupTransResponseData
    {
        public string FpId { get; set; }
        public string TransSeqNo { get; set; }
        public TransactionParameters TransPars { get; set; }
    }

    /// <summary>
    /// Transaction parameters
    /// </summary>
    public class TransactionParameters
    {
        public string FcShiftNo { get; set; }
        public string ReceiptNo { get; set; }
        public string AuthId { get; set; }
        public string SmId { get; set; }
        public string FmId { get; set; }
        public string FpId { get; set; }
        public string FcPriceGroupId { get; set; }
        public string FcPriceSetId { get; set; }
        public string CurrencyCode { get; set; }
        public string FcGradeId { get; set; }
        public string Price { get; set; }
        public string Vol { get; set; }
        public string Money { get; set; }
        public FcDateTime StartDate { get; set; }
        public string StartTime { get; set; }
        public FcDateTime FinishDate { get; set; }
        public string FinishTime { get; set; }
        public string Price_e { get; set; }
        public string Vol_e { get; set; }
        public string Money_e { get; set; }
    }
}
