﻿using System;
using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryStartedMessage : ForecourtEventMessage
    {
        public IDelivery Delivery { get; set; }

        public EventDeliveryStartedMessage(int sequenceNo, int sourceId, int targetId, IDelivery delivery) 
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_STARTED)
        {
            Delivery = delivery;
        }

        public static EventDeliveryStartedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var deliveryStartedNode = eventNode.Element("DeliveryStarted");

            if (deliveryStartedNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryStarted> node");
            }
            return
                deliveryStartedNode.Descendants("Delivery")
                    .Select(del => Messaging.Delivery.Parse(del, sourceId))
                    .Select(delivery => new EventDeliveryStartedMessage(seqNo, sourceId, targetId, delivery))
                    .FirstOrDefault();
        }
    }
}
