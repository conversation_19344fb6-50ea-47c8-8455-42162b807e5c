﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace DomsIntegration.Core.Models
{
    /// <summary>
    /// Represents a date and time in FC format (YYYYMMDDhhmmss)
    /// </summary>
    [JsonConverter(typeof(FcDateTimeConverter))]
    public struct FcDateTime
    {
        private readonly string _value;

        public FcDateTime(DateTime dateTime)
        {
            _value = dateTime.ToString("yyyyMMddHHmmss");
        }

        public FcDateTime(string value)
        {
            _value = value ?? throw new ArgumentNullException(nameof(value));
        }

        public DateTime ToDateTime()
        {
            if (string.IsNullOrEmpty(_value) || _value.Length != 14)
                throw new FormatException("Invalid FcDateTime format");

            return DateTime.ParseExact(_value, "yyyyMMddHHmmss", null);
        }

        public override string ToString() => _value;

        public static implicit operator string(FcDateTime fcDateTime) => fcDateTime._value;
        public static implicit operator FcDateTime(string value) => new FcDateTime(value);
        public static implicit operator FcDateTime(DateTime dateTime) => new FcDateTime(dateTime);
    }

    /// <summary>
    /// JSON converter for FcDateTime
    /// </summary>
    public class FcDateTimeConverter : JsonConverter<FcDateTime>
    {
        public override void WriteJson(JsonWriter writer, FcDateTime value, JsonSerializer serializer)
        {
            writer.WriteValue(value.ToString());
        }

        public override FcDateTime ReadJson(JsonReader reader, Type objectType, FcDateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            return new FcDateTime(reader.Value?.ToString());
        }
    }

    /// <summary>
    /// Represents a forecourt money value
    /// </summary>
    public struct FcMoney
    {
        private readonly string _value;

        public FcMoney(decimal amount, int decimalPlaces = 2)
        {
            _value = ((long)(amount * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcMoney(string value)
        {
            _value = value ?? "0";
        }

        public decimal ToDecimal(int decimalPlaces = 2)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcMoney fcMoney) => fcMoney._value;
        public static implicit operator FcMoney(string value) => new FcMoney(value);
        public static implicit operator FcMoney(decimal amount) => new FcMoney(amount);
    }

    /// <summary>
    /// Represents a forecourt volume value
    /// </summary>
    public struct FcVolume
    {
        private readonly string _value;

        public FcVolume(decimal volume, int decimalPlaces = 2)
        {
            _value = ((long)(volume * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcVolume(string value)
        {
            _value = value ?? "0";
        }

        public decimal ToDecimal(int decimalPlaces = 2)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcVolume fcVolume) => fcVolume._value;
        public static implicit operator FcVolume(string value) => new FcVolume(value);
        public static implicit operator FcVolume(decimal volume) => new FcVolume(volume);
    }

    /// <summary>
    /// Represents a forecourt price value
    /// </summary>
    public struct FcPrice
    {
        private readonly string _value;

        public FcPrice(decimal price, int decimalPlaces = 4)
        {
            _value = ((long)(price * (decimal)Math.Pow(10, decimalPlaces))).ToString();
        }

        public FcPrice(string value)
        {
            _value = value ?? "0";
        }

        public decimal ToDecimal(int decimalPlaces = 4)
        {
            if (long.TryParse(_value, out long value))
            {
                return value / (decimal)Math.Pow(10, decimalPlaces);
            }
            return 0;
        }

        public override string ToString() => _value;

        public static implicit operator string(FcPrice fcPrice) => fcPrice._value;
        public static implicit operator FcPrice(string value) => new FcPrice(value);
        public static implicit operator FcPrice(decimal price) => new FcPrice(price);
    }

    /// <summary>
    /// Enumeration wrapper for JPL enum values
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    public class JplEnum<T> where T : struct, Enum
    {
        [JsonProperty("enum")]
        public Dictionary<string, string> EnumValues { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        public T GetEnumValue()
        {
            if (EnumValues != null)
            {
                var kvp = EnumValues.FirstOrDefault(e => e.Value == Value);
                if (Enum.TryParse<T>(kvp.Key, true, out T result))
                    return result;
            }
            return default(T);
        }
    }

    /// <summary>
    /// Represents bit flags in JPL messages
    /// </summary>
    public class JplBitFlags
    {
        [JsonProperty("value")]
        public int Value { get; set; }

        [JsonProperty("bits")]
        public Dictionary<string, int> Bits { get; set; }

        public bool IsBitSet(string bitName)
        {
            return Bits?.ContainsKey(bitName) == true && Bits[bitName] > 0;
        }
    }
}
