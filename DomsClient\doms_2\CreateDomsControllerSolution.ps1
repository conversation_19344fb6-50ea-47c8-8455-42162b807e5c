# PowerShell script to create DOMS Controller JPL integration solution
param(
    [string]$SolutionPath = "C:\Source\DomsControllerIntegration",
    [string]$SolutionName = "DomsControllerIntegration"
)

# Ensure we have the required tools
if (-not (Get-Command "dotnet" -ErrorAction SilentlyContinue)) {
    Write-Error "dotnet CLI is required but not found. Please install .NET SDK."
    exit 1
}

Write-Host "Creating DOMS Controller JPL Integration Solution at: $SolutionPath" -ForegroundColor Green

# Create solution directory (use a subdirectory to avoid conflicts)
$FullSolutionPath = Join-Path $SolutionPath $SolutionName
if (Test-Path $FullSolutionPath) {
    Write-Host "Directory already exists. Removing..." -ForegroundColor Yellow
    Remove-Item $FullSolutionPath -Recurse -Force
}
New-Item -ItemType Directory -Path $FullSolutionPath -Force | Out-Null
Set-Location $FullSolutionPath

# Create solution
Write-Host "Creating solution..." -ForegroundColor Blue
dotnet new sln -n $SolutionName

# Create main library project
$MainProjectName = "DomsJplProtocol"
Write-Host "Creating main library project: $MainProjectName" -ForegroundColor Blue

# Create project directory
New-Item -ItemType Directory -Path $MainProjectName -Force | Out-Null
Set-Location $MainProjectName

# Create the .csproj file manually for .NET Framework 4.8
$projectContent = @"
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>latest</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="6.0.6" />
    <PackageReference Include="System.Threading.Tasks.Extensions" Version="4.5.4" />
    <PackageReference Include="System.Memory" Version="4.5.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Net.Http" />
  </ItemGroup>

</Project>
"@

$projectContent | Out-File -FilePath "$MainProjectName.csproj" -Encoding UTF8

# Navigate back and add project to solution
Set-Location ..
dotnet sln add "$MainProjectName\$MainProjectName.csproj"

# Create test project
$TestProjectName = "DomsJplProtocol.Tests"
Write-Host "Creating test project: $TestProjectName" -ForegroundColor Blue

# Create project directory
New-Item -ItemType Directory -Path $TestProjectName -Force | Out-Null
Set-Location $TestProjectName

# Create the test .csproj file manually for .NET Framework 4.8
$testProjectContent = @"
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>latest</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.1.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="2.2.8" />
    <PackageReference Include="MSTest.TestFramework" Version="2.2.8" />
    <PackageReference Include="coverlet.collector" Version="3.1.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\$MainProjectName\$MainProjectName.csproj" />
  </ItemGroup>

</Project>
"@

$testProjectContent | Out-File -FilePath "$TestProjectName.csproj" -Encoding UTF8

# Navigate back and add project to solution
Set-Location ..
dotnet sln add "$TestProjectName\$TestProjectName.csproj"

# Create console test app
$ConsoleProjectName = "DomsJplProtocol.ConsoleApp"
Write-Host "Creating console app: $ConsoleProjectName" -ForegroundColor Blue

# Create project directory
New-Item -ItemType Directory -Path $ConsoleProjectName -Force | Out-Null
Set-Location $ConsoleProjectName

# Create the console .csproj file manually for .NET Framework 4.8
$consoleProjectContent = @"
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net48</TargetFramework>
    <LangVersion>latest</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\$MainProjectName\$MainProjectName.csproj" />
  </ItemGroup>

</Project>
"@

$consoleProjectContent | Out-File -FilePath "$ConsoleProjectName.csproj" -Encoding UTF8

# Create Program.cs
$programContent = @"
using System;
using System.Threading.Tasks;
using DomsJplProtocol.Network;
using DomsJplProtocol.Services;

namespace DomsJplProtocol.ConsoleApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("DOMS JPL Protocol Console Test Application");
            Console.WriteLine("==========================================");
            
            Console.Write("Enter DOMS controller IP address: ");
            string ipAddress = Console.ReadLine();
            
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = "127.0.0.1";
                
            Console.WriteLine(\$"Connecting to {ipAddress}...");
            
            try
            {
                using (var client = PssConnectionFactory.CreateUnencryptedClient(ipAddress))
                {
                    client.ConnectionStateChanged += (sender, e) => 
                        Console.WriteLine(\$"Connection state changed: {e.OldState} -> {e.NewState}");
                    
                    client.UnsolicitedMessageReceived += (sender, e) => 
                        Console.WriteLine(\$"Received unsolicited message: {e.Message.Name}");
                    
                    client.ErrorOccurred += (sender, e) => 
                        Console.WriteLine(\$"Error: {e.Message}");
                    
                    await client.ConnectAsync();
                    Console.WriteLine("Connected successfully!");
                    
                    Console.WriteLine("Logging on to forecourt controller...");
                    var response = await client.LogonAsync(
                        "POS,APPL_ID=CONSOLE,RI,UNSO_FPSTA_3", 
                        "0000", 
                        "CONSOLE_TEST_1.0");
                        
                    if (response != null)
                    {
                        Console.WriteLine("Logged in successfully!");
                        
                        var fcService = new ForecourtControllerService(client);
                        var status = await fcService.GetStatusAsync();
                        Console.WriteLine(\$"Forecourt controller status received");
                        
                        Console.WriteLine("Press Enter to get forecourt date/time...");
                        Console.ReadLine();
                        
                        var dateTime = await fcService.GetDateTimeAsync();
                        Console.WriteLine(\$"Forecourt controller date/time: {dateTime.Data.FcDateAndTime}");
                        
                        Console.WriteLine("Press Enter to enable status updates...");
                        Console.ReadLine();
                        
                        await fcService.EnableStatusUpdates();
                        Console.WriteLine("Status updates enabled");
                        
                        if (client.IsLoggedOn)
                        {
                            Console.WriteLine("Successfully communicating with DOMS controller");
                            Console.WriteLine("Press Enter to disconnect...");
                            Console.ReadLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed to log in");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(\$"Error: {ex.Message}");
            }
            
            Console.WriteLine("Press Enter to exit...");
            Console.ReadLine();
        }
    }
}
"@

$programContent | Out-File -FilePath "Program.cs" -Encoding UTF8

# Navigate back and add project to solution
Set-Location ..
dotnet sln add "$ConsoleProjectName\$ConsoleProjectName.csproj"

# Create directory structure for main project
Set-Location $MainProjectName
Write-Host "Creating directory structure for main project..." -ForegroundColor Blue

$directories = @(
    "Core",
    "Network",
    "Messages\General",
    "Messages\DispenseControl",
    "Messages\PriceDisplay",
    "Messages\Wetstock",
    "Services",
    "Utilities",
    "Models"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
}

# Create core message classes
Write-Host "Creating core message classes..." -ForegroundColor Blue

# Constants.cs
$constantsContent = @"
using System;

namespace DomsJplProtocol.Core
{
    public static class JplConstants
    {
        // Message Types
        public static class MessageNames
        {
            // General FC Messages
            public const string FC_LOGON_REQ = "FcLogon_req";
            public const string FC_LOGON_RESP = "FcLogon_resp";
            public const string FC_STATUS_REQ = "FcStatus_req";
            public const string FC_STATUS_RESP = "FcStatus_resp";
            public const string FC_DATE_TIME_REQ = "FcDateAndTime_req";
            public const string FC_DATE_TIME_RESP = "FcDateAndTime_resp";
            public const string CHANGE_FC_DATE_TIME_REQ = "change_FcDateAndTime_req";
            public const string CHANGE_FC_DATE_TIME_RESP = "change_FcDateAndTime_resp";
            
            // Status Messages
            public const string FC_INSTALL_STATUS_REQ = "FcInstallStatus_req";
            public const string FC_INSTALL_STATUS_RESP = "FcInstallStatus_resp";
            public const string FC_PRICE_SET_STATUS_REQ = "FcPriceSetStatus_req";
            public const string FC_PRICE_SET_STATUS_RESP = "FcPriceSetStatus_resp";
            public const string FC_OPERATION_MODE_STATUS_REQ = "FcOperationModeStatus_req";
            public const string FC_OPERATION_MODE_STATUS_RESP = "FcOperationModeStatus_resp";
            
            // Service Messages
            public const string FC_SERVICE_MSG_REQ = "FcServiceMsg_req";
            public const string FC_SERVICE_MSG_RESP = "FcServiceMsg_resp";
            public const string CLEAR_FC_SERVICE_MSG_REQ = "clear_FcServiceMsg_req";
            public const string CLEAR_FC_SERVICE_MSG_RESP = "clear_FcServiceMsg_resp";
            
            // Fuelling Point Messages
            public const string FP_STATUS_REQ = "FpStatus_req";
            public const string FP_STATUS_RESP = "FpStatus_resp";
            public const string OPEN_FP_REQ = "open_Fp_req";
            public const string OPEN_FP_RESP = "open_Fp_resp";
            public const string CLOSE_FP_REQ = "close_Fp_req";
            public const string CLOSE_FP_RESP = "close_Fp_resp";
            public const string AUTH_FP_REQ = "auth_Fp_req";
            public const string AUTH_FP_RESP = "auth_Fp_resp";
            
            // Price Messages
            public const string CHANGE_FC_PRICE_SET_REQ = "change_FcPriceSet_req";
            public const string CHANGE_FC_PRICE_SET_RESP = "change_FcPriceSet_resp";
            
            // Tank Messages
            public const string TANK_GAUGE_STATUS_REQ = "TankGaugeStatus_req";
            public const string TANK_GAUGE_STATUS_RESP = "TankGaugeStatus_resp";
            public const string TANK_GAUGE_DATA_REQ = "TankGaugeData_req";
            public const string TANK_GAUGE_DATA_RESP = "TankGaugeData_resp";
            
            // Utility
            public const string CHANGE_FC_STATUS_UPDATE_MODE_REQ = "change_FcStatusUpdateMode_req";
            public const string CHANGE_FC_STATUS_UPDATE_MODE_RESP = "change_FcStatusUpdateMode_resp";
            public const string ECHO_REQ = "echo_req";
            public const string ECHO_RESP = "echo_resp";
            public const string HEARTBEAT = "heartbeat";
            public const string MULTI_MESSAGE_RESP = "MultiMessage_resp";
            public const string REJECT_MESSAGE_RESP = "RejectMessage_resp";
            public const string JPL = "jpl";
        }

        public static class SubCodes
        {
            public const string SUBC_00H = "00H";
            public const string SUBC_01H = "01H";
            public const string SUBC_02H = "02H";
            public const string SUBC_03H = "03H";
        }

        public static class NetworkConstants
        {
            public const int UNENCRYPTED_PORT = 8888;
            public const int ENCRYPTED_TLS_PORT = 8889;
            public const byte STX = 2;
            public const byte ETX = 3;
        }
        
        public static class FuellingPointStates
        {
            public const string Closed = "00H";
            public const string Idle = "01H";
            public const string Calling = "02H";
            public const string Authorized = "03H";
            public const string Fuelling = "04H";
            public const string PreAuthorized = "05H";
            public const string PostPaid = "06H";
            public const string Suspended = "07H";
            public const string Stopped = "08H";
            public const string EndOfFuelling = "09H";
            public const string Reserved = "0AH";
            public const string Inoperative = "0BH";
        }
    }
}
"@

$constantsContent | Out-File -FilePath "Core\JplConstants.cs" -Encoding UTF8

# Message base classes
$messageBaseContent = @"
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace DomsJplProtocol.Core
{
    // Base message class
    public abstract class JplMessage
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("subCode")]
        public string SubCode { get; set; }

        [JsonPropertyName("correlationId")]
        public object CorrelationId { get; set; }
    }

    // Request message base
    public abstract class JplRequest : JplMessage
    {
        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Response message base
    public abstract class JplResponse : JplMessage
    {
        [JsonPropertyName("solicited")]
        public bool Solicited { get; set; }

        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Enumeration wrapper
    public class EnumValue<T>
    {
        [JsonPropertyName("enum")]
        public Dictionary<string, string> Enum { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }
        
        // Helper method to get the enum value as a string
        public string GetValueString()
        {
            return Value;
        }
        
        // Helper method to check if a specific enum value is set
        public bool IsValue(string enumKey)
        {
            if (Enum == null || !Enum.ContainsKey(enumKey))
                return false;
                
            return Enum[enumKey] == Value;
        }
    }

    // Bit flags wrapper
    public class BitFlags
    {
        [JsonPropertyName("value")]
        public int Value { get; set; }

        [JsonPropertyName("bits")]
        public Dictionary<string, int> Bits { get; set; }
        
        // Helper method to check if a specific bit is set
        public bool IsBitSet(string bitName)
        {
            if (Bits == null || !Bits.ContainsKey(bitName))
                return false;
                
            return (Value & Bits[bitName]) != 0;
        }
    }

    // Multi-message container
    public class MultiMessageResponse : JplResponse
    {
        public class MultiMessageData
        {
            [JsonPropertyName("messages")]
            public List<JplResponse> Messages { get; set; }
        }
    }

    // Reject message
    public class RejectMessageResponse : JplResponse
    {
        public class RejectData
        {
            [JsonPropertyName("RejectedExtendedMsgCode")]
            public string RejectedExtendedMsgCode { get; set; }

            [JsonPropertyName("RejectedMsgSubc")]
            public string RejectedMsgSubc { get; set; }

            [JsonPropertyName("RejectCode")]
            public EnumValue<string> RejectCode { get; set; }

            [JsonPropertyName("RejectInfo")]
            public string RejectInfo { get; set; }

            [JsonPropertyName("RejectInfoText")]
            public string RejectInfoText { get; set; }
        }
    }
    
    // Heartbeat message
    public class HeartbeatMessage : JplMessage
    {
        public class HeartbeatData
        {
            // Empty data object
        }
    }
}
"@

$messageBaseContent | Out-File -FilePath "Core\JplMessageBase.cs" -Encoding UTF8

# Network Configuration
$networkConfigContent = @"
using System;
using System.Security.Cryptography.X509Certificates;

namespace DomsJplProtocol.Network
{
    // Connection configuration
    public class PssConnectionConfig
    {
        public string HostName { get; set; } = "localhost";
        public int Port { get; set; } = 8888;
        public bool UseTls { get; set; } = false;
        public int ConnectionTimeoutMs { get; set; } = 30000;
        public int HeartbeatIntervalMs { get; set; } = 20000;
        public int MaxHeartbeatMissedCount { get; set; } = 3;
        public X509Certificate2 ClientCertificate { get; set; }
        public bool ValidateServerCertificate { get; set; } = true;
    }

    // Connection state
    public enum ConnectionState
    {
        Disconnected,
        Connecting,
        Connected,
        LoggedOn,
        Disconnecting,
        Error
    }

    // Event arguments for connection events
    public class ConnectionStateChangedEventArgs : EventArgs
    {
        public ConnectionState OldState { get; set; }
        public ConnectionState NewState { get; set; }
        public string Reason { get; set; }
    }

    public class MessageReceivedEventArgs : EventArgs
    {
        public DomsJplProtocol.Core.JplMessage Message { get; set; }
        public string RawMessage { get; set; }
    }

    public class ErrorEventArgs : EventArgs
    {
        public Exception Exception { get; set; }
        public string Message { get; set; }
        public bool IsFatal { get; set; }
    }
}
"@

$networkConfigContent | Out-File -FilePath "Network\NetworkConfiguration.cs" -Encoding UTF8

# Create PssClient
$pssClientContent = @"
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;

namespace DomsJplProtocol.Network
{
    public class PssClient : IDisposable
    {
        private readonly PssConnectionConfig _config;
        private TcpClient _tcpClient;
        private Stream _stream;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Timer _heartbeatTimer;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplResponse>> _pendingRequests;
        private ConnectionState _connectionState;
        private DateTime _lastMessageReceived;
        private readonly object _stateLock = new object();
        private readonly JsonSerializerOptions _jsonOptions;

        public event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;
        public event EventHandler<MessageReceivedEventArgs> UnsolicitedMessageReceived;
        public event EventHandler<ErrorEventArgs> ErrorOccurred;

        public ConnectionState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _connectionState;
                }
            }
            private set
            {
                lock (_stateLock)
                {
                    var oldState = _connectionState;
                    _connectionState = value;
                    ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs
                    {
                        OldState = oldState,
                        NewState = value
                    });
                }
            }
        }

        public bool IsConnected => State == ConnectionState.Connected || State == ConnectionState.LoggedOn;
        public bool IsLoggedOn => State == ConnectionState.LoggedOn;

        public PssClient(PssConnectionConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _cancellationTokenSource = new CancellationTokenSource();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplResponse>>();
            _connectionState = ConnectionState.Disconnected;
            _lastMessageReceived = DateTime.UtcNow;

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            _heartbeatTimer = new Timer(OnHeartbeatTimer, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (IsConnected)
                return true;

            try
            {
                State = ConnectionState.Connecting;

                _tcpClient = new TcpClient();
                var connectTask = _tcpClient.ConnectAsync(_config.HostName, _config.Port);
                await connectTask;

                if (_config.UseTls)
                {
                    var sslStream = new SslStream(_tcpClient.GetStream(), false, ValidateServerCertificate);
                    var clientCertificates = _config.ClientCertificate != null 
                        ? new X509CertificateCollection { _config.ClientCertificate } 
                        : new X509CertificateCollection();

                    await sslStream.AuthenticateAsClientAsync(_config.HostName, clientCertificates, 
                        System.Security.Authentication.SslProtocols.Tls12, false);
                    _stream = sslStream;
                }
                else
                {
                    _stream = _tcpClient.GetStream();
                }

                State = ConnectionState.Connected;
                _lastMessageReceived = DateTime.UtcNow;

                // Start reading messages
                Task.Run(async () => await ReadMessagesAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                // Start heartbeat timer
                _heartbeatTimer.Change(_config.HeartbeatIntervalMs, _config.HeartbeatIntervalMs);

                return true;
            }
            catch (Exception ex)
            {
                State = ConnectionState.Error;
                OnError(ex, "Failed to connect", true);
                return false;
            }
        }

        public async Task<FcLogonResponse> LogonAsync(string accessCode, string countryCode, string posVersionId, 
            CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Must be connected before logging on");

            var request = new FcLogonRequest
            {
                Name = JplConstants.MessageNames.FC_LOGON_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcLogonRequest.FcLogonData
                {
                    FcAccessCode = accessCode,
                    CountryCode = countryCode,
                    PosVersionId = posVersionId
                }
            };

            var response = await SendRequestAsync<FcLogonResponse>(request, cancellationToken);
            if (response != null)
            {
                State = ConnectionState.LoggedOn;
            }

            return response;
        }

        public async Task<T> SendRequestAsync<T>(JplRequest request, CancellationToken cancellationToken = default) 
            where T : JplResponse
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var correlationId = Guid.NewGuid().ToString();
            request.CorrelationId = correlationId;

            var tcs = new TaskCompletionSource<JplResponse>();
            _pendingRequests[correlationId] = tcs;

            try
            {
                await SendMessageAsync(request, cancellationToken);
                
                using (cancellationToken.Register(() => tcs.TrySetCanceled()))
                {
                    var response = await tcs.Task;
                    return response as T;
                }
            }
            finally
            {
                TaskCompletionSource<JplResponse> removed;
                _pendingRequests.TryRemove(correlationId, out removed);
            }
        }

        public async Task SendMessageAsync(JplMessage message, CancellationToken cancellationToken = default)
        {
            if (!IsConnected)
                throw new InvalidOperationException("Not connected");

            var json = JsonSerializer.Serialize(message, _jsonOptions);
            var messageBytes = Encoding.UTF8.GetBytes(json);
            
            var frame = new byte[messageBytes.Length + 2];
            frame[0] = JplConstants.NetworkConstants.STX;
            Array.Copy(messageBytes, 0, frame, 1, messageBytes.Length);
            frame[frame.Length - 1] = JplConstants.NetworkConstants.ETX;

            await _stream.WriteAsync(frame, 0, frame.Length, cancellationToken);
            await _stream.FlushAsync(cancellationToken);
        }

        public async Task SendHeartbeatAsync(CancellationToken cancellationToken = default)
        {
            var heartbeat = new HeartbeatMessage
            {
                Name = JplConstants.MessageNames.HEARTBEAT,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new HeartbeatMessage.HeartbeatData()
            };

            await SendMessageAsync(heartbeat, cancellationToken);
        }

        public async Task DisconnectAsync()
        {
            State = ConnectionState.Disconnecting;
            
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _cancellationTokenSource?.Cancel();

            try
            {
                _stream?.Close();
                _tcpClient?.Close();
            }
            catch (Exception ex)
            {
                OnError(ex, "Error during disconnect", false);
            }
            finally
            {
                State = ConnectionState.Disconnected;
            }
        }

        private async Task ReadMessagesAsync(CancellationToken cancellationToken)
        {
            var buffer = new byte[8192];
            var messageBuffer = new List<byte>();

            try
            {
                while (!cancellationToken.IsCancellationRequested && IsConnected)
                {
                    var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                    {
                        break;
                    }

                    _lastMessageReceived = DateTime.UtcNow;

                    for (int i = 0; i < bytesRead; i++)
                    {
                        var currentByte = buffer[i];

                        if (currentByte == JplConstants.NetworkConstants.STX)
                        {
                            messageBuffer.Clear();
                        }
                        else if (currentByte == JplConstants.NetworkConstants.ETX)
                        {
                            if (messageBuffer.Count > 0)
                            {
                                var messageJson = Encoding.UTF8.GetString(messageBuffer.ToArray());
                                await ProcessReceivedMessage(messageJson);
                                messageBuffer.Clear();
                            }
                        }
                        else
                        {
                            messageBuffer.Add(currentByte);
                        }
                    }
                }
            }
            catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
            {
                OnError(ex, "Error reading messages", true);
            }
        }

        private async Task ProcessReceivedMessage(string messageJson)
        {
            try
            {
                var messageDocument = JsonDocument.Parse(messageJson);
                var root = messageDocument.RootElement;

                if (root.TryGetProperty("name", out var nameElement) && 
                    root.TryGetProperty("subCode", out var subCodeElement))
                {
                    var name = nameElement.GetString();
                    var subCode = subCodeElement.GetString();
                    
                    bool solicited = false;
                    if (root.TryGetProperty("solicited", out var solicitedElement))
                    {
                        solicited = solicitedElement.GetBoolean();
                    }
                    
                    string correlationId = null;
                    if (root.TryGetProperty("correlationId", out var corrIdElement))
                    {
                        correlationId = corrIdElement.GetString();
                    }
                    
                    // Parse based on message name
                    JplMessage message = null;
                    
                    // This is a simplified version - in a complete implementation,
                    // we would have a message factory to handle all message types
                    switch (name)
                    {
                        case JplConstants.MessageNames.FC_LOGON_RESP:
                            message = JsonSerializer.Deserialize<FcLogonResponse>(messageJson, _jsonOptions);
                            break;
                        case JplConstants.MessageNames.FC_STATUS_RESP:
                            message = JsonSerializer.Deserialize<FcStatusResponse>(messageJson, _jsonOptions);
                            break;
                        case JplConstants.MessageNames.FC_DATE_TIME_RESP:
                        case JplConstants.MessageNames.CHANGE_FC_DATE_TIME_RESP:
                            message = JsonSerializer.Deserialize<FcDateTimeResponse>(messageJson, _jsonOptions);
                            break;
                        case JplConstants.MessageNames.HEARTBEAT:
                            message = JsonSerializer.Deserialize<HeartbeatMessage>(messageJson, _jsonOptions);
                            break;
                        case JplConstants.MessageNames.REJECT_MESSAGE_RESP:
                            message = JsonSerializer.Deserialize<RejectMessageResponse>(messageJson, _jsonOptions);
                            break;
                        default:
                            // Generic response handler for unknown messages
                            message = JsonSerializer.Deserialize<JplResponse>(messageJson, _jsonOptions);
                            break;
                    }

                    if (message != null)
                    {
                        // Notify listeners of all messages
                        MessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                        { 
                            Message = message, 
                            RawMessage = messageJson 
                        });

                        // Complete pending request if this is a response
                        if (!string.IsNullOrEmpty(correlationId))
                        {
                            if (_pendingRequests.TryRemove(correlationId, out var tcs))
                            {
                                if (message is JplResponse response)
                                {
                                    tcs.SetResult(response);
                                }
                            }
                        }
                        // Notify listeners of unsolicited messages
                        else if (!solicited)
                        {
                            UnsolicitedMessageReceived?.Invoke(this, new MessageReceivedEventArgs 
                            { 
                                Message = message, 
                                RawMessage = messageJson 
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                OnError(ex, \$"Error processing message: {messageJson}", false);
            }
        }

        private void OnHeartbeatTimer(object state)
        {
            try
            {
                if (!IsConnected)
                    return;

                var timeSinceLastMessage = DateTime.UtcNow - _lastMessageReceived;
                if (timeSinceLastMessage.TotalMilliseconds > (_config.HeartbeatIntervalMs * _config.MaxHeartbeatMissedCount))
                {
                    OnError(new TimeoutException("Heartbeat timeout"), "Connection lost - no heartbeat", true);
                    return;
                }

                Task.Run(async () =>
                {
                    try
                    {
                        await SendHeartbeatAsync(_cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        OnError(ex, "Failed to send heartbeat", false);
                    }
                });
            }
            catch (Exception ex)
            {
                OnError(ex, "Error in heartbeat timer", false);
            }
        }

        private bool ValidateServerCertificate(object sender, X509Certificate certificate, 
            X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            if (!_config.ValidateServerCertificate)
                return true;

            return sslPolicyErrors == SslPolicyErrors.None;
        }

        private void OnError(Exception exception, string message, bool isFatal)
        {
            ErrorOccurred?.Invoke(this, new ErrorEventArgs
            {
                Exception = exception,
                Message = message,
                IsFatal = isFatal
            });

            if (isFatal)
            {
                State = ConnectionState.Error;
                Task.Run(async () => await DisconnectAsync());
            }
        }

        public void Dispose()
        {
            _heartbeatTimer?.Dispose();
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _stream?.Dispose();
            _tcpClient?.Dispose();
        }
    }
}
"@

$pssClientContent | Out-File -FilePath "Network\PssClient.cs" -Encoding UTF8

# Create Connection Factory
$factoryContent = @"
using System;
using System.Security.Cryptography.X509Certificates;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Network
{
    public class PssConnectionFactory
    {
        public static PssClient CreateClient(PssConnectionConfig config)
        {
            return new PssClient(config);
        }

        public static PssClient CreateUnencryptedClient(string hostname, int port = JplConstants.NetworkConstants.UNENCRYPTED_PORT)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = false
            };
            return new PssClient(config);
        }

        public static PssClient CreateEncryptedClient(string hostname, int port = JplConstants.NetworkConstants.ENCRYPTED_TLS_PORT, 
            X509Certificate2 clientCertificate = null)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = true,
                ClientCertificate = clientCertificate
            };
            return new PssClient(config);
        }
    }
}
"@

$factoryContent | Out-File -FilePath "Network\PssConnectionFactory.cs" -Encoding UTF8

# Create General Messages
$fcLogonMessageContent = @"
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcLogonRequest : JplRequest
    {
        public class FcLogonData
        {
            [JsonPropertyName("FcAccessCode")]
            public string FcAccessCode { get; set; }

            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("PosVersionId")]
            public string PosVersionId { get; set; }

            [JsonPropertyName("FcLogonPars")]
            public FcLogonParameters FcLogonPars { get; set; }
        }

        public class FcLogonParameters
        {
            [JsonPropertyName("UnsolMsgList")]
            public List<UnsolicitedMessage> UnsolMsgList { get; set; }
        }

        public class UnsolicitedMessage
        {
            [JsonPropertyName("ExtMsgCode")]
            public string ExtMsgCode { get; set; }

            [JsonPropertyName("MsgSubc")]
            public string MsgSubc { get; set; }
        }
    }

    public class FcLogonResponse : JplResponse
    {
        public class FcLogonResponseData
        {
            [JsonPropertyName("CountryCode")]
            public string CountryCode { get; set; }

            [JsonPropertyName("FcHwType")]
            public int FcHwType { get; set; }

            [JsonPropertyName("FcHwVersionNo")]
            public string FcHwVersionNo { get; set; }

            [JsonPropertyName("FcSwType")]
            public int FcSwType { get; set; }

            [JsonPropertyName("FcSwVersionNo")]
            public string FcSwVersionNo { get; set; }

            [JsonPropertyName("FcSwDate")]
            public string FcSwDate { get; set; }

            [JsonPropertyName("FcSwBlocks")]
            public List<FcSwBlock> FcSwBlocks { get; set; }

            [JsonPropertyName("UnsolMessages")]
            public List<FcLogonRequest.UnsolicitedMessage> UnsolMessages { get; set; }
        }

        public class FcSwBlock
        {
            [JsonPropertyName("FcSwMainBlockId")]
            public string FcSwMainBlockId { get; set; }

            [JsonPropertyName("FcSwSubBlockId")]
            public string FcSwSubBlockId { get; set; }

            [JsonPropertyName("FcSwBlockReleaseNo")]
            public string FcSwBlockReleaseNo { get; set; }

            [JsonPropertyName("FcSwBlockCheckCode")]
            public string FcSwBlockCheckCode { get; set; }
        }
    }
}
"@

$fcLogonMessageContent | Out-File -FilePath "Messages\General\FcLogonMessage.cs" -Encoding UTF8

$fcStatusMessageContent = @"
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcStatusRequest : JplRequest
    {
        public class FcStatusData
        {
            [JsonPropertyName("NecessaryDevices")]
            public List<NecessaryDevice> NecessaryDevices { get; set; }
        }

        public class NecessaryDevice
        {
            [JsonPropertyName("NecessaryDeviceTypeId")]
            public string NecessaryDeviceTypeId { get; set; }

            [JsonPropertyName("NecessaryDeviceStatus")]
            public string NecessaryDeviceStatus { get; set; }
        }
    }

    public class FcStatusResponse : JplResponse
    {
        public class FcStatusResponseData
        {
            [JsonPropertyName("FcStatus1Flags")]
            public BitFlags FcStatus1Flags { get; set; }

            [JsonPropertyName("FcStatus2Flags")]
            public BitFlags FcStatus2Flags { get; set; }

            [JsonPropertyName("FcServiceMsgSeqNo")]
            public string FcServiceMsgSeqNo { get; set; }

            [JsonPropertyName("FcMasterResetDateAndTime")]
            public string FcMasterResetDateAndTime { get; set; }

            [JsonPropertyName("FcMasterResetCode")]
            public int FcMasterResetCode { get; set; }

            [JsonPropertyName("FcResetDateAndTime")]
            public string FcResetDateAndTime { get; set; }

            [JsonPropertyName("FcResetCode")]
            public string FcResetCode { get; set; }

            [JsonPropertyName("FcStatusPars")]
            public FcStatusParameters FcStatusPars { get; set; }
        }

        public class FcStatusParameters
        {
            [JsonPropertyName("FcShiftNo")]
            public string FcShiftNo { get; set; }

            [JsonPropertyName("FcShiftChangeDateAndTime")]
            public string FcShiftChangeDateAndTime { get; set; }

            [JsonPropertyName("VATRateSeqNo")]
            public string VATRateSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingSeqNo")]
            public string FcRTCSettingSeqNo { get; set; }

            [JsonPropertyName("FcRTCSettingDateAndTime")]
            public string FcRTCSettingDateAndTime { get; set; }

            [JsonPropertyName("FcCurrencySettings")]
            public FcCurrencySettings FcCurrencySettings { get; set; }
        }

        public class FcCurrencySettings
        {
            [JsonPropertyName("CurrencyCode")]
            public string CurrencyCode { get; set; }

            [JsonPropertyName("DecimalPositionInPrice")]
            public int DecimalPositionInPrice { get; set; }

            [JsonPropertyName("DecimalPositionInVol")]
            public int DecimalPositionInVol { get; set; }

            [JsonPropertyName("DecimalPositionInMoney")]
            public int DecimalPositionInMoney { get; set; }
        }
    }
}
"@

$fcStatusMessageContent | Out-File -FilePath "Messages\General\FcStatusMessage.cs" -Encoding UTF8

$fcDateTimeMessageContent = @"
using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcDateTimeRequest : JplRequest
    {
        public class FcDateTimeData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }
        }
    }

    public class FcDateTimeResponse : JplResponse
    {
        public class FcDateTimeResponseData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }

            [JsonPropertyName("LastDateAndTimeSetting")]
            public string LastDateAndTimeSetting { get; set; }
        }
    }
}
"@

$fcDateTimeMessageContent | Out-File -FilePath "Messages\General\FcDateTimeMessage.cs" -Encoding UTF8

$fcStatusUpdateMessageContent = @"
using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class ChangeFcStatusUpdateModeRequest : JplRequest
    {
        public class ChangeFcStatusUpdateModeData
        {
            [JsonPropertyName("StatusUpdateCode")]
            public int StatusUpdateCode { get; set; }
        }
    }

    public class ChangeFcStatusUpdateModeResponse : JplResponse
    {
        public class ChangeFcStatusUpdateModeResponseData
        {
            // Empty response
        }
    }
}
"@

$fcStatusUpdateMessageContent | Out-File -FilePath "Messages\General\FcStatusUpdateMessage.cs" -Encoding UTF8

# Create basic messages for other chapters
$fpStatusMessageContent = @"
using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.DispenseControl
{
    public class FpStatusRequest : JplRequest
    {
        public class FpStatusRequestData
        {
            [JsonPropertyName("FpId")]
            public string FpId { get; set; }
        }
    }

    public class FpStatusResponse : JplResponse
    {
        public class FpStatusResponseData
        {
            [JsonPropertyName("FpId")]
            public string FpId { get; set; }

            [JsonPropertyName("SmId")]
            public string SmId { get; set; }

            [JsonPropertyName("FpMainState")]
            public EnumValue<string> FpMainState { get; set; }

            [JsonPropertyName("FpSubStates")]
            public BitFlags FpSubStates { get; set; }

            [JsonPropertyName("FpLockId")]
            public string FpLockId { get; set; }
        }
    }
}
"@

$fpStatusMessageContent | Out-File -FilePath "Messages\DispenseControl\FpStatusMessage.cs" -Encoding UTF8

$tankStatusMessageContent = @"
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.Wetstock
{
    public class TankGaugeStatusRequest : JplRequest
    {
        public class TankGaugeStatusRequestData
        {
            [JsonPropertyName("TankId")]
            public string TankId { get; set; }
        }
    }

    public class TankGaugeStatusResponse : JplResponse
    {
        public class TankGaugeStatusResponseData
        {
            [JsonPropertyName("TankGaugeSystem")]
            public TankGaugeSystem TankGaugeSystem { get; set; }
            
            [JsonPropertyName("Tanks")]
            public List<Tank> Tanks { get; set; }
        }
        
        public class TankGaugeSystem
        {
            [JsonPropertyName("TankGaugeSystemStatus")]
            public BitFlags TankGaugeSystemStatus { get; set; }
            
            [JsonPropertyName("TankGaugeSystemAlarms")]
            public BitFlags TankGaugeSystemAlarms { get; set; }
        }
        
        public class Tank
        {
            [JsonPropertyName("TankId")]
            public string TankId { get; set; }
            
            [JsonPropertyName("TankStatus")]
            public BitFlags TankStatus { get; set; }
            
            [JsonPropertyName("TankAlarms")]
            public BitFlags TankAlarms { get; set; }
        }
    }
}
"@

$tankStatusMessageContent | Out-File -FilePath "Messages\Wetstock\TankGaugeStatusMessage.cs" -Encoding UTF8

$priceDisplayMessageContent = @"
using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.PriceDisplay
{
    public class PriceDisplayStatusRequest : JplRequest
    {
        public class PriceDisplayStatusRequestData
        {
            [JsonPropertyName("PriceDisplayId")]
            public string PriceDisplayId { get; set; }
        }
    }

    public class PriceDisplayStatusResponse : JplResponse
    {
        public class PriceDisplayStatusResponseData
        {
            [JsonPropertyName("PriceDisplayId")]
            public string PriceDisplayId { get; set; }
            
            [JsonPropertyName("PriceDisplayState")]
            public EnumValue<string> PriceDisplayState { get; set; }
            
            [JsonPropertyName("PriceDisplayStatus")]
            public BitFlags PriceDisplayStatus { get; set; }
        }
    }
}
"@

$priceDisplayMessageContent | Out-File -FilePath "Messages\PriceDisplay\PriceDisplayStatusMessage.cs" -Encoding UTF8

# Create Services
$forecourtServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class ForecourtControllerService
    {
        private readonly PssClient _client;

        public ForecourtControllerService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default)
        {
            var request = new FcStatusRequest
            {
                Name = JplConstants.MessageNames.FC_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcStatusRequest.FcStatusData()
            };

            return await _client.SendRequestAsync<FcStatusResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default)
        {
            var request = new FcDateTimeRequest
            {
                Name = JplConstants.MessageNames.FC_DATE_TIME_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData()
            };

            return await _client.SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default)
        {
            var request = new FcDateTimeRequest
            {
                Name = JplConstants.MessageNames.CHANGE_FC_DATE_TIME_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData
                {
                    FcDateAndTime = dateTime.ToString("yyyyMMddHHmmss")
                }
            };

            return await _client.SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task EnableStatusUpdates(CancellationToken cancellationToken = default)
        {
            var request = new ChangeFcStatusUpdateModeRequest
            {
                Name = JplConstants.MessageNames.CHANGE_FC_STATUS_UPDATE_MODE_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new ChangeFcStatusUpdateModeRequest.ChangeFcStatusUpdateModeData
                {
                    StatusUpdateCode = 3 // Enable all status updates
                }
            };

            await _client.SendRequestAsync<ChangeFcStatusUpdateModeResponse>(request, cancellationToken);
        }
    }
}
"@

$forecourtServiceContent | Out-File -FilePath "Services\ForecourtControllerService.cs" -Encoding UTF8

$dispenseControlServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.DispenseControl;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class DispenseControlService
    {
        private readonly PssClient _client;

        public DispenseControlService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<FpStatusResponse> GetFuellingPointStatusAsync(string fpId, 
            CancellationToken cancellationToken = default)
        {
            var request = new FpStatusRequest
            {
                Name = JplConstants.MessageNames.FP_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FpStatusRequest.FpStatusRequestData
                {
                    FpId = fpId
                }
            };

            return await _client.SendRequestAsync<FpStatusResponse>(request, cancellationToken);
        }
    }
}
"@

$dispenseControlServiceContent | Out-File -FilePath "Services\DispenseControlService.cs" -Encoding UTF8

$wetstockServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.Wetstock;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class WetstockControlService
    {
        private readonly PssClient _client;

        public WetstockControlService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<TankGaugeStatusResponse> GetTankGaugeStatusAsync(string tankId = "00", 
            CancellationToken cancellationToken = default)
        {
            var request = new TankGaugeStatusRequest
            {
                Name = JplConstants.MessageNames.TANK_GAUGE_STATUS_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new TankGaugeStatusRequest.TankGaugeStatusRequestData
                {
                    TankId = tankId
                }
            };

            return await _client.SendRequestAsync<TankGaugeStatusResponse>(request, cancellationToken);
        }
    }
}
"@

$wetstockServiceContent | Out-File -FilePath "Services\WetstockControlService.cs" -Encoding UTF8

$priceDisplayServiceContent = @"
using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.PriceDisplay;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class PriceDisplayService
    {
        private readonly PssClient _client;

        public PriceDisplayService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<PriceDisplayStatusResponse> GetPriceDisplayStatusAsync(string priceDisplayId, 
            CancellationToken cancellationToken = default)
        {
            var request = new PriceDisplayStatusRequest
            {
                Name = "PriceDisplayStatus_req", // Not included in the constants yet
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new PriceDisplayStatusRequest.PriceDisplayStatusRequestData
                {
                    PriceDisplayId = priceDisplayId
                }
            };

            return await _client.SendRequestAsync<PriceDisplayStatusResponse>(request, cancellationToken);
        }
    }
}
"@

$priceDisplayServiceContent | Out-File -FilePath "Services\PriceDisplayService.cs" -Encoding UTF8

# Create test class in the test project
Set-Location ..
Set-Location $TestProjectName

$unitTestContent = @"
using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsJplProtocol.Network;
using DomsJplProtocol.Services;

namespace DomsJplProtocol.Tests
{
    [TestClass]
    public class DomsControllerIntegrationTests
    {
        // NOTE: These tests require a real DOMS controller to be available
        // For unit testing without a controller, you would need to mock the responses
        
        private const string CONTROLLER_IP = "127.0.0.1"; // Change to your controller IP
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task ConnectToController_ShouldSucceed()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                bool connected = await client.ConnectAsync();
                
                // Assert
                Assert.IsTrue(connected);
                Assert.IsTrue(client.IsConnected);
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task LogonToController_ShouldSucceed()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                await client.ConnectAsync();
                var response = await client.LogonAsync(
                    "POS,APPL_ID=TEST,RI", 
                    "0000", 
                    "TEST_1.0");
                
                // Assert
                Assert.IsNotNull(response);
                Assert.IsTrue(client.IsLoggedOn);
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task GetDateTimeFromController_ShouldReturnValidData()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                await client.ConnectAsync();
                await client.LogonAsync("POS,APPL_ID=TEST,RI", "0000", "TEST_1.0");
                
                var service = new ForecourtControllerService(client);
                var dateTimeResponse = await service.GetDateTimeAsync();
                
                // Assert
                Assert.IsNotNull(dateTimeResponse);
                Assert.IsNotNull(dateTimeResponse.Data);
                Assert.IsFalse(string.IsNullOrEmpty(dateTimeResponse.Data.FcDateAndTime));
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
    }
}
"@

$unitTestContent | Out-File -FilePath "DomsControllerIntegrationTests.cs" -Encoding UTF8

# Create mocks for unit testing
$mockTestContent = @"
using System;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.General;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Tests
{
    [TestClass]
    public class MessageSerializationTests
    {
        [TestMethod]
        public void FcLogonRequest_Serialization_ShouldMatch()
        {
            // Arrange
            var request = new FcLogonRequest
            {
                Name = JplConstants.MessageNames.FC_LOGON_REQ,
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new FcLogonRequest.FcLogonData
                {
                    FcAccessCode = "POS,APPL_ID=TEST,RI",
                    CountryCode = "0000",
                    PosVersionId = "TEST_1.0"
                }
            };
            
            // Act
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
            var json = JsonSerializer.Serialize(request, options);
            
            // Assert
            Assert.IsTrue(json.Contains("FcLogon_req"));
            Assert.IsTrue(json.Contains("POS,APPL_ID=TEST,RI"));
            Assert.IsTrue(json.Contains("TEST_1.0"));
        }
        
        [TestMethod]
        public void FcDateTimeResponse_Deserialization_ShouldWork()
        {
            // Arrange
            string json = @"{
                ""name"": ""FcDateAndTime_resp"",
                ""subCode"": ""00H"",
                ""solicited"": true,
                ""data"": {
                    ""FcDateAndTime"": ""20230215123456"",
                    ""LastDateAndTimeSetting"": ""20230215000000""
                }
            }";
            
            // Act
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var response = JsonSerializer.Deserialize<FcDateTimeResponse>(json, options);
            
            // Assert
            Assert.IsNotNull(response);
            Assert.AreEqual(JplConstants.MessageNames.FC_DATE_TIME_RESP, response.Name);
            Assert.AreEqual(JplConstants.SubCodes.SUBC_00H, response.SubCode);
            Assert.IsTrue(response.Solicited);
            Assert.IsNotNull(response.Data);
        }
    }
}
"@

$mockTestContent | Out-File -FilePath "MessageSerializationTests.cs" -Encoding UTF8

# Navigate back to solution directory
Set-Location ..

# Build the solution
Write-Host "Building solution..." -ForegroundColor Blue
dotnet build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Solution created successfully at: $FullSolutionPath" -ForegroundColor Green
    Write-Host "Project structure:" -ForegroundColor Cyan
    Get-ChildItem $FullSolutionPath -Recurse -Name | Where-Object { $_ -like "*.cs" -or $_ -like "*.csproj" -or $_ -like "*.sln" } | Sort-Object
} else {
    Write-Host "Build failed. Please check the errors above." -ForegroundColor Red
}

