﻿using System;
using System.Linq;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using Triquestra.Common.PumpEsm.Controls;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.PumpEsm.Messaging.Event;
using Triquestra.Common.PumpEsm.Messaging.Response;
using Triquestra.Common.PumpEsm.RegulusInterface;
using Triquestra.Common.PumpEsm.Types;
using System.Collections.Generic;
using System.Drawing;
using System.ComponentModel;
using System.Collections.Concurrent;
using Triquestra.Common.PumpEsm.DataAccess;
using Triquestra.Common.Database;
using Triquestra.Common.GUI;
using Triquestra.Common.PumpEsm.Helpers;
using Triquestra.Common.PumpEsm.Builders;
using System.IO;
using Triquestra.Common.GUI.POSThemes;

namespace Triquestra.Common.PumpEsm
{
    public partial class PumpInfoPosFooter : Form
    {
        #region

        public static readonly TimeSpan OFFLINE_TIMEOUT = new TimeSpan(0, 0, 0, 10);
        private static readonly string FUEL_ITEMS_FILE_PATH = Path.Combine(System.Environment.CurrentDirectory, "FuelItems.dat");

        #endregion

        #region Private Elements

        private object _syncRoot = new Object();

        private readonly NLog.Logger _log = NLog.LogManager.GetCurrentClassLogger();
        //TODO read config from the db
        private string _tcpHost = "*************";
        private int _tcpPort = 6050;
        private string _udpHost = "**********";
        private int _udpPort = 8051;
        private int _workstationId = 8;
        internal PetrolSaleItem _activeDelivery;
        private bool _firstRun = true;
        internal List<PetrolSaleItem> _itemsInDelivery = new List<PetrolSaleItem>();
        /// <summary>
        /// All async events coming from Regulus are immediately put into this queue and later processed by timer.
        /// The reason is to ensure the event handlers are executed in the same order they were received.
        /// See the ProcessAsyncQueue() method
        /// </summary>
        private ConcurrentQueue<Action> _procQueue = new ConcurrentQueue<Action>();
        private bool _leftRightToggle = false; // the name of the propery is imprecise...
        private long __lastHeartbeat = DateTime.Now.ToBinary();
        private int __heartbeatRecovered = 1;

        internal readonly IGuiHelper _guiHelper;

        private bool heartbeatRecovered
        {
            get { return System.Threading.Interlocked.Exchange(ref __heartbeatRecovered, __heartbeatRecovered) == 1; }
            set { System.Threading.Interlocked.Exchange(ref __heartbeatRecovered, value ? 1 : 0); }
        }
        //private bool _prepay;

        #region Internals

        internal IPumpDataProvider _pumpDataProvider;

        internal IRegulus _regulus;

        internal IDigifortHelper _digifort;

        /// <summary>
        /// The number of pumps in a single row
        /// </summary>
        internal int PumpsInARow;

        #endregion

        #endregion

        //public PumpInfoPosFooter()
        //{
        //    InitializeComponent();

        //    _guiHelper = new GuiHelper();

        //    PumpsInARow = 8;

        //    AllowSales = true;
            
        //    InitRegulus();

        //    _digifort = new DigifortHelper(IntPtr.Zero);

        //    _pumpDataProvider = new PumpDataProvider();
        //}


        public PumpInfoPosFooter(IPumpDataProvider initData, int screenWidth)
        {
            InitializeComponent();

            _guiHelper = new GuiHelper();
            
            this.Width = screenWidth;

            //CR17090 Req 8: either 12 or 8 pumps
#if DEBUG
            PumpsInARow = 8;
#else
            PumpsInARow = (screenWidth > 1300) ? 12 : 8;
#endif

            BtnStop.KeyClick += BtnStop_Click;
            BtnStopAll.Click += BtnStopAll_Click;
            BtnPlay.KeyClick += BtnPlay_Click;
            BtnPlayAll.Click += BtnPlayAll_Click;
            BtnPrepay.KeyClick += BtnPrepay_Click;
            BtnToggle.Click += BtnToggle_Click;

            AllowSales = true;

            _pumpDataProvider = initData;
            _tcpHost = _pumpDataProvider.TcpAddress;
            _tcpPort = _pumpDataProvider.TcpPort;
            _udpHost = _pumpDataProvider.UdpAddress;
            _udpPort = _pumpDataProvider.UdpPort;
            _workstationId = _pumpDataProvider.WorkstationId;
            _pumpSet = _pumpDataProvider.RetailPumps;
            if (0 == _pumpSet.Count)
                _log.Error("The retail pumps are not configured for the station. Please contact support.");

            var digifortTitle = initData.DigifortTitle;
            var digifortWindow = !string.IsNullOrEmpty(digifortTitle) ? Triquestra.Common.PumpEsm.Base.Native.FindWindow(null, digifortTitle) : IntPtr.Zero;
            if (!string.IsNullOrEmpty(digifortTitle) && digifortWindow == IntPtr.Zero)
                _log.Warn("Unable to find the '{0}' window. Any pump suspend/resume events will not be broadcasted.", digifortTitle);// we're expected to find a Digifort window, but failed.
            _digifort = new DigifortHelper(digifortWindow);

            InitRegulus();

            //B148263 - INFP-691 Fuel delivery not clearing off Pump after POS restart
            var existingDeliveries = ReadExistingFuelItemsFromFile();
            if (existingDeliveries != null && existingDeliveries.Count > 0)
                _itemsInDelivery = existingDeliveries;

		}

		public PumpInfoPosFooter(IPumpDataProvider pumpDataProvider, IRegulus regulus, IDigifortHelper digifortHelper, IGuiHelper guiHelper, int screenWidth)
        {
            InitializeComponent();
            _regulus = regulus;
            _pumpDataProvider = pumpDataProvider;
            _digifort = digifortHelper;
            _guiHelper = guiHelper;

            _workstationId = pumpDataProvider.WorkstationId;

            Width = screenWidth;

            PumpsInARow = 8;
            AllowSales = true;
        }

        internal void BtnPlay_Click(object sender, EventArgs e)
        {
            BtnPlay.Down = !BtnPlay.Down;
            if (BtnPlay.Down && BtnStop.Down)
                BtnStop.Down = false;
            ReturnFocus();
        }

        private void InitRegulus()
        {
            _log.Debug("Connecting to Regulus. (TCP:{0}:{1} UDP:{2}:{3} WS: {4} Type:{5})", _tcpHost, _tcpPort, _udpHost, _udpPort, _workstationId, "PumpController");
            var pumpController = new PumpController(_tcpHost, _tcpPort, _udpHost, _udpPort, _workstationId);
            try
            {
                pumpController.Connect();
            }
            catch (Exception x)
            {
                _log.Error(x.ToString());
#if DEBUG
                MessageBox.Show(x.Message);
#endif
                throw;
            }
            
            _regulus = new Regulus(
               pumpController,//Regulus keeps track of requests by workstation id which is an integer. This needs to be unique per site. 
                    null // we do not need System Manager at POS
                );
            _regulus.PumpController.DispenserStateChanged += PumpController_DispenserStateChanged;
            _regulus.PumpController.DispensersUpdated += PumpController_DispensersUpdated;
            _regulus.PumpController.Connected += PumpController_Connected;
            _regulus.PumpController.NozzleStateChanged += PumpController_NozzleStateChanged;
            _regulus.PumpController.DeliveryInProgress += PumpController_DeliveryInProgress;
            _regulus.PumpController.DispenserModeChanged += PumpController_DispenserModeChanged;
            _regulus.PumpController.DeliveryLocked += PumpController_DeliveryLocked;
            _regulus.PumpController.DeliveryUnlocked += PumpController_DeliveryUnlocked;
            _regulus.PumpController.DeliveryCompleted += PumpController_DeliveryCompleted;
            _regulus.PumpController.DeliveryStateChanged += PumpController_DeliveryStateChanged;
            _regulus.PumpController.ProfilesReceived += PumpController_ProfilesReceived;
            _regulus.PumpController.DeliveryStarted += PumpController_DeliveryStarted;
            _regulus.PumpController.DeliveryDeleted += PumpController_DeliveryDeleted;

            _regulus.PumpController.DispenserDataReceived += PumpController_DispenserDataReceived;
            _regulus.PumpController.DispenserListReceived += PumpController_DispenserListReceived;
            _regulus.PumpController.DeliveryLockReceived += PumpController_DeliveryLockReceived;
            _regulus.PumpController.DeliveryDataReceived += PumpController_DeliveryDataReceived;
            _regulus.PumpController.ReserveReceived += PumpController_ReserveReceived;
            _regulus.PumpController.Heartbeat += PumpController_Heartbeat;
        }


        #region Forecourt Events
        private void PumpController_Heartbeat(object sender, EventArgs e)
        {
            var dt = DateTime.Now;
            var dtb = dt.ToBinary();
            //threadsafe
            var prevDt = System.Threading.Interlocked.Exchange(ref __lastHeartbeat, dtb);            
            //b145685 recover after connection loss
            if (dt.Subtract(DateTime.FromBinary(prevDt)) > OFFLINE_TIMEOUT || !heartbeatRecovered)
            {
                _procQueue.Enqueue(RecoverHeartBeat);
            }
        }

        internal void PumpController_ReserveReceived(object sender, EventArgs e)
        {
            var msg = sender as ReserveResponse;
            if (msg == null)
            {
                return;
            }
            _procQueue.Enqueue(() =>
            {
                _log.Trace($"Picked from queue: PumpController_ReserveReceived. CommandResult: {msg.CommandResult}, DispenserId: {msg.DispenserId}");
                if (msg.CommandResult == ForecourtCommandResults.SUCCESS || msg.CommandResult == ForecourtCommandResults.Pump_Already_Reserved)
                {
                    Delivery delivery;
                    lock (_syncRoot)
                    {
                        delivery = _reservedDelivery;
                        _reservedDelivery = null;
                    }
                    if (delivery == null)
                        return;

                    SendDelivery(delivery);
                }
                else
                {
                    _guiHelper.ShowMessage("Unable to reserve the pump.");
                }
            });

        }

        internal void PumpController_DeliveryLockReceived(object sender, EventArgs e)
        {
            var msg = sender as DeliveryLockResponse;
            if (msg == null)
            {
                return;
            }

            _procQueue.Enqueue(() =>
            {
                _log.Trace($"Picked from queue: PumpController_DeliveryLockReceived. CommandResult: {msg.CommandResult}, DispenserId: {msg.DispenserId}");
                var delivery = _pendingDelivery.Delivery;
                _pendingDelivery = null;

                if (delivery == null)
                    return;
                var msgCommandResult = msg.CommandResult;

                var dispenser =
                    PanelDispensers.Controls.Cast<DispenserUi>()
                        .SingleOrDefault(d => d.DispenserId == msg.DispenserId);
                if (dispenser == null)
                {
#if DEBUG
                    MessageBox.Show("" + msg.DispenserId + " is not found in PumpController_DeliveryLockReceived");
#endif
                    return;
                }

                if (msgCommandResult == ForecourtCommandResults.Delivery_Already_Locked || msgCommandResult == ForecourtCommandResults.SUCCESS)
                {
                    //b146015 display a message when already clicked on the sale.
                    if (msgCommandResult == ForecourtCommandResults.Delivery_Already_Locked)
                    {
                        int idx;
                        lock (_syncRoot)
                            idx = _itemsInDelivery.FindIndex(fi => fi.DeliveryId == delivery.DeliveryId && fi.DispenserId == delivery.DispenserId);

                        if (idx != -1)
                        {
                            _guiHelper.ShowMessage("Delivery is already in a sale", MessageBoxIcon.Information);
                        }
                    }

                    if (!SendDelivery(delivery))
                    {
                        try
                        {
                            _regulus.PumpController.UnlockDelivery(msg.DispenserId, msg.DeliveryId);
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex.ToString());
                            _guiHelper.ShowError("Unable to unlock the delivery: " + ex.Message, MessageBoxIcon.Error);
                        }
                    }
                }
                else
                {
                    _guiHelper.ShowMessage("The delivery is locked by another station", MessageBoxIcon.Error);
                }
                //TODO: add delivery to sale
            });
        }

        private void PumpController_DeliveryCompleted(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryCompletedMessage;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.UpdateDispenserDelivery(msg.Delivery)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace("Picked from queue: PumpController_DeliveryCompleted");
                    var dispenser =
                        PanelDispensers.Controls.Cast<DispenserUi>()
                            .SingleOrDefault(d => d.DispenserId == msg.Delivery.DispenserId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.Delivery.DispenserId + " is not found in PumpController_DeliveryCompleted");
#endif
                        return;
                    }
                    dispenser.UpdateDispenserDelivery(msg.Delivery);


                });
#endif
        }
        private void PumpController_DispenserListReceived(object sender, EventArgs e)
        {
            var msg = sender as ResponseDispenserList;
            if (msg == null)
            {
                MessageBox.Show(@"ResponseDispenserList msg is null ");
                return;
            }
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DispenserListReceived. CommandResult: {msg.CommandResult}");

                    var count = PanelDispensers.Controls.Count;
                    foreach (var pumpId in msg.DispenserList.OrderBy(d => d))
                    {
#if !DEBUG
                        if (!_pumpDataProvider.PumpAvailable(pumpId))
                            continue;
#else
                        
#endif
                        //var child =
                        //    PanelDispensers.Controls.Cast<DispenserUi>()
                        //        .SingleOrDefault(d => d.DispenserId == pumpId);

                        var disp = new DispenserUi { DispenserId = pumpId, Mode = DispenserModes.Undefined, DispenserState = DispenserStates.NOT_RESPONDING };

                        disp.BackColor = PanelDispensers.BackColor;
                        disp.Connect(this);


                        if (count >= MaxPumps)
                            _log.Error("Unable to register the dispenser #{0} - only {1} pumps are supported", pumpId, MaxPumps);
                        else
                        {
                            try
                            {
                                AddDispenser(disp, count);
                                count++;
                            }
                            catch (Exception x)
                            {
                                _log.Error(x.ToString());
                            }

                        }

                    }

                    FinalisePanelDispenser(count);

                });

        }
        private void PumpController_DispenserDataReceived(object sender, EventArgs e)
        {
            var msg = sender as DispenserDataResponse;
            if (msg == null)
            {
                MessageBox.Show(@"DispenserDataResponse msg is null ");
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.DispenserId);

            if (dispenser == null)
            {
                return;
            }

            _regulus.PumpController.GetDeliveryData(dispenser.DispenserId, 0);

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.DispenserState = msg.DispenserState));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DispenserDataReceived. CommandResult: {msg.CommandResult}");
                    foreach (var disp in msg.Dispensers)
                    {
                        var dispenser =
                            PanelDispensers.Controls.Cast<DispenserUi>()
                                .SingleOrDefault(d => d.DispenserId == disp.DispenserId);

                        if (dispenser == null)
                        {
#if DEBUG
                            MessageBox.Show("" + disp.DispenserId + " is not found in PumpController_DispenserDataReceived");
#endif
                            continue;
                        }
                        dispenser.AuthMode = disp.AuthMode;
                        dispenser.DispenserState = disp.State;
                        if (disp.Nozzles.Any(a => a.State == NozzleStates.OUT))
                            dispenser.UpdateNozzleState(0, NozzleStates.OUT);
                        else
                            dispenser.UpdateNozzleState(0, NozzleStates.IN);

                        QueryDeliveryInformation(dispenser.DispenserId, 0);                        
                    }
                });
#endif
        }


        void PumpController_ProfilesReceived(object sender, EventArgs e)
        {
            var msg = sender as ResponseProfilesConfig;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"ProfilesReceived msg is null ");
#endif
                return;
            }
            _log.Debug(() =>
            {

                return "CurrentProfileIndex=" + msg.CurrentProfileIndex + Environment.NewLine +
                    "Profiles=" + string.Join(",", msg.Profiles.Select(p => p.Name).ToArray());
            });
#if DEBUG
            MessageBox.Show("CurrentProfileIndex=" + msg.CurrentProfileIndex + Environment.NewLine +
                            "Profiles=" + string.Join(",", msg.Profiles.Select(p => p.Name).ToArray()));
#endif
        }

        void PumpController_DispenserStateChanged(object sender, EventArgs e)
        {
            var msg = sender as EventDispenserStateChangeMessage;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"DispenserStateChanged msg is null");
#endif
                return;
            }

#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);

            if (dispenser == null)
            {
                return;
            }
            dispenser.AuthMode = msg.AuthMode;

            dispenser.Invoke((MethodInvoker)
                (() =>
                {
                    dispenser.DispenserState = msg.DispenserState;                    
                }
                ));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DispenserStateChanged. DispenserId: {msg.DispenserId}");

                    var dispenser =
                        PanelDispensers.Controls.Cast<DispenserUi>()
                            .SingleOrDefault(d => d.DispenserId == msg.DispenserId);

                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.DispenserId + " is not found in PumpController_DispenserStateChanged");
#endif
                        return;
                    }
                    dispenser.AuthMode = msg.AuthMode;
                    dispenser.DispenserState = msg.DispenserState;
                });
#endif
        }

        void PumpController_DispensersUpdated(object sender, EventArgs e)
        {
            //var pumpSet = _pumpDataProvider.RetailPumps;
#if OLD_WAY
            PanelDispensers.Invoke((MethodInvoker)(()=>
            {
                var count = PanelDispensers.Controls.Count;
                foreach (var pump in _regulus.PumpController.Dispensers.OrderBy(i=>i.DispenserId))
                {
#if !DEBUG
                    if (!_pumpDataProvider.PumpAvailable(pump.DispenserId))
                        continue;
#endif          
                    var child =
                        PanelDispensers.Controls.Cast<DispenserUi>()
                            .SingleOrDefault(d => d.DispenserId == pump.DispenserId);

                    if (child != null)
                    {                    
                        continue;
                    }
                    
                    var disp = new DispenserUi { DispenserId = pump.DispenserId, Mode = pump.DispenserMode };
                    disp.Connect(this);

                    foreach (var grade in pump.Blends)
                    {
                        disp.AddBlend(grade);
                    }

                    try
                    {
                        AddDispenser(disp, count);
                        count++;
                    }
                    catch (Exception x)
                    {
                        _log.Error(x.ToString());
                    }                
                    _regulus.PumpController.GetDispenserData(pump.DispenserId);
                }

                FinalisePanelDispenser(count);
            }));
#else
            _procQueue.Enqueue(() =>
            {
                _log.Trace("Picked from queue: PumpController_DispensersUpdated");
                if (_firstRun)
                {
                    _firstRun = false;

                    var count = PanelDispensers.Controls.Count;
                    foreach (var pump in _regulus.PumpController.Dispensers.OrderBy(i => i.DispenserId))
                    {
                        var disp =
                            PanelDispensers.Controls.Cast<DispenserUi>()
                                .SingleOrDefault(d => d.DispenserId == pump.DispenserId);

                        if (disp == null)
                        {
#if DEBUG
                            MessageBox.Show("" + pump.DispenserId + " is not found in PumpController_DispensersUpdated");
#endif
                            continue;
                        }
                        if (disp.Mode == DispenserModes.Undefined)
                        {
                            disp.DispenserState = DispenserStates.IDLE;
                        }
                        disp.Mode = pump.DispenserMode;

                        foreach (var grade in pump.Blends)
                        {
                            disp.AddBlend(grade);
                        }
                    }
                }

            });
#endif
        }


        void PumpController_Connected(object sender, EventArgs e)
        {

        }

        void PumpController_NozzleStateChanged(object sender, EventArgs e)
        {
            var msg = sender as EventNozzleStateChangeMessage;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.UpdateNozzleState(msg.NozzleId, msg.NozzleState)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_NozzleStateChanged. DispenserId: {msg.DispenserId}");
                    var dispenser =
                       PanelDispensers.Controls.Cast<DispenserUi>()
                           .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.SourceId + " is not found in PumpController_NozzleStateChanged");
#endif
                        return;
                    }

                    dispenser.UpdateNozzleState(msg.NozzleId, msg.NozzleState);
                });
#endif

        }

        void PumpController_DispenserModeChanged(object sender, EventArgs e)
        {
            var msg = sender as EventDispenserModeChangedMessage;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"DispenserModeChanged msg is null ");
#endif
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.Mode = msg.Mode));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace("Picked from queue: PumpController_DispenserModeChanged");
                    var dispenser =
                           PanelDispensers.Controls.Cast<DispenserUi>()
                               .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.SourceId + " is not found in PumpController_DispenserModeChanged");
#endif
                        return;
                    }
                    dispenser.Mode = msg.Mode;
                });
#endif
        }

        # region Delivery Events

        void PumpController_DeliveryDeleted(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryDeletedMessage;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.DeleteDelivery(msg.DeliveryId)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DeliveryDeleted. DispenserId: {msg.DispenserId}");
                    var dispenser =
                        PanelDispensers.Controls.Cast<DispenserUi>()
                            .SingleOrDefault(d => d.DispenserId == msg.DispenserId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.DispenserId + " is not found in PumpController_DeliveryDeleted");
#endif
                        return;
                    }
                  
                    dispenser.DeleteDelivery(msg.DeliveryId);
                    
                    var res = _itemsInDelivery.RemoveAll(ra => ra.DispenserId == msg.DispenserId && ra.DeliveryId == msg.DeliveryId);
                    //WriteFuelItemsToFile(_itemsInDelivery);
                    //may happen that other ESM requested the delivery deletion.
                    if (res != 0)
                        _log.Trace($"The delivery is deleted unexpectedly. DispenserId: {msg.DispenserId}");
                });
#endif
        }

        //void PumpController_DeliveryCleared(object sender, EventArgs e)
        //{
        //    var msg = sender as EventDeliveryClearedMessage;
        //    if (msg == null)
        //    {
        //        return;
        //    }
        //    _procQueue.Enqueue(() =>
        //    {
        //        //this event may be triggered from different ESMs
        //        _itemsInDelivery.RemoveAll(ra => ra.DispenserId == msg.DispenserId && ra.DeliveryId == msg.DeliveryId);                
        //    });

        //}

        void PumpController_DeliveryDataReceived(object sender, EventArgs e)
        {
            var msg = sender as DeliveryDataResponse;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.TargetId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => {
            dispenser.StartDelivery(msg.Delivery)));
#else
            _procQueue.Enqueue(() =>
            {
                _log.Trace($"Picked from queue: PumpController_DeliveryDataReceived. CommandResult: {msg.CommandResult}");
                var dispenser =
                    PanelDispensers.Controls.Cast<DispenserUi>()
                        .SingleOrDefault(d => d.DispenserId == msg.TargetId);
                if (dispenser == null)
                {
#if DEBUG
                    MessageBox.Show("" + msg.TargetId + " is not found in PumpController_DeliveryDataReceived");
#endif
                    return;
                }
                //b145995 the current delivery should be last.
                msg.Delivery.Sort((a, b) => (a.IsCurrent || b.IsCurrent) ? (a.IsCurrent ? 1 : -1) : (a.DeliveryId - b.DeliveryId));
                msg.Delivery.ForEach(dispenser.StartDelivery);
                dispenser.DispenserState = msg.DispenserState;
            });
#endif
        }

        internal void PumpController_DeliveryStarted(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryStartedMessage;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.StartDelivery(msg.Delivery)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace("Picked from queue: PumpController_DeliveryStarted");
                    var dispenser = PanelDispensers.Controls.Cast<DispenserUi>()
        .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.SourceId + " is not found in PumpController_DeliveryStarted");
#endif
                        return;
                    }
                    dispenser.StartDelivery(msg.Delivery);
                });
#endif
        }

        internal void PumpController_DeliveryStateChanged(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryStateChangeMessage;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"DeliveryStateChanged msg is null ");
#endif
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                //MessageBox.Show(@"dispenser is null for SourceId=" + msg.SourceId);
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.DeliveryState = msg.DeliveryState));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DeliveryStateChanged. DispenserId: {msg.DispenserId}");
                    var dispenser = PanelDispensers.Controls.Cast<DispenserUi>()
                        .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show(@"dispenser is null for SourceId=" + msg.SourceId + " in PumpController_DeliveryStateChanged");
#endif
                        return;
                    };
                    dispenser.SetDeliveryState(msg.DeliveryId, msg.DeliveryState);
                });
#endif
        }

        void PumpController_DeliveryLocked(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryLockedMessage;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"DeliveryLocked msg is null ");
#endif
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.LockDelivery(msg.DeliveryId)));
#else
            _procQueue.Enqueue(() =>
                {
                    var dispenser =
               PanelDispensers.Controls.Cast<DispenserUi>()
                   .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
                        return;
                    }

                    var delivery = dispenser.GetDelivery(msg.DeliveryId);
                    if (delivery != null)
                        delivery.Owner = msg.WorkstationId;
                });
#endif

        }

        void PumpController_DeliveryUnlocked(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryUnlockedMessage;
            if (msg == null)
            {
#if DEBUG
                MessageBox.Show(@"DeliveryLocked msg is null ");
#endif
                return;
            }
#if OLD_WAY
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == msg.SourceId);
            if (dispenser == null)
            {
                return;
            }

            dispenser.Invoke((MethodInvoker)
                (() => dispenser.UnlockDelivery(msg.DeliveryId)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DeliveryUnlocked. DeliveryId: {msg.DeliveryId}");
                    var dispenser =
               PanelDispensers.Controls.Cast<DispenserUi>()
                   .SingleOrDefault(d => d.DispenserId == msg.SourceId);
                    if (dispenser == null)
                    {
#if DEBUG
                        MessageBox.Show("" + msg.SourceId + " is not found in PumpController_DeliveryUnlocked");
#endif
                        return;
                    }
                    dispenser.UnlockDelivery(msg.DeliveryId);
                });
#endif
        }

        void PumpController_DeliveryInProgress(object sender, EventArgs e)
        {
            var msg = sender as EventDeliveryProgressMessage;
            if (msg == null)
            {
                return;
            }
#if OLD_WAY
            var dispenser = GetDispenserById(msg.SourceId);
            var delivery =
                dispenser.DeliveryList.SingleOrDefault(
                    d => d.DispenserId == dispenser.DispenserId && d.DeliveryId == msg.DeliveryId);
            if (delivery == null)
            {
                delivery = new Delivery
                {
                    DeliveryId = msg.DeliveryId,
                    Amount = msg.Amount,
                    BlendId = dispenser.Blends.SingleOrDefault(b => b.NozzleId == msg.NozzleId).BlendId,
                    DispenserId = dispenser.DispenserId,
                    IsCurrent = true,
                    Limit = 0,
                    LimitType = LimitTypes.NONE,
                    Mode = AuthModes.None,
                    NozzleId = msg.NozzleId,
                    Volume = msg.Volume,
                    State = DeliveryStates.DELIVERING
                };
                dispenser.Invoke((MethodInvoker)
                    (() => dispenser.StartDelivery(delivery)));
                return;
                //throw new InstanceNotFoundException("Cannot find delivery id=" + msg.DeliveryId);
            }
            delivery.Amount = msg.Amount;
            delivery.Volume = msg.Volume;


            dispenser.Invoke((MethodInvoker)
                (() => dispenser.UpdateDispenserDelivery(delivery)));
#else
            _procQueue.Enqueue(() =>
                {
                    _log.Trace($"Picked from queue: PumpController_DeliveryInProgress. DeliveryId: {msg.DeliveryId}");
                    var dispenser = GetDispenserById(msg.SourceId);
                    if (dispenser == null)
                        return;

                    IDelivery delivery =
                        dispenser.GetDelivery(msg.DeliveryId);
                    if (delivery == null)
                    {
#if false
                        var blend = dispenser.Blends.SingleOrDefault(b => b.NozzleId == msg.NozzleId);
                        if (blend != null)
                        {
                            delivery = new Delivery
                            {
                                DeliveryId = msg.DeliveryId,
                                Amount = msg.Amount,
                                BlendId = blend.BlendId,
                                DispenserId = dispenser.DispenserId,
                                IsCurrent = true,
                                Limit = 0,
                                LimitType = LimitTypes.NONE,
                                Mode = AuthModes.None,
                                NozzleId = msg.NozzleId,
                                Volume = msg.Volume,
                                State = DeliveryStates.DELIVERING
                            };
                            dispenser.StartDelivery(delivery);
                        }
#endif
                    }
                    else
                    {
                        delivery.Amount = msg.Amount;
                        delivery.Volume = msg.Volume;
                        //dispenser.UpdateDispenserDelivery(delivery);
                    }
                });
#endif
        }

        #endregion

        #endregion

        private DispenserUi GetDispenserById(int id)
        {
            var dispenser =
                PanelDispensers.Controls.Cast<DispenserUi>()
                    .SingleOrDefault(d => d.DispenserId == id);
            if (dispenser == null)
            {
#if DEBUG
                MessageBox.Show("" + id + " is not found");
#endif
                //throw new InstanceNotFoundException("Dispenser not found for id " + id);
            }
            return dispenser;
        }

        #region Pagination
        public bool CurrentPage { get { return _leftRightToggle; } }
        private void BtnToggle_Click(object sender, EventArgs e)
        {
            _leftRightToggle = (!_leftRightToggle) && PanelDispensers.Controls.Count > PumpsInARow;
            for (int ctrlIdx = 0; ctrlIdx < PanelDispensers.Controls.Count; ctrlIdx++)
            {
                PanelDispensers.Controls[ctrlIdx].Visible = (GetDispenserPage(ctrlIdx) == _leftRightToggle);
            }
            BtnToggle.DisplayValue = _leftRightToggle ? "<=" : "=>";
            ReturnFocus();
        }
        private int _leftMostX = 0;
        private void AddDispenser(DispenserUi disp, int count)
        {
            AllocateDispenser(disp, count);

            Update();
        }

        private void AllocateDispenser(DispenserUi disp, int count)
        {
            if ((count % PumpsInARow) == 0)
                _leftMostX = 0;

            var part = GetDispenserPage(count);
            disp.Visible = (part == _leftRightToggle);
            disp.Location = new Point(_leftMostX, disp.Margin.Top);
            _leftMostX += disp.Size.Width + disp.Margin.Horizontal;
            PanelDispensers.Controls.Add(disp);
        }

        private void FinalisePanelDispenser(int count)
        {
            BtnToggle.Visible = (count > PumpsInARow);
        }

        public bool GetDispenserPage(int count)
        {
            return ((count / PumpsInARow) == 1);
        }

        public bool GetDispenserPage(DispenserUi ctrl)
        {
            var idx = PanelDispensers.Controls.IndexOf(ctrl);
            return GetDispenserPage(idx);
        }

        #endregion

        public void TapPump(int dispenserId)
        {
            _log.Trace($"User tapped on pump. DispenserId: {dispenserId} ");
            var dispenser = GetDispenserById(dispenserId);

            if (BtnStop.Down)
            {
                BtnStop.Down = false;

                if (dispenser.DispenserState != DispenserStates.SUSPENDED)
                {
                    try
                    {
                        DoSuspend(dispenserId);
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.ToString());
                        _guiHelper.ShowError("Unable to suspend the pump: " + ex.Message, MessageBoxIcon.Error);
                    }
                }
            }
            else if (BtnPlay.Down)
            {
                BtnPlay.Down = false;

                if (dispenser.DispenserState == DispenserStates.SUSPENDED)
                {
                    try
                    {
                        DoResume(dispenserId);                     
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.ToString());
                        _guiHelper.ShowError("Unable to resume the pump: " + ex.Message, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                string action = string.Empty;
                try
                {
                    if (dispenser.DispenserState == DispenserStates.CALLING)
                    {
                        action = "authorise";
                        _regulus.PumpController.Authorize(dispenserId);
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex.ToString());
                    _guiHelper.ShowError("Unable to " + action + " the pump: " + ex.Message, MessageBoxIcon.Error);
                }
            }
            ReturnFocus();
        }
                
        /// <summary>
        /// Build and shows prepay form if no current prepay form
        /// </summary>
        /// <param name="dispenser">Dispenser UI control</param>
        /// <returns>Returns true if new form created or false if we already have prepay form</returns>
        public bool TapForPrepay(DispenserUi dispenser)
        {
            if (PrepayForm != null)
            {
                _log.Debug("User tapped on pump and want to prepay, but he's already have another prepay window active");
                return false;
            }

            _log.Debug("User tapped on pump and want to prepay");

            var form = new WinFormUi.Controls.PrepayForm();
            form.SetDispenser(dispenser.DispenserId);

            form.SetGuiMode(this.GuiMode);
            if (this.GuiMode != Common.GUI.Mode.Standard)
            {
                form.StartPosition = FormStartPosition.Manual;
                form.Location = new Point(this.Size.Width - form.Size.Width, 0);
            }
            else
                form.StartPosition = FormStartPosition.CenterScreen;

            form.AddDenom(PumpDataProvider.Denominations);
            //M158250: when there are multiple hoses (on a pump) using a same grade, then the Prepay Form will only display one grade button with the first hose
            form.AddBlends(dispenser.Blends.GroupBy(t => new {t.BlendId}).Select(s => s.FirstOrDefault()));

            this.PrepayForm = form;
            form.Show(this);
            form = null;

            return true;         
        }
        
        public void ApplyPrepay(IPrepay prepay)
        {
            if (prepay != null)
            {
                Items item = _pumpDataProvider.GetItemByBlendId(prepay.Blend.BlendId);
                Fuel_Pump pumpInfo = null;
                int? nozzleId = null;
                int? tankId = null;
                if (item != null)
                {
                    var nozzleInfo = _pumpDataProvider.Nozzles.SingleOrDefault(s => s.PumpID == prepay.DispenserId && 
                        s.DeviceID.ToInteger() == prepay.Blend.NozzleId && string.Equals(s.UPC, item.UPC, StringComparison.OrdinalIgnoreCase));
                    pumpInfo = _pumpDataProvider.Dispensers.SingleOrDefault(s => s.ID == prepay.DispenserId);
                    if (nozzleInfo != null && pumpInfo != null)
                    {
                        tankId = nozzleInfo.TankID;

                        if (nozzleInfo.ID == pumpInfo.H1)
                            nozzleId = 1;
                        else
                            if (nozzleInfo.ID == pumpInfo.H2)
                                nozzleId = 2;
                            else
                                if (nozzleInfo.ID == pumpInfo.H3)
                                    nozzleId = 3;
                                else
                                    if (nozzleInfo.ID == pumpInfo.H4)
                                        nozzleId = 4;
                    }
                }
                if (nozzleId.HasValue)
                {
                    var delivery = DeliveryBuilder.Build(tankId, prepay, item);

                    SetReservedDelivery(delivery);
                    try
                    {
                        _regulus.PumpController.Reserve(_workstationId, prepay.DispenserId, prepay.Amount, prepay.Blend);
                    }
                    catch (Exception ex)
                    {
                        _log.Error(ex.ToString());
                        _guiHelper.ShowError("Unable to reserve the pump: " + ex.Message, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    if (item == null)
                    {
                        string txt = string.Format("The UPC for the BlendID {0} is not found. Please contact support.", prepay.Blend.BlendId);
                        _log.Error(txt);
                        _guiHelper.ShowMessage(txt, MessageBoxIcon.Error);
                    }
                    else
                    {
                        if (pumpInfo == null)
                        {
                            string txt = string.Format("The nozzle configuration is not found for the \"{0}\" ({1}) fuel pump #{2}. Please contact support.", item.Description, item.UPC, prepay.DispenserId);
                            _log.Error(txt);
                            _guiHelper.ShowMessage(txt, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }

        private void SetReservedDelivery(Delivery delivery)
        {
            lock (_syncRoot)
                _reservedDelivery = delivery;
        }

        private void WaitForResponse()
        {
            System.Threading.Thread.Sleep(100);
            ProcessAsyncQueue();
        }

        public void SendDeliveryAsync(IDelivery delivery)
        {
            // check the latest delivery state in case the delivery is selected from the stack form and it's already locked by another station
            var pumpUi = PanelDispensers.Controls.Cast<DispenserUi>().Where(p => p.DispenserId == delivery.DispenserId).SingleOrDefault();
            var origDelivery = pumpUi.DeliveryList.Where(d => d.DeliveryId == delivery.DeliveryId).SingleOrDefault();
            if ( origDelivery!= null && origDelivery != delivery && (origDelivery.State == DeliveryStates.FINALIZING || origDelivery.State == DeliveryStates.REFUND_FINALIZING))
            {
                Dialogs.ShowMessage(GuiMode, "The delivery has already been locked by a station!", "", MessageBoxButtons.OK, MessageBoxIcon.Warning, true);
                return;
            }

            //b147060 if we have not received any response on delivery lock within the period - repeat the operation
            if (_pendingDelivery == null || _pendingDelivery.IsExpired())
            {
                _log.Trace("Attempting to lock the delivery");
				try
				{
					_regulus.PumpController.LockDelivery(delivery.DispenserId, delivery.DeliveryId);
					_pendingDelivery = new PendingDelivery(delivery);
				}
				catch (Exception ex)
				{
					_log.Error(ex.ToString());
					_guiHelper.ShowError("Unable to lock the delivery: " + ex.Message, MessageBoxIcon.Error);
				}				
            }
            else
            {
                // previous operation is in progress
                System.Media.SystemSounds.Beep.Play();
            }
            // the actual add to the sale should happen in the response on the LockDelivery
        }
        
        public bool SendDelivery(IDelivery delivery)
        {
            int idx;
            lock(_syncRoot)
                idx =_itemsInDelivery.FindIndex(fi => fi.DeliveryId == delivery.DeliveryId && fi.DispenserId == delivery.DispenserId
                                                   && fi.NozzleId == delivery.NozzleId);
            if (-1 == idx)
            {
                var upc = _pumpDataProvider.GetUPCforBlendId(delivery.BlendId);

                if (!string.IsNullOrEmpty(upc))
                {
                    _activeDelivery = new PetrolSaleItem(upc,delivery);// the sale ID will be assigned later

                    SendScanCode(upc);
                    return true;
                }
                else
                {
                    _log.Error("Unknown blendID {0} in a call to SendDelivery", delivery.BlendId);
                    return false;
                }
            }
            else
                return true;
        }

        public PetrolSaleItem ActiveDelivery
        {
            get { lock (_syncRoot) { return _activeDelivery; } }
        }

        public const uint WM_APP = 0x8000;
        public const int WM_MOUSEMOVE = 0x200;
        public const int WM_KEYDOWN = 0x100;
        public const int WM_KEYUP = 0x101;
        public const int VK_RETURN = 0x0D;

        private HashSet<int> _pumpSet;
        /// <summary>
        /// Maximum pumps allowed
        /// </summary>
        private int MaxPumps { get { return PumpsInARow * 2; } }
        internal PendingDelivery _pendingDelivery;
        private Delivery _reservedDelivery;

        public IPumpDataProvider PumpDataProvider { get { return _pumpDataProvider; } }
        public Action<string> SendScanCode { get; set; }
        public IntPtr MainActiveWindow { get; set; }
        private void BtnStop_Click(object sender, EventArgs e)
        {
            BtnStop.Down = !BtnStop.Down;
            if (BtnPlay.Down && BtnStop.Down)
                BtnPlay.Down = false;
            ReturnFocus();
        }

        private void BtnStopAll_Click(object sender, EventArgs e)
        {
            if (_posMode != POSModeType.Normal)
            {
                return;
            }
            try
            {
                //CR17090 Req 1: add prompt message
                if (_guiHelper.ShowMessage("This action will stop all pumps. Proceed?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    DoSuspendAll();
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                _guiHelper.ShowError("Unable to suspend all pumps: " + ex.Message, MessageBoxIcon.Error);
            }
			ReturnFocus();
        }

        /// <summary>
        /// Suspend all pumps
        /// </summary>
        public void DoSuspendAll()
        {
            _regulus.PumpController.SuspendAll();
            _pumpDataProvider.ReportPumpSuspendAction(true, 0);
            _digifort.NotifySuspend(true);
        }
        /// <summary>
        /// Suspend a separate pump
        /// </summary>
        /// <param name="pumpNo"></param>
        public void DoSuspend(int dispenserId)
        {
            _regulus.PumpController.Suspend(dispenserId);
            _pumpDataProvider.ReportPumpSuspendAction(true, dispenserId);
            _digifort.NotifySuspend(true, dispenserId);
        }

        /// <summary>
        /// Resume all pumps
        /// </summary>
        public void DoResumeAll()
        {
            _regulus.PumpController.ResumeAll();
            _pumpDataProvider.ReportPumpSuspendAction(false, 0);
            _digifort.NotifySuspend(false);
        }
        /// <summary>
        /// Resume a separate pump
        /// </summary>
        /// <param name="pumpNo"></param>
        public void DoResume(int dispenserId)
        {
            _regulus.PumpController.Resume(dispenserId);
            _pumpDataProvider.ReportPumpSuspendAction(false, dispenserId);
            _digifort.NotifySuspend(false, dispenserId);
        }        

        private void BtnPlayAll_Click(object sender, EventArgs e)
        {
            try
            {
                DoResumeAll();
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                _guiHelper.ShowError("Unable to resume all pumps: " + ex.Message, MessageBoxIcon.Error);
            }
            ReturnFocus();
        }

        internal void BtnPrepay_Click(object sender, EventArgs e)
        {
            if (!AllowSales)
                return;
            if (PrepayForm == null)
                BtnPrepay.Down = !BtnPrepay.Down;
            ReturnFocus();
        }

        private void tmrWakeUp_Tick(object sender, EventArgs e)
        {
            ProcessAsyncQueue();
            //this.BringToFront();
            //if (this.Enabled)
            //{
            //    //if (!AllowSales) ;
            //    //EnableWindow(this.Handle, 1);
            //}
            //else
            //    this.Text = sender.ToString();
        }

        internal void ProcessAsyncQueue()
        {
            int actionListCount = 0;
            Action action;
            while (actionListCount++ < 100 && _procQueue.TryDequeue(out action))
            { action(); }
        }

        private POSModeType _posMode;
        public POSModeType PosMode
        {
            set
            {
                lock (_syncRoot)
                    _posMode = value;
            }
        }

        private bool _allowSales;

        public bool AllowSales
        {
            get
            {
                lock (_syncRoot)
                    return (_allowSales && (_posMode == POSModeType.Normal));
            }
            set
            {
                lock (_syncRoot)
                    _allowSales = value;
            }

        }

        [DllImport("user32.dll")]
        public static extern int EnableWindow(IntPtr handle, int bEnable);

        // for debug purposes
        //private void button1_Click(object sender, EventArgs e)
        //{
        //    MessageBox.Show("button1");
        //    SendDelivery(new Delivery()
        //    {
        //        Amount = 10.462m,
        //        BlendId = 10,
        //        DeliveryId = 0,
        //        DispenserId = 0,
        //        IsCurrent = true,
        //        Limit = 10m,
        //        LimitType = LimitTypes.NONE,
        //        Mode = AuthModes.POSTPAY,
        //        Price = 1.885m,
        //        PriceId = 1,
        //        PriceLevel = 1,
        //        TankId = 0,
        //        Volume = 5.55m
        //    });
        //}
        int pumpHandle = 0;
        private int GetNextPumpHandle()
        {
            return ++pumpHandle;
        }
        internal bool AddDeliveryToList()
        {
            lock (_syncRoot)
            {
                if (-1 == _itemsInDelivery.FindIndex(fi => fi.DeliveryId == _activeDelivery.DeliveryId && fi.DispenserId == _activeDelivery.DispenserId))
                {
                    //m149000 assign unique pump handle for each delivery
                    int handle = GetNextPumpHandle();
                    while (_itemsInDelivery.FindIndex(fi => fi.Handle == handle) != -1) { handle = GetNextPumpHandle(); }

                    _activeDelivery.Handle = handle;

                    _itemsInDelivery.Add(_activeDelivery);
                    WriteFuelItemsToFile(_itemsInDelivery);
                    return true;
                }
            }
            return false;
        }

        internal bool ProcessDelivery(int handle, bool voidLine)
        {
            PetrolSaleItem item;
            lock (_syncRoot)
                item = _itemsInDelivery.Find(f => f.Handle == handle);
            bool res = true;
            if (item != null)
            {
                res = ProcessDelivery(item, voidLine);
            }
            else
            {
                _log.Error("Unknown delivery reference {0} passed in ProcessDelivery", handle);
                // Need to clear the FuelItems file when there is no tank deliveries in the sale,
                // so that the sale (when there are other items) can still be completed after the fuel item is voided (as this method gets called when the fuel item is voided)
                WriteFuelItemsToFile(_itemsInDelivery);
                res = false;
            }
            return res;
        }
        /// <summary>
        /// Process delivery
        /// </summary>
        /// <param name="item">Delivery to process</param>
        /// <param name="voidLine">True - release the delivery (Cancel prepay/release postpay), False - activate (authorise prepay/complete postpay)</param>
        /// <returns>True - the action was successfull, false - overwise</returns>
        private bool ProcessDelivery(PetrolSaleItem item, bool voidLine)
        {
            try
            {
                if (!voidLine)
                {
                    if (IsPrepaySaleItem(item)) // a special case of our artificial delivery
                    {
                        var pump = GetDispenserById(item.DispenserId);
                        if (pump == null)
                        {
                            return false;
                        }
                        var blend = pump.Blends.SingleOrDefault(b => b.BlendId == item.BlendId && b.NozzleId == item.NozzleId);
                        if (blend == null)
                        {
                            return false;
                        }
                        PostProcessing.AfterPrepayComplete.PostPrepayTransaction(this.Receipt, item);
                        try
                        {
                            if (pump.DispenserState != DispenserStates.RESERVED)
                            {
                                _log.Error("The reservation is cancelled on pump#{0}. The current status is {1}.", item.DispenserId, pump.DispenserState);
                                _regulus.PumpController.Reserve(_workstationId, item.DispenserId, item.Amount, blend);
                            }
                            _regulus.PumpController.Authorize(item.DispenserId, item.Amount, blend);
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex.ToString());
                            _guiHelper.ShowError("Unable to authorise the pump: " + ex.Message, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        try
                        {
                            _regulus.PumpController.ClearDelivery(item.DispenserId, item.DeliveryId);
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex.ToString());
                            _guiHelper.ShowError("Unable to clear the delivery: " + ex.Message, MessageBoxIcon.Error);
                        }
                    }
                }
                else // void the line
                {
                    if (IsPrepaySaleItem(item))
                    {
                        try
                        {
                            _regulus.PumpController.Abort(item.DispenserId);
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex.ToString());
                            _guiHelper.ShowError("Unable to cancel the delivery: " + ex.Message, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        try
                        {
                            var disp = GetDispenserById(item.DispenserId);
                            var delivery = disp.GetDelivery(item.DeliveryId);
                            var delLocked = false;
                            if (delivery != null)
                            {
                                delLocked = delivery.IsLocked();
                            }

                            if (delLocked)  // if the delivery is not locked, then it might have been finalised on another station so we don't need to send another unlock command
                                _regulus.PumpController.UnlockDelivery(item.DispenserId, item.DeliveryId);
                        }
                        catch (Exception ex)
                        {
                            _log.Error(ex.ToString());
                            _guiHelper.ShowError("Unable to unlock the delivery: " + ex.Message, MessageBoxIcon.Error);
                        }
                    }
                }

                lock (_syncRoot)
                {
                    _itemsInDelivery.Remove(item);
                }
                WriteFuelItemsToFile(_itemsInDelivery);

                //!!!System.Threading.Thread.Sleep(100);//b23015
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                return false;
            }
            return true;            
        }

        private static bool IsPrepaySaleItem(PetrolSaleItem item)
        {
            return item.Mode == AuthModes.PREPAY && item.State == DeliveryStates.RESERVED && item.DeliveryId == 0;
        }

        internal void ProcessDeliveries(int saleId, bool voidLine)
        {
            bool bContinue = true;
            while (bContinue)
            {
                bContinue = false;
                int cnt;
                lock (_syncRoot)
                    cnt = _itemsInDelivery.Count;
                for (var i = 0; i < cnt; i++)
                {
                    PetrolSaleItem item;
                    lock (_syncRoot)
                        item = _itemsInDelivery[i];

                    if (item.SaleID == saleId)
                    {
                        bContinue = ProcessDelivery(item, voidLine);
                        break;
                    }
                }
            }
        }

        private void PumpInfoPosFooter_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
                e.Cancel = true;
        }

        internal bool ValidateSale(int saleId)
        {
            bool res = _itemsInDelivery.Count > 0;
            if (!res)
            {
                // If the tank delivery has been finalised on another station, then it's been removed from _itemsInDelivery but still in the FuelItems.dat file.
                var existingDeliveries = ReadExistingFuelItemsFromFile();
                var del = existingDeliveries.Where(d => d.SaleID == saleId).FirstOrDefault();
                var hasFinalised = del != null;

                if (hasFinalised)
                {
                    _guiHelper.ShowMessage("The fuel delivery has already been finalized. Please remove the item from sale.", MessageBoxIcon.Error);
                    return res;
                }
                else  
                    // Cannot find in FuelItems file, then it's a manual delivery.
                    res = true;
            }

            try
            {
                for (var i = 0; i < _itemsInDelivery.Count; i++)
                {
                    var item = _itemsInDelivery[i];
                    if (item.SaleID == saleId)
                    {
                        var disp = GetDispenserById(item.DispenserId);
                        if (IsPrepaySaleItem(item))
                        {
                            //b145824 attempt to recover the reservation
                            switch (disp.DispenserState)
                            {
                                case DispenserStates.RESERVED:
                                    //all good. nothing to do here
                                    break;
                                case DispenserStates.IDLE:
                                    //it is idle for some reason. attempt to re-reserve
                                    try
                                    {
                                        _regulus.PumpController.Reserve(_workstationId, item.DispenserId, item.Amount, disp.Blends.Single(s => s.BlendId == item.BlendId && s.NozzleId == item.NozzleId));
                                    }
                                    catch (Exception ex)
                                    {
                                        _log.Error(ex.ToString());
                                        _guiHelper.ShowError("Unable to reserve the pump: " + ex.Message, MessageBoxIcon.Error);
                                    }
                                    break;
                                default:
                                    _guiHelper.ShowMessage(string.Format("The reservation of the pump #{0} has been cancelled. Unable to complete the sale.",item.DispenserId),
                                        MessageBoxIcon.Error);
                                    break;
                            }                                                       
                        }
                        else
                        {
                            var delivery = disp.GetDelivery(item.DeliveryId);
                            if (delivery != null)
                            {
                                res &= delivery.IsLocked();
                                if (!res)
                                    _guiHelper.ShowMessage("The lock has been cancelled. Please remove the item from the sale and add it back again.", MessageBoxIcon.Error);
                                else
                                {
                                    res &= delivery.IsLockedByStation(_workstationId);
                                    if (!res)
                                        _guiHelper.ShowMessage("The fuel delivery is locked by another station. Please remove the item from the sale.", MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                _guiHelper.ShowMessage("The fuel delivery has already been finalized. Please remove the item from sale.", MessageBoxIcon.Error);
                                res = false;  // Cannot find the delivery from the pump (it might have been finalised on another POS)
                            }
                        }
                        if (!res)
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                res = false;
            }
            return res;
        }

        //b145685 recover after connection loss
        private DateTime _lastHeartBeatReconnectAttempt = DateTime.Now;
        private void RecoverHeartBeat()
        {
            var dt = DateTime.Now;
            if (heartbeatRecovered || dt.Subtract(_lastHeartBeatReconnectAttempt)< OFFLINE_TIMEOUT)
                return;
            _lastHeartBeatReconnectAttempt = dt;
            _log.Info("The heartbeat restored. Setting up pumps back..");
            
            var dispensers = PanelDispensers.Controls.OfType<DispenserUi>().ToList();
            try
            {
                for (int pumpIdx = 0; pumpIdx < dispensers.Count; pumpIdx++)
                {
                    var pump = dispensers[pumpIdx];
                    _regulus.PumpController.GetDispenserData(pump.DispenserId);
                    //we don't need to inform a user about an error here
                }

                heartbeatRecovered = true;
            }
            catch (Exception ex)
            {
                _log.Error("Unable to restore pumps: "  + ex.Message);
            }
        }

        #region Signal authorisation b23117

        private int playSoundStep = 0;
        private const int STEP_TO_PLAY_SOUND = 2; // play the sound on each second step
        private Color _bkToggleButtonColor;
        private Color _bkToggleButtonColorHighLight;
        private WinFormUi.Controls.PrepayForm _prepayForm;

        private void _blinkingTimer_Tick(object sender, EventArgs e)
        {
            // blink the "toggle" button if the current page is not the one with a calling pump
            var dispensers = PanelDispensers.Controls.OfType<DispenserUi>().ToList();
            var currentPage = this.CurrentPage;
            var blinkToggleButton = false;
            var smthIsCalling = false;

#region blackout pumps 
            var lastHeartBeat = System.Threading.Interlocked.Read(ref __lastHeartbeat);
#if !DEBUG
            if (DateTime.Now.Subtract(DateTime.FromBinary(lastHeartBeat)) > OFFLINE_TIMEOUT)
            {
                _log.Error("No heartbeat for a long period of time. Shut down the pumps.");
                heartbeatRecovered = false;
                for (int pumpIdx = 0; pumpIdx < dispensers.Count; pumpIdx++)
                {
                    var pump = dispensers[pumpIdx];
                    pump.DispenserState = DispenserStates.NOT_RESPONDING;
                    var delList = pump.DeliveryList.ToList();
                    if (delList.Count > 0)
                    {
                        _log.Warn("There were deliveries on the pump #{0}, but the pump is not responding.", pump.DispenserId);

                        //the deliveries should return back on heartbeat recover
                        for (int i = delList.Count - 1; i >= 0; i--)
                            pump.DeleteDelivery(delList[i].DeliveryId);
                    }
                }
            }
#endif
#endregion
            for (int pumpIdx = 0; pumpIdx < dispensers.Count; pumpIdx++)
            {
                var pump = dispensers[pumpIdx];
                if (pump.IsBlinking)
                    pump.DoBlink();

                if (pump.DispenserState == DispenserStates.CALLING && pump.Mode == DispenserModes.COMP_AUTH)
                {
                    smthIsCalling = true;

					if (!blinkToggleButton && (GetDispenserPage(pumpIdx) != _leftRightToggle))
					{
						blinkToggleButton = true;
					}
                }

				if (!blinkToggleButton && (GetDispenserPage(pumpIdx) != _leftRightToggle))
				{
					if (//Delivery in progress on pumps
						(pump.DispenserState == DispenserStates.DELIVERING) ||    
					    //Completed delivery on pump; or Stacked sale on pump; or Completed prepay delivery on pump; or Prepay timeout
					    pump.DeliveryList.Any(a => a.State == DeliveryStates.COMPLETED || a.State == DeliveryStates.PREPAY_REFUND))
					{
						blinkToggleButton = true;
					}
				}
            }

            // toggle the color of the '=>' button            
            Color currentToggleButtonColor = BtnToggle.BackColor;
            if (blinkToggleButton)
            {
                if (currentToggleButtonColor == _bkToggleButtonColor)
                {
                    BtnToggle.BackColor = _bkToggleButtonColorHighLight;
                }
                else
                {
                    BtnToggle.BackColor = _bkToggleButtonColor;
                }
            }
            else
            {
                if (currentToggleButtonColor != _bkToggleButtonColor)
                    BtnToggle.BackColor = _bkToggleButtonColor;
            }

            playSoundStep++;
            if (playSoundStep >= STEP_TO_PLAY_SOUND)
            {
                if (smthIsCalling)
                    //CR17090 Req 4: play Fax notification sound
                    MediaHelper.PlayNotificationSound();
                    
                playSoundStep = 0;
            }

            var pF = this.PrepayForm;
            if (pF != null)
            {
                //if (pF.Visible)
                //    pF.BringToFront();
            }            
        }
        #endregion

        internal IRegulus Regulus { get { return _regulus; } }

        public Mode GuiMode { get; private set; }
        public bool IsPrepay { get { return BtnPrepay.Down; } set { BtnPrepay.Down = value; } }

        internal void SetGuiMode(Mode mode)
        {
            GuiMode = mode;
            Font font;
            if (mode == Mode.Standard)
                font = new Font("Verdana", 8f, GraphicsUnit.Point);
            else
                font = new Font("Verdana", 9f, GraphicsUnit.Point);
            this.Font = font;
            font = new Font("Verdana", 9f, FontStyle.Bold, GraphicsUnit.Point);
            pnlButtons.Font = font;
        }

        internal void ApplyTheme()
        {
            if (ThemeManager.IsThemed)
            {
                ThemeManager.ApplyThemeToForm(this, POSThemeFormType.Standard);
                PanelDispensers.BackColor = ThemeManager.GetElementColor(POSThemeElement.GridSale1);
				// M155552 DWP Set the OnScreenStateButton colours (Due to changes in GUI dll)
				BtnStop.ForeColor = BtnPlay.ForeColor = BtnPrepay.ForeColor = ThemeManager.GetElementColor(POSThemeElement.FontColor);
				BtnStop.BackColor = BtnPlay.BackColor = BtnPrepay.BackColor = ThemeManager.GetElementColor(POSThemeElement.FaceSale);
			}

            _bkToggleButtonColor = ThemeManager.GetElementColor(POSThemeElement.ButtonSale);
            _bkToggleButtonColorHighLight = ThemeManager.GetElementColor(POSThemeElement.Button);
            if (_bkToggleButtonColor == _bkToggleButtonColorHighLight)
                _bkToggleButtonColorHighLight = _bkToggleButtonColor.GetBrightness() > 0.7f ? Color.Black : Color.Red;

			//FormBorderStyle = FormBorderStyle.None;			
		}

        public WinFormUi.Controls.PrepayForm PrepayForm
        {
            get
            {
                lock (_syncRoot)
                    return _prepayForm;
            }
            set
            {
                lock (_syncRoot)
                {
                    if (_prepayForm != null)
                        _prepayForm.MainForm = null;
                    _prepayForm = value;
                    if (_prepayForm != null)
                        _prepayForm.MainForm = this;
                }
            }
        }
        private string _receipt;

        public string Receipt
        {
            get
            {
                lock (_syncRoot)
                    return _receipt;
            }
            set
            {
                lock (_syncRoot)
                    _receipt = value;
            }
        }

        internal void QueryDeliveryInformation(int DispenserId, int deliveryId)
        {
            try
            {
                _regulus.PumpController.GetDeliveryData(DispenserId, deliveryId);
            }
            catch (Exception ex)
            {
                _log.Error(ex.ToString());
                _guiHelper.ShowError("Unable to query the delivery information: " + ex.Message, MessageBoxIcon.Error);
            }
        }

        private void PanelDispensers_Click(object sender, EventArgs e)
        {
            ReturnFocus();
        }

        public void ReturnFocus()
        {
			//GuiHelper.SetWindowActive(this.MainActiveWindow);
			// B146382 - Let the UI update while the focus returns to the main form
			var worker = new BackgroundWorker();
			worker.DoWork += SetWindowActive;
			worker.RunWorkerAsync();
        }

		private void SetWindowActive(object sender, DoWorkEventArgs e)
	    {
			_guiHelper.SetWindowActive(MainActiveWindow);
	    }

        private void PumpInfoPosFooter_Activated(object sender, EventArgs e)
        {
            ReturnFocus();
        }

        private void PanelDispensers_BackColorChanged(object sender, EventArgs e)
        {
            var clr = PanelDispensers.BackColor;
            foreach(var ctrl in PanelDispensers.Controls.OfType<DispenserUi>())
            {
                if (ctrl.BackColor != clr)
                    ctrl.BackColor = clr;
            }
        }

        private void WriteFuelItemsToFile(List<PetrolSaleItem> itemsInDelivery)
        {
            try
            {
                ReadWriteTextFileHelper.WriteToFile(FUEL_ITEMS_FILE_PATH, itemsInDelivery);
                LogForFuelFile("Overwrote file", itemsInDelivery);
            }
            catch (Exception ex)
            {
                _log.Error(string.Format("Error when saving fuel delivery items to file {0}. {1}", FUEL_ITEMS_FILE_PATH, ex.Message));
            }
        }

        private List<PetrolSaleItem> ReadExistingFuelItemsFromFile()
        {
            try
            {
                var itemsInDelivery = ReadWriteTextFileHelper.ReadFromFile(FUEL_ITEMS_FILE_PATH);
                LogForFuelFile("Read from file", itemsInDelivery);

                return itemsInDelivery;
            }
            catch (Exception ex)
            {
                _log.Error(string.Format("Failed at reading fuel delivery items from file {0}. {1}", FUEL_ITEMS_FILE_PATH, ex.Message));
                return null;
            }
        }

        private void LogForFuelFile(string HeadInfo, List<PetrolSaleItem> itemsInDelivery)
        {
            if (_log.IsTraceEnabled)
            {
                if (itemsInDelivery.Count > 0)
                {
                    var fuelItemStr = string.Empty;
                    foreach (var fuelItem in itemsInDelivery)
                    {

                        fuelItemStr = fuelItem.GetSaleItemString() + (string.IsNullOrEmpty(fuelItemStr) ? "" : "\r\n") + fuelItemStr;
                    }
                    _log.Trace(string.Format("{0} {1} with the following {2} fuel delivery items: \r\n{3}", HeadInfo, FUEL_ITEMS_FILE_PATH, itemsInDelivery.Count, fuelItemStr));
                }
                else
                    _log.Trace(string.Format("{0} {1} with empty fuel delivery items.", HeadInfo, FUEL_ITEMS_FILE_PATH));
            }
        }
		
	}
}
