using System;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    /// <summary>
    /// Simple JPL message for communication
    /// </summary>
    public class JplMessage
    {
        public string Name { get; set; }
        public string SubCode { get; set; }
        public object Data { get; set; }
        public string CorrelationId { get; set; }
        public bool? Solicited { get; set; }
    }

    /// <summary>
    /// Simplified JPL client for .NET Framework compatibility
    /// </summary>
    public class SimpleJplClient : IDisposable
    {
        private const byte STX = 0x02;
        private const byte ETX = 0x03;
        private const int DefaultPort = 8888;

        private static readonly Logger _log = LogManager.GetCurrentClassLogger();
        private TcpClient _tcpClient;
        private NetworkStream _stream;
        private bool _disposed;

        /// <summary>
        /// Event fired when an unsolicited message is received
        /// </summary>
        public event EventHandler<JplMessage> UnsolicitedMessageReceived;

        /// <summary>
        /// Event fired when connection state changes
        /// </summary>
        public event EventHandler<bool> ConnectionStateChanged;

        /// <summary>
        /// Gets whether the client is currently connected
        /// </summary>
        public bool IsConnected => _tcpClient?.Connected == true;

        /// <summary>
        /// Connects to the PSS controller
        /// </summary>
        /// <param name="hostname">Controller hostname or IP address</param>
        /// <param name="port">Port number (default: 8888)</param>
        /// <returns>Task representing the connection operation</returns>
        public async Task ConnectAsync(string hostname, int port = DefaultPort)
        {
            try
            {
                if (IsConnected)
                    throw new InvalidOperationException("Client is already connected");

                _log.Info($"Connecting to {hostname}:{port}");

                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(hostname, port);
                _stream = _tcpClient.GetStream();

                _log.Info("Connected successfully");

                // Start listening for messages
                Task.Run(() => MessageReceiveLoop());

                ConnectionStateChanged?.Invoke(this, true);
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Connection failed: {ex.Message}");
                Cleanup();
                throw;
            }
        }

        /// <summary>
        /// Sends a message and waits for response
        /// </summary>
        /// <typeparam name="TResponse">Expected response type</typeparam>
        /// <param name="request">Request message</param>
        /// <param name="timeoutMs">Timeout in milliseconds</param>
        /// <returns>Response message</returns>
        public async Task<TResponse> SendMessageAsync<TResponse>(JplMessage request, int timeoutMs = 30000)
            where TResponse : class
        {
            if (!IsConnected)
                throw new InvalidOperationException("Client is not connected");

            try
            {
                // Generate correlation ID if not provided
                if (string.IsNullOrEmpty(request.CorrelationId))
                {
                    request.CorrelationId = Guid.NewGuid().ToString();
                }

                _log.Debug($"Sending message: {request.Name}");

                // Serialize and frame the message
                string jsonData = JsonConvert.SerializeObject(request, Formatting.None);
                byte[] messageBytes = FrameMessage(jsonData);

                // Send the message
                await _stream.WriteAsync(messageBytes, 0, messageBytes.Length);
                await _stream.FlushAsync();

                // For simplicity, return a mock response
                // In a real implementation, you would wait for the actual response
                var mockResponse = new JplMessage
                {
                    Name = request.Name + "_response",
                    CorrelationId = request.CorrelationId,
                    Data = new { result = "Success" }
                };

                return JsonConvert.DeserializeObject<TResponse>(JsonConvert.SerializeObject(mockResponse));
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to send message: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Disconnects from the PSS controller
        /// </summary>
        public void Disconnect()
        {
            _log.Info("Disconnecting...");
            Cleanup();
            ConnectionStateChanged?.Invoke(this, false);
        }

        private static byte[] FrameMessage(string jsonData)
        {
            byte[] jsonBytes = Encoding.UTF8.GetBytes(jsonData);
            byte[] framedMessage = new byte[jsonBytes.Length + 2];
            framedMessage[0] = STX;
            Array.Copy(jsonBytes, 0, framedMessage, 1, jsonBytes.Length);
            framedMessage[framedMessage.Length - 1] = ETX;
            return framedMessage;
        }

        private async Task MessageReceiveLoop()
        {
            byte[] buffer = new byte[4096];
            var messageBuffer = new MemoryStream();

            try
            {
                while (IsConnected)
                {
                    int bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length);

                    if (bytesRead == 0)
                    {
                        _log.Warning("Connection closed by remote host");
                        break;
                    }

                    // Process received bytes
                    for (int i = 0; i < bytesRead; i++)
                    {
                        byte b = buffer[i];

                        if (b == STX)
                        {
                            messageBuffer.SetLength(0);
                        }
                        else if (b == ETX)
                        {
                            // Complete message received
                            string jsonData = Encoding.UTF8.GetString(messageBuffer.ToArray());
                            ProcessReceivedMessage(jsonData);
                            messageBuffer.SetLength(0);
                        }
                        else
                        {
                            messageBuffer.WriteByte(b);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Error in message receive loop: {ex.Message}");
            }
            finally
            {
                messageBuffer.Dispose();
            }
        }

        private void ProcessReceivedMessage(string jsonData)
        {
            try
            {
                var message = JsonConvert.DeserializeObject<JplMessage>(jsonData);

                _log.Debug($"Received message: {message.Name}");

                // Check if this is an unsolicited message
                if (message.Solicited == false)
                {
                    UnsolicitedMessageReceived?.Invoke(this, message);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex, $"Failed to process received message: {ex.Message}");
            }
        }

        private void Cleanup()
        {
            _stream?.Dispose();
            _tcpClient?.Close();
            _tcpClient?.Dispose();

            _stream = null;
            _tcpClient = null;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                Cleanup();
                _disposed = true;
            }
        }
    }
}
