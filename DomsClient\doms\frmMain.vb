﻿Public Class frmMain

  Private Class clsTransInfo
    Public ReadOnly TransSeqNo As Integer
    Public ReadOnly LockId As Integer
    Public ReadOnly FpId As Integer

    Public Sub New(ByVal FpId As Byte, ByVal TransSeqNo As Integer, ByVal LockId As Byte)
      Me.FpId = FpId
      Me.TransSeqNo = TransSeqNo
      Me.LockId = LockId
    End Sub

  End Class

#Region "Member variables"
  Private WithEvents _Forecourt As clsForecourt
  Public WithEvents _frmJsonMessageDump As frmJplMessages
  Public WithEvents _frmJsonConsole As frmJplConsole
  Private WithEvents _frmPrices As frmPrices
#End Region

#Region "Constants"
  Private Const TRANS_PREFIX_TEXT As String = "TRANS_"             'Node name prefix for the transaction nodes in the treeview
  Private Const FP_PREFIX_TEXT As String = "FP_"                   'Node name prefix for the fuelling points in the treeview
  Private Const TG_PREFIX_TEXT As String = "TG_"                   'Node name prefix for the tank gauges in the treeview
  Private Const DELIVERYDATA_NODE_TEXT As String = "Delivery Data" 'Text for delivery data root node in the treeview
  Private Const TGDATA_NODE_TEXT As String = "Tank Gauge Data"     'Text for tank gauge data root node in the treeview

  Private Const ONLINE_IMAGEINDEX As Integer = 3                   'Index of online image 
  Private Const OFFLINE_IMAGEINDEX As Integer = 4                  'Index of offline image
  Private Const TRANS_IMAGEINDEX As Integer = 5                    'Index of transaction image
  Private Const DELIVERY_DATA_IMAGEINDEX As Integer = 6            'Index of delivery data image
  Private Const TG_DATA_IMAGEINDEX As Integer = 6                  'Index of tank gauge data image
#End Region

#Region "Delegate definitions" 'Region contains all delegate defitions
  Public Delegate Sub OperationCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
  Delegate Sub OperationCompleted2(Success As Boolean, Message As String)
  Delegate Sub DeviceConfigChanged(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer)
  Delegate Sub DeviceStatusChanged(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer, ByVal StatusText As String, ByVal IsOnline As Boolean, ByVal FuellingData As Object)
  Delegate Sub InitTransList(ByVal DeviceId As Integer)
  Delegate Sub FpTransaction(ByVal FpId As Integer, ByVal GradeName As String, ByVal Volume As Decimal, ByVal Money As Decimal, ByVal TransSeqNo As Integer, ByVal UnitPrice As Decimal, ByVal LockId As Integer)
  Delegate Sub DeviceError(ByVal DeviceType As Integer, ByVal DeviceId As Integer, ByVal ErrorText As String, ByVal IsOnline As Boolean)
  Delegate Sub ConnectionClosed(ByVal WasLost As Boolean)
#End Region

  'This event handler is called when the application's main form/window is loaded
  Private Sub frmMain_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
    _Forecourt = New clsForecourt()                                          'Create an instance of the clsForecourt class

    tvForecourt.Nodes(0).ExpandAll()                                         'Expand the treeview of the forecourt
    btnFCLogon.Enabled = True                      'Enable FcLogon button if Init was successfull.

    If My.Settings.AutoLogon Then btnFCLogon_Click(Nothing, Nothing)
  End Sub

#Region "Completion handlers" 'Region contains the callbacks that are called when forecourt operations have succeeded.
  'This call-back is called when _Forecourt.FcLogon has completed.
  Private Sub FcLogonCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf FcLogonCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        SetButtonState(True)                 'Enable all relevant buttons when FcLogon was successfull.
      Else
        ShowErrorMsgBox("Logon failed.", Ex) 'Display an error message if FcLogon failed.
      End If
    End If
  End Sub

  'This call-back is called when _Forecourt.ReadPrices has completed.
  Private Sub PricesReadCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    Dim dt As New DataTable

    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf PricesReadCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        btnchangePrices.Enabled = False

        _frmPrices = New frmPrices                                                  'Create a frmPrices dialog
        _frmPrices.PriceDecimalPointPos = My.Settings.PriceDecimalPointPosition     'Set decimal point position to be used when displaying prices
        _frmPrices.Forecourt = _Forecourt
        _frmPrices.FcPriceSetResponse = Data
        _frmPrices.PricesSetCallback = AddressOf PricesSetCompleted

        _frmPrices.Show()
      Else
        ShowErrorMsgBox("Error reading prices.", Ex)                 'Show an error message if the prices could not be read.
      End If
    End If
  End Sub

  'This call-back is called when _Forecourt.SetPrices has completed.
  Private Sub PricesSetCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf PricesSetCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        MsgBox("Prices changed successfully.", MsgBoxStyle.Information)
      Else
        ShowErrorMsgBox("Prices could not be changed.", Ex)
      End If
    End If
  End Sub

  'This call-back is called when _Forecourt.ReadTgData has completed.
  Private Sub ReadTgDataCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    Dim n, DeviceNode, DataNode As TreeNode
    Dim TgDataResponse As TgDataRespType
    Dim Value As String
    Dim TankAvgTemp As TgDataRespType.SignedTemperatureType

    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf ReadTgDataCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        TgDataResponse = Data.TgDataResponse
        DeviceNode = GetDeviceNode(clsForecourt.DeviceTypes.TANK_GAUGE, TgDataResponse.data.TgId)  'Get the tank gauge node.

        If DeviceNode IsNot Nothing Then
          DataNode = DeviceNode.Nodes(TGDATA_NODE_TEXT)                             'Get the data node.

          If DataNode Is Nothing Then                                               'If there is no data node, then add one.
            DataNode = DeviceNode.Nodes.Add(TGDATA_NODE_TEXT, TGDATA_NODE_TEXT)
            DataNode.ImageIndex = TG_DATA_IMAGEINDEX
            DataNode.SelectedImageIndex = TG_DATA_IMAGEINDEX
          End If

          DataNode.Nodes.Clear()                                                    'Clear all sub nodes of the data node.

          For Each fi As Reflection.FieldInfo In TgDataResponse.data.TankDataItems.GetType().GetFields()
            Dim FieldValue As Object = fi.GetValue(TgDataResponse.data.TankDataItems)

            If TypeOf FieldValue Is String Then
              If fi.Name.EndsWith("DateAndTime") Then
                Value = clsForecourt.BCDBufToDateAndTime(FieldValue)
              Else
                Value = clsForecourt.BCDBufToDecimal(FieldValue, 2)
              End If
            Else
              TankAvgTemp = FieldValue
              Value = clsForecourt.BCDBufToDecimal(TankAvgTemp.Temperature, 1)
            End If

            n = DataNode.Nodes.Add(String.Format("{0}: {1}", fi.Name, Value))
            n.ImageIndex = TG_DATA_IMAGEINDEX
            n.SelectedImageIndex = TG_DATA_IMAGEINDEX
          Next

          DeviceNode.Expand() 'Expand the device node.
          DataNode.Expand()   'Expand the data node.
        End If
      Else
        ShowErrorMsgBox("Could not read tank gauge data.", Ex)
      End If
    End If
  End Sub

  Private Sub ReadDeliveriesCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    Dim n, DeviceNode, DeliveriesNode, DeliveryNode As TreeNode
    Dim DeliveryDataResponse As TankDeliveryDataRespType
    Dim TgId As Integer
    Dim DeliverySeqNo As String
    Dim Value As String = Nothing

    Try
      If InvokeRequired Then
        Invoke(New OperationCompleted(AddressOf ReadDeliveriesCompleted), New Object() {Success, Data, Ex})
      Else
        If Success Then
          DeliveryDataResponse = Data.DeliveryDataResponse
          TgId = DeliveryDataResponse.data.TgId

          If DeliveryDataResponse.data.DeliveryReportSeqNo = 0 Then
            MsgBox("No delivery information available for tank gauge " + TgId.ToString, MsgBoxStyle.Information)
            Return
          End If

          DeviceNode = GetDeviceNode(clsForecourt.DeviceTypes.TANK_GAUGE, TgId)

          If DeviceNode IsNot Nothing Then
            DeliveriesNode = DeviceNode.Nodes(DELIVERYDATA_NODE_TEXT)

            If DeliveriesNode Is Nothing Then
              DeliveriesNode = DeviceNode.Nodes.Add(DELIVERYDATA_NODE_TEXT, DELIVERYDATA_NODE_TEXT)
              DeliveriesNode.ImageIndex = DELIVERY_DATA_IMAGEINDEX
              DeliveriesNode.SelectedImageIndex = DELIVERY_DATA_IMAGEINDEX
            End If

            DeliverySeqNo = DeliveryDataResponse.data.TankDeliveryDataItems.TankDeliverySeqNo

            DeliveryNode = DeliveriesNode.Nodes.Item(DeliverySeqNo)

            If DeliveryNode Is Nothing Then
              DeliveryNode = DeliveriesNode.Nodes.Add(DeliverySeqNo.ToString, String.Format("Delivery Sequence Number {0}", DeliverySeqNo))
              DeliveryNode.ImageIndex = DELIVERY_DATA_IMAGEINDEX
              DeliveryNode.SelectedImageIndex = DELIVERY_DATA_IMAGEINDEX
            End If

            DeliveryNode.Nodes.Clear()

            For Each fi As Reflection.FieldInfo In DeliveryDataResponse.data.TankDeliveryDataItems.GetType().GetFields()
              If fi.Name = "TankDeliverySeqNo" Then Continue For

              Dim FieldValue As Object = fi.GetValue(DeliveryDataResponse.data.TankDeliveryDataItems)

              If fi.Name.EndsWith("DateAndTime") Then
                Value = clsForecourt.BCDBufToDateAndTime(FieldValue)
              ElseIf fi.Name.EndsWith("Vol") Then
                Value = clsForecourt.BCDBufToDecimal(FieldValue, 2)
              Else
                Value = FieldValue
              End If

              n = DeliveryNode.Nodes.Add(String.Format("{0}: {1}", fi.Name, Value))
              n.ImageIndex = DELIVERY_DATA_IMAGEINDEX
              n.SelectedImageIndex = DELIVERY_DATA_IMAGEINDEX
            Next

            DeviceNode.Expand()
            DeliveriesNode.Expand()
            DeliveryNode.Expand()
          End If
        Else
          ShowErrorMsgBox("Could not read delivery information.", Ex)
        End If
      End If
    Catch Excep As Exception
    End Try
  End Sub

  Private Sub AuthorizeCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf AuthorizeCompleted), New Object() {Success, Data, Ex})
    Else
      If Not Success Then ShowErrorMsgBox(String.Format("Authorization failed for fuelling point {0}.", Data), Ex)
    End If
  End Sub

  Private Sub DeauthorizeCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf DeauthorizeCompleted), New Object() {Success, Data, Ex})
    Else
      If Not Success Then ShowErrorMsgBox(String.Format("Deauthorization failed for fuelling point {0}.", Data), Ex)
    End If
  End Sub

  Private Sub ReadTotalsCompleted(ByVal Success As Boolean, ByVal Data As Object, ByVal Ex As Exception)
    Dim f As frmTotals

    If InvokeRequired Then
      Invoke(New OperationCompleted(AddressOf ReadTotalsCompleted), New Object() {Success, Data, Ex})
    Else
      If Success Then
        f = New frmTotals

        f.Text = String.Format("Totals for Fuelling Point {0}", Data.FpId)

        FillInTotals(Data.FpGradeTotalsResponse.data, f.lblFpGrandMoneyTotal, f.lblFpGrandVolTotal, f.lvFpGradeTotals, False)
        If Data.PumpGradeTotalsResponse IsNot Nothing Then FillInTotals(Data.PumpGradeTotalsResponse.data, f.lblPumpGrandMoneyTotal, f.lblPumpGrandVolTotal, f.lvPumpGradeTotals, True)

        f.Show()
      Else
        ShowErrorMsgBox(String.Format("Could not read totals for fuelling point {0}.", Data.FpId), Ex)
      End If
    End If
  End Sub

  Private Sub FillInTotals(ByVal Totals As Object, ByVal lblGrandMoneyTotal As Label, ByVal lblGrandVolTotal As Label, ByVal lvGradeTotals As ListView, IsPumpTotals As Boolean)
    Dim lvi As ListViewItem
    Dim GrandVolTotal, GrandMoneyTotal, GradeVolTotal As Decimal

    If IsPumpTotals Then
      GrandVolTotal = clsForecourt.BCDBufToDecimal(Totals.PuGrandVolTotal, My.Settings.VolumeDecimalPointPosition)
      GrandMoneyTotal = clsForecourt.BCDBufToDecimal(Totals.PuGrandMoneyTotal, My.Settings.MoneyDecimalPointPosition)
    Else
      GrandVolTotal = clsForecourt.BCDBufToDecimal(Totals.FpGrandVolTotal, My.Settings.VolumeDecimalPointPosition)
      GrandMoneyTotal = clsForecourt.BCDBufToDecimal(Totals.FpGrandMoneyTotal, My.Settings.MoneyDecimalPointPosition)
    End If

    lblGrandMoneyTotal.Text = String.Format("{0} {1}", lblGrandMoneyTotal.Text, FormatNumber(GrandMoneyTotal, My.Settings.MoneyDecimalPointPosition))
    lblGrandVolTotal.Text = String.Format("{0} {1}", lblGrandVolTotal.Text, FormatNumber(GrandVolTotal, My.Settings.VolumeDecimalPointPosition))

    For Each gt As Object In Totals.FpGradeOptions
      lvi = lvGradeTotals.Items.Add(gt.FcGradeId.ToString)
      lvi.SubItems.Add(gt.FpGradeOptionNo.ToString)
      lvi.SubItems.Add(_Forecourt.LookUpGradeName(gt.FcGradeId))

      If IsPumpTotals Then
        GradeVolTotal = clsForecourt.BCDBufToDecimal(gt.PuGradeVolTotal, My.Settings.VolumeDecimalPointPosition)
      Else
        GradeVolTotal = clsForecourt.BCDBufToDecimal(gt.FpGradeVolTotal, My.Settings.VolumeDecimalPointPosition)
      End If

      lvi.SubItems.Add(FormatNumber(GradeVolTotal, My.Settings.VolumeDecimalPointPosition))
    Next
  End Sub

#End Region

#Region "Forecourt Event Handlers"
  Private Sub _Forecourt_OperationCompleted(Success As Boolean, Message As String) Handles _Forecourt.OperationCompleted
    If InvokeRequired Then
      Invoke(New OperationCompleted2(AddressOf _Forecourt_OperationCompleted), New Object() {Success, Message})
    Else
      MsgBox(Message, IIf(Success, MsgBoxStyle.Information, MsgBoxStyle.Exclamation))
    End If
  End Sub

  Private Sub Forecourt_DeviceError(ByVal DeviceType As Integer, ByVal DeviceId As Integer, ByVal ErrorText As String, ByVal IsOnline As Boolean) Handles _Forecourt.DeviceError
    Dim Node As TreeNode

    If InvokeRequired Then
      Invoke(New DeviceError(AddressOf Forecourt_DeviceError), New Object() {DeviceType, DeviceId, ErrorText, IsOnline})
    Else
      Node = GetDeviceNode(DeviceType, DeviceId)

      If Node IsNot Nothing Then
        Node.Text = String.Format("{0} - {1}", DeviceId, ErrorText)
        Node.ImageIndex = IIf(IsOnline, ONLINE_IMAGEINDEX, OFFLINE_IMAGEINDEX)
        Node.SelectedImageIndex = Node.ImageIndex
        Node.BackColor = Color.Red
        Node.ForeColor = Color.White
      End If
    End If
  End Sub

  Private Sub Forecourt_DeviceStatusChanged(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer, ByVal StatusText As String, ByVal IsOnline As Boolean, ByVal FuellingData As Object) Handles _Forecourt.DeviceStatusChanged
    Dim Node As TreeNode
    Dim s As String

    If InvokeRequired Then
      Invoke(New DeviceStatusChanged(AddressOf Forecourt_DeviceStatusChanged), New Object() {DeviceType, DeviceId, StatusText, IsOnline, FuellingData})
    Else
      Node = GetDeviceNode(DeviceType, DeviceId)

      If Node IsNot Nothing Then
        If FuellingData IsNot Nothing Then
          s = String.Format("Volume: {0} Money: {1}", FormatNumber(FuellingData.Volume_e, My.Settings.VolumeDecimalPointPosition), FormatNumber(FuellingData.Money_e, My.Settings.MoneyDecimalPointPosition))
          Node.Text = String.Format("{0} - {1} ({2})", DeviceId, StatusText, s)
        Else
          Node.Text = String.Format("{0} - {1}", DeviceId, StatusText)
        End If

        Node.ImageIndex = IIf(IsOnline, ONLINE_IMAGEINDEX, OFFLINE_IMAGEINDEX)
        Node.SelectedImageIndex = Node.ImageIndex
        Node.BackColor = Color.White
        Node.ForeColor = Color.Black
      End If
    End If
  End Sub

  Private Sub Forecourt_DeviceConfigChanged(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer) Handles _Forecourt.DeviceConfigured
    Dim DeviceTypeNode, DeviceNode As TreeNode

    If InvokeRequired Then
      Invoke(New DeviceConfigChanged(AddressOf Forecourt_DeviceConfigChanged), New Object() {DeviceType, DeviceId})
    Else
      DeviceTypeNode = GetDeviceRootNode(DeviceType)

      If DeviceTypeNode IsNot Nothing Then
        DeviceNode = DeviceTypeNode.Nodes.Add(GetDeviceNodeName(DeviceType, DeviceId), DeviceId.ToString)
        DeviceNode.Tag = DeviceId
        DeviceTypeNode.ExpandAll()
      End If
    End If
  End Sub

  Private Sub _Forecourt_DeviceRemoved(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer) Handles _Forecourt.DeviceRemoved
    Dim DeviceNode As TreeNode

    If InvokeRequired Then
      Invoke(New DeviceConfigChanged(AddressOf _Forecourt_DeviceRemoved), New Object() {DeviceType, DeviceId})
    Else
      DeviceNode = GetDeviceNode(DeviceType, DeviceId)
      If DeviceNode IsNot Nothing Then Me.tvForecourt.Nodes.Remove(DeviceNode)
    End If
  End Sub

  Private Sub Forecourt_InitFpTransactionList(ByVal FpId As Integer) Handles _Forecourt.InitFpTransactionList
    Dim Node As TreeNode

    If InvokeRequired Then
      Invoke(New InitTransList(AddressOf Forecourt_InitFpTransactionList), New Object() {FpId})
    Else
      Node = tvForecourt.SelectedNode
      If Node IsNot Nothing AndAlso Node.Name.StartsWith(TRANS_PREFIX_TEXT) Then tvForecourt.SelectedNode = Node.Parent

      Node = GetDeviceNode(clsForecourt.DeviceTypes.FUELLING_POINT, FpId)

      If Node IsNot Nothing Then
        Node.Nodes.Clear()

        For Each lvi As ListViewItem In lvReceipt.Items
          If lvi.Name = FpId.ToString Then lvReceipt.Items.Remove(lvi)
        Next

        btnSaleOK.Enabled = lvReceipt.Items.Count > 0
      End If
    End If
  End Sub

  Private Sub _Forecourt_FpTransaction(FpId As Integer, GradeName As String, Volume As Decimal, Money As Decimal, TransSeqNo As Integer, UnitPrice As Decimal, LockId As Integer) Handles _Forecourt.FpTransaction
    Dim DeviceNode, TransactionNode As TreeNode
    Dim lvi As ListViewItem
    Dim TransInfo As clsTransInfo

    If InvokeRequired Then
      Invoke(New FpTransaction(AddressOf _Forecourt_FpTransaction), New Object() {FpId, GradeName, Volume, Money, TransSeqNo, UnitPrice, LockId})
    Else
      DeviceNode = GetDeviceNode(clsForecourt.DeviceTypes.FUELLING_POINT, FpId)

      If DeviceNode IsNot Nothing Then
        If Volume < 0D Then 'Volume will be negative if it does not contain a "valid" value
          TransactionNode = DeviceNode.Nodes.Add(String.Format("{0}{1}", TRANS_PREFIX_TEXT, TransSeqNo), String.Format("Money: {0}{1}", FormatNumber(Money, My.Settings.MoneyDecimalPointPosition), IIf(LockId <> 0, String.Format("  (locked by {0})", LockId), String.Empty)))
        Else
          TransactionNode = DeviceNode.Nodes.Add(String.Format("{0}{1}", TRANS_PREFIX_TEXT, TransSeqNo), String.Format("Volume: {0} Money: {1}{2}", FormatNumber(Volume, My.Settings.VolumeDecimalPointPosition), FormatNumber(Money, My.Settings.MoneyDecimalPointPosition), IIf(LockId <> 0, String.Format("  (locked by {0})", LockId), String.Empty)))
        End If
        TransInfo = New clsTransInfo(FpId, TransSeqNo, LockId)

        TransactionNode.Tag = TransInfo
        TransactionNode.ImageIndex = TRANS_IMAGEINDEX
        TransactionNode.SelectedImageIndex = TRANS_IMAGEINDEX

        DeviceNode.ExpandAll()

        If LockId = My.Settings.PosId Then
          lvi = lvReceipt.Items.Add(FpId.ToString)
          lvi.Name = FpId.ToString
          lvi.SubItems.Add(GradeName)

          With My.Settings
            lvi.SubItems.Add(FormatNumber(Volume, .VolumeDecimalPointPosition))
            lvi.SubItems.Add(FormatNumber(UnitPrice, .PriceDecimalPointPosition))
            lvi.SubItems.Add(FormatNumber(Money, .MoneyDecimalPointPosition))
          End With

          lvi.Tag = TransInfo

          btnSaleOK.Enabled = True
        End If
      End If
    End If
  End Sub

  Private Sub Forecourt_ConnectionClosed(ByVal WasLost As Boolean) Handles _Forecourt.ConnectionClosed
    Dim Node As TreeNode

    If InvokeRequired Then
      Invoke(New ConnectionClosed(AddressOf Forecourt_ConnectionClosed), New Object() {WasLost})
    Else
      DisableButtons()
      SetButtonState(False)

      lvReceipt.Items.Clear()

      Node = GetDeviceRootNode(clsForecourt.DeviceTypes.FUELLING_POINT)
      If Node IsNot Nothing Then Node.Nodes.Clear()

      Node = GetDeviceRootNode(clsForecourt.DeviceTypes.TANK_GAUGE)
      If Node IsNot Nothing Then Node.Nodes.Clear()

      If WasLost Then MsgBox("Connection was lost. Try FcLogon again.", MsgBoxStyle.Exclamation)
    End If
  End Sub

#End Region

#Region "UI event Handlers"

  Private Sub btnFCLogon_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFCLogon.Click
    With My.Settings
      If .IPAddress = String.Empty Then
        If MsgBox("IP address of PSS has not been configgured. Do you want to configure it now?", MsgBoxStyle.YesNo Or MsgBoxStyle.Question) = MsgBoxResult.Yes Then
          btnOptions_Click(Nothing, Nothing)
        End If
      End If

      If .IPAddress <> String.Empty Then _Forecourt.FcLogon(.IPAddress, .PosId, .FcLogonString, .VolumeDecimalPointPosition, .PriceDecimalPointPosition, .MoneyDecimalPointPosition, AddressOf FcLogonCompleted)
    End With
  End Sub

  Private Sub btnLockTrans_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLockTrans.Click
    Dim TransInfo As clsTransInfo

    TransInfo = tvForecourt.SelectedNode.Tag

    If TransInfo.LockId = 0 Then
      _Forecourt.LockTransaction(TransInfo.FpId, TransInfo.TransSeqNo)
    Else
      MsgBox("Transaction is already locked.", MsgBoxStyle.Information)
    End If
  End Sub

  Private Sub btnUnlock_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnUnlock.Click
    Dim TransInfo As clsTransInfo

    TransInfo = tvForecourt.SelectedNode.Tag

    If TransInfo.LockId = My.Settings.PosId Then
      _Forecourt.UnlockTransaction(TransInfo.FpId, TransInfo.TransSeqNo)
    ElseIf TransInfo.LockId = 0 Then
      MsgBox("Transaction is not locked.", MsgBoxStyle.Information)
    Else
      MsgBox("Transaction is locked by another POS.", MsgBoxStyle.Information)
    End If
  End Sub

  Private Sub btnSaleOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaleOK.Click
    Dim TransInfo As clsTransInfo

    For Each lvi As ListViewItem In lvReceipt.Items
      TransInfo = lvi.Tag
      _Forecourt.ClearTransAction(TransInfo.FpId, TransInfo.TransSeqNo)
    Next
  End Sub

  Private Sub btnReadTgData_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReadTgData.Click
    _Forecourt.ReadTgData(tvForecourt.SelectedNode.Tag, AddressOf ReadTgDataCompleted)
  End Sub

  Private Sub btnAuthorize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAuthorize.Click
    _Forecourt.AuthorizeFuellingPoint(tvForecourt.SelectedNode.Tag, AddressOf AuthorizeCompleted)
  End Sub

  Private Sub btnDeauthorize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeauthorize.Click
    _Forecourt.DeauthorizeFuellingPoint(tvForecourt.SelectedNode.Tag, AddressOf DeauthorizeCompleted)
  End Sub

  Private Sub btnClearError_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClearError.Click
    Dim Node As TreeNode = tvForecourt.SelectedNode
    If Node IsNot Nothing AndAlso Node.Name.StartsWith(FP_PREFIX_TEXT) Then _Forecourt.ClearFpError(Node.Tag)
  End Sub

  Private Sub btnchangePrices_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnchangePrices.Click
    _Forecourt.ReadPrices(AddressOf PricesReadCompleted)
  End Sub

  Private Sub btnReadTotals_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReadTotals.Click
    _Forecourt.ReadTotals(tvForecourt.SelectedNode.Tag, AddressOf ReadTotalsCompleted)
  End Sub

  Private Sub btnReadDeliveries_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReadDeliveries.Click
    _Forecourt.ReadDeliveries(tvForecourt.SelectedNode.Tag, AddressOf ReadDeliveriesCompleted)
  End Sub

  Private Sub btnViewJsonMessages_Click(sender As Object, e As EventArgs) Handles btnViewJsonMessageDump.Click
    If _frmJsonMessageDump Is Nothing Then
      btnViewJsonMessageDump.Enabled = False
      _frmJsonMessageDump = New frmJplMessages
      _frmJsonMessageDump.Show(Me)
    End If
  End Sub

  Private Sub btnOpenConsole_Click(sender As Object, e As EventArgs) Handles btnOpenConsole.Click
    If _frmJsonConsole Is Nothing Then
      btnOpenConsole.Enabled = False
      _frmJsonConsole = New frmJplConsole
      _frmJsonConsole.Forecourt = _Forecourt
      _frmJsonConsole.Show(Me)
    End If
  End Sub

  Private Sub _frmJsonMessageDump_Closed(sender As Object, e As EventArgs) Handles _frmJsonMessageDump.Closed
    _frmJsonMessageDump = Nothing
    btnViewJsonMessageDump.Enabled = True
  End Sub

  Private Sub _frmJsonConsole_Closed(sender As Object, e As EventArgs) Handles _frmJsonConsole.Closed
    btnOpenConsole.Enabled = True
    _frmJsonConsole = Nothing
  End Sub

  Private Sub _frmPrices_Closed(sender As Object, e As EventArgs) Handles _frmPrices.Closed
    btnchangePrices.Enabled = True
    _frmPrices = Nothing
  End Sub


  Private Sub btnOptions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOptions.Click
    Dim f As frmOptions

    f = New frmOptions
    f.ShowDialog()
  End Sub

  Private Sub tvForecourt_AfterSelect(ByVal sender As Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles tvForecourt.AfterSelect
    Dim NodeName As String = e.Node.Name
    Dim OnFp As Boolean = NodeName.StartsWith(FP_PREFIX_TEXT)
    Dim OnTg As Boolean = NodeName.StartsWith(TG_PREFIX_TEXT)
    Dim OnTrans As Boolean = NodeName.StartsWith(TRANS_PREFIX_TEXT)

    btnAuthorize.Enabled = OnFp
    btnDeauthorize.Enabled = OnFp
    btnReadTotals.Enabled = OnFp

    btnClearError.Enabled = OnFp Or OnTg

    btnReadTgData.Enabled = OnTg
    btnReadDeliveries.Enabled = OnTg

    btnLockTrans.Enabled = OnTrans
    btnUnlock.Enabled = OnTrans
  End Sub

#End Region

  Private Function GetDeviceNode(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer) As TreeNode
    Dim Nodes() As TreeNode = tvForecourt.Nodes.Find(GetDeviceNodeName(DeviceType, DeviceId), True)
    If Nodes.Count > 0 Then Return Nodes(0) Else Return Nothing
  End Function

  Private Function GetDeviceNodeName(ByVal DeviceType As clsForecourt.DeviceTypes, ByVal DeviceId As Integer) As String
    Select Case DeviceType
      Case clsForecourt.DeviceTypes.FUELLING_POINT
        Return String.Format("{0}_{1}", FP_PREFIX_TEXT, DeviceId)
      Case clsForecourt.DeviceTypes.TANK_GAUGE
        Return String.Format("{0}_{1}", TG_PREFIX_TEXT, DeviceId)
      Case Else
        Return Nothing
    End Select
  End Function

  Private Function GetDeviceRootNode(ByVal DeviceType As clsForecourt.DeviceTypes) As TreeNode
    Dim RootNodeName As String
    Dim Nodes() As TreeNode

    Select Case DeviceType
      Case clsForecourt.DeviceTypes.FUELLING_POINT
        RootNodeName = "Fps"
      Case clsForecourt.DeviceTypes.TANK_GAUGE
        RootNodeName = "Tgs"
      Case Else
        Return Nothing
    End Select

    Nodes = tvForecourt.Nodes.Find(RootNodeName, True)
    If Nodes.Count > 0 Then Return Nodes(0) Else Return Nothing
  End Function

  Private Sub SetButtonState(ByVal Enabled As Boolean)
    btnchangePrices.Enabled = Enabled
  End Sub

  Private Sub DisableButtons()
    btnSaleOK.Enabled = False
    btnUnlock.Enabled = False
    btnClearError.Enabled = False
    btnReadTotals.Enabled = False
    btnReadDeliveries.Enabled = False
    btnAuthorize.Enabled = False
    btnDeauthorize.Enabled = False
    btnLockTrans.Enabled = False
    btnReadTgData.Enabled = False
  End Sub


  Shared Function FormatNumber(ByVal Number As Decimal, ByVal DecimalPointPos As Integer) As String
    Return String.Format(String.Format("{{0:n{0}}}", DecimalPointPos), Number)
  End Function

  Private Sub ShowErrorMsgBox(ByVal ErrMessage As String, ByVal Ex As Exception)
    MsgBox(String.Format("{0} ({1})", ErrMessage, Ex.Message), MsgBoxStyle.Exclamation)
  End Sub

  Private Sub _Forecourt_RequestSent(Request As String) Handles _Forecourt.RequestSent
    AddToJsonDump("TX", Request)
  End Sub

  Private Sub _Forecourt_ResponseReceived(Response As String) Handles _Forecourt.ResponseReceived
    AddToJsonDump("RX", Response)
  End Sub

  Private Sub AddToJsonDump(Prefix As String, Message As String)
    If _frmJsonMessageDump IsNot Nothing Then _frmJsonMessageDump.AddToList(New frmJplMessages.JsonDumpEntry(Prefix, Message))
  End Sub

  Private Sub FuellingDataTimer_Tick(sender As Object, e As EventArgs) Handles FuellingDataTimer.Tick
    _Forecourt.CheckFuellingData()
  End Sub

End Class
