﻿Public Class frmOptions

  Private Sub btnOk_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOk.Click
    If Not txtFcLogonString.Text.Contains("UNSO_TRBUFSTA_3") Then
      MsgBox("Forecourt logon string must contain ""UNSO_TRBUFSTA_3"".", MsgBoxStyle.Exclamation)
      Return
    End If

    If Not txtFcLogonString.Text.Contains("UNSO_INSTSTA_1") Then
      MsgBox("Forecourt logon string must contain ""UNSO_INSTSTA_1"".", MsgBoxStyle.Exclamation)
      Return
    End If

    My.Settings.PosId = udPosId.Value

    My.Settings.FcLogonString = txtFcLogonString.Text
    My.Settings.IPAddress = txtIPAddress.Text

    My.Settings.MoneyDecimalPointPosition = udAmountDecPointPos.Value
    My.Settings.PriceDecimalPointPosition = udPriceDecPointPos.Value
    My.Settings.VolumeDecimalPointPosition = udVolDecPointPos.Value

    My.Settings.ClearDeliveryReports = cbClearDeliveryReports.Checked
    My.Settings.ClearBackOfficeRecords = cbClearBackOfficeRecords.Checked
    My.Settings.ShowHeartBeatMessages = cbShowHeartbeatMessages.Checked
    My.Settings.ShowUnsolicitedMessages = cbShowUnsolicitedMessages.Checked
    My.Settings.AutoLockTransactions = cbAutoLockTransactions.Checked

    Close()

    MyBase.DialogResult = Windows.Forms.DialogResult.OK
  End Sub

  Private Sub frmOptions_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
    udPosId.Value = My.Settings.PosId

    txtFcLogonString.Text = My.Settings.FcLogonString
    txtIPAddress.Text = My.Settings.IPAddress

    udAmountDecPointPos.Value = My.Settings.MoneyDecimalPointPosition
    udPriceDecPointPos.Value = My.Settings.PriceDecimalPointPosition
    udVolDecPointPos.Value = My.Settings.VolumeDecimalPointPosition

    cbClearDeliveryReports.Checked = My.Settings.ClearDeliveryReports
    cbClearBackOfficeRecords.Checked = My.Settings.ClearBackOfficeRecords
    cbShowHeartbeatMessages.Checked = My.Settings.ShowHeartBeatMessages
    cbShowUnsolicitedMessages.Checked = My.Settings.ShowUnsolicitedMessages
    cbAutoLockTransactions.Checked = My.Settings.AutoLockTransactions
  End Sub

  Private Sub btnCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnCancel.Click
    MyBase.DialogResult = Windows.Forms.DialogResult.Cancel
  End Sub
End Class