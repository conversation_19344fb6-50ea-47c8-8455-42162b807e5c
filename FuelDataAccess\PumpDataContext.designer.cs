﻿#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Triquestra.Common.PumpEsm.DataAccess
{
	using System.Data.Linq;
	using System.Data.Linq.Mapping;
	using System.Data;
	using System.Collections.Generic;
	using System.Reflection;
	using System.Linq;
	using System.Linq.Expressions;
	using System.ComponentModel;
	using System;
	
	
	[global::System.Data.Linq.Mapping.DatabaseAttribute(Name="AKPOS")]
	public partial class PumpDataContext : System.Data.Linq.DataContext
	{
		
		private static System.Data.Linq.Mapping.MappingSource mappingSource = new AttributeMappingSource();
		
    #region Extensibility Method Definitions
    partial void OnCreated();
    partial void InsertFuel_Config(Fuel_Config instance);
    partial void UpdateFuel_Config(Fuel_Config instance);
    partial void DeleteFuel_Config(Fuel_Config instance);
    partial void InsertFuel_Tank(Fuel_Tank instance);
    partial void UpdateFuel_Tank(Fuel_Tank instance);
    partial void DeleteFuel_Tank(Fuel_Tank instance);
    partial void InsertFuel_Hose(Fuel_Hose instance);
    partial void UpdateFuel_Hose(Fuel_Hose instance);
    partial void DeleteFuel_Hose(Fuel_Hose instance);
    partial void InsertFuel_Meter(Fuel_Meter instance);
    partial void UpdateFuel_Meter(Fuel_Meter instance);
    partial void DeleteFuel_Meter(Fuel_Meter instance);
    partial void InsertFuel_ProfilePumpMode(Fuel_ProfilePumpMode instance);
    partial void UpdateFuel_ProfilePumpMode(Fuel_ProfilePumpMode instance);
    partial void DeleteFuel_ProfilePumpMode(Fuel_ProfilePumpMode instance);
    partial void InsertFuel_Profile(Fuel_Profile instance);
    partial void UpdateFuel_Profile(Fuel_Profile instance);
    partial void DeleteFuel_Profile(Fuel_Profile instance);
    partial void InsertFuel_PumpModel(Fuel_PumpModel instance);
    partial void UpdateFuel_PumpModel(Fuel_PumpModel instance);
    partial void DeleteFuel_PumpModel(Fuel_PumpModel instance);
    partial void InsertFuel_Pump(Fuel_Pump instance);
    partial void UpdateFuel_Pump(Fuel_Pump instance);
    partial void DeleteFuel_Pump(Fuel_Pump instance);
    partial void InsertFuel_Recon(Fuel_Recon instance);
    partial void UpdateFuel_Recon(Fuel_Recon instance);
    partial void DeleteFuel_Recon(Fuel_Recon instance);
    #endregion
		
		public PumpDataContext() : 
				base(global::Triquestra.Common.PumpEsm.DataAccess.Properties.Settings.Default.AKPOSConnectionString, mappingSource)
		{
			OnCreated();
		}
		
		public PumpDataContext(string connection) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public PumpDataContext(System.Data.IDbConnection connection) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public PumpDataContext(string connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public PumpDataContext(System.Data.IDbConnection connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public System.Data.Linq.Table<Fuel_Config> Fuel_Configs
		{
			get
			{
				return this.GetTable<Fuel_Config>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Tank> Fuel_Tanks
		{
			get
			{
				return this.GetTable<Fuel_Tank>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Hose> Fuel_Hoses
		{
			get
			{
				return this.GetTable<Fuel_Hose>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Meter> Fuel_Meters
		{
			get
			{
				return this.GetTable<Fuel_Meter>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_ProfilePumpMode> Fuel_ProfilePumpModes
		{
			get
			{
				return this.GetTable<Fuel_ProfilePumpMode>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Profile> Fuel_Profiles
		{
			get
			{
				return this.GetTable<Fuel_Profile>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_PumpModel> Fuel_PumpModels
		{
			get
			{
				return this.GetTable<Fuel_PumpModel>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Pump> Fuel_Pumps
		{
			get
			{
				return this.GetTable<Fuel_Pump>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_Recon> Fuel_Recons
		{
			get
			{
				return this.GetTable<Fuel_Recon>();
			}
		}
		
		public System.Data.Linq.Table<Fuel_ReconDetail> Fuel_ReconDetails
		{
			get
			{
				return this.GetTable<Fuel_ReconDetail>();
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Config")]
	public partial class Fuel_Config : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private string _Parameter;
		
		private System.Nullable<int> _GroupID;
		
		private string _Value;
		
		private string _Description;
		
		private string _ValueNotes;
		
		private bool _BVisible;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnParameterChanging(string value);
    partial void OnParameterChanged();
    partial void OnGroupIDChanging(System.Nullable<int> value);
    partial void OnGroupIDChanged();
    partial void OnValueChanging(string value);
    partial void OnValueChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnValueNotesChanging(string value);
    partial void OnValueNotesChanged();
    partial void OnBVisibleChanging(bool value);
    partial void OnBVisibleChanged();
    #endregion
		
		public Fuel_Config()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Parameter", DbType="VarChar(100) NOT NULL", CanBeNull=false, IsPrimaryKey=true)]
		public string Parameter
		{
			get
			{
				return this._Parameter;
			}
			set
			{
				if ((this._Parameter != value))
				{
					this.OnParameterChanging(value);
					this.SendPropertyChanging();
					this._Parameter = value;
					this.SendPropertyChanged("Parameter");
					this.OnParameterChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_GroupID", DbType="Int")]
		public System.Nullable<int> GroupID
		{
			get
			{
				return this._GroupID;
			}
			set
			{
				if ((this._GroupID != value))
				{
					this.OnGroupIDChanging(value);
					this.SendPropertyChanging();
					this._GroupID = value;
					this.SendPropertyChanged("GroupID");
					this.OnGroupIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Value", DbType="VarChar(200)")]
		public string Value
		{
			get
			{
				return this._Value;
			}
			set
			{
				if ((this._Value != value))
				{
					this.OnValueChanging(value);
					this.SendPropertyChanging();
					this._Value = value;
					this.SendPropertyChanged("Value");
					this.OnValueChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(200)")]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ValueNotes", DbType="VarChar(500)")]
		public string ValueNotes
		{
			get
			{
				return this._ValueNotes;
			}
			set
			{
				if ((this._ValueNotes != value))
				{
					this.OnValueNotesChanging(value);
					this.SendPropertyChanging();
					this._ValueNotes = value;
					this.SendPropertyChanged("ValueNotes");
					this.OnValueNotesChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_BVisible", DbType="Bit NOT NULL")]
		public bool BVisible
		{
			get
			{
				return this._BVisible;
			}
			set
			{
				if ((this._BVisible != value))
				{
					this.OnBVisibleChanging(value);
					this.SendPropertyChanging();
					this._BVisible = value;
					this.SendPropertyChanged("BVisible");
					this.OnBVisibleChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Tanks")]
	public partial class Fuel_Tank : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _Description;
		
		private string _UPC;
		
		private int _Capacity;
		
		private string _TankMeterType;
		
		private System.Nullable<int> _HWA;
		
		private System.Nullable<int> _HVW;
		
		private System.Nullable<int> _LVW;
		
		private System.Nullable<int> _LVA;
		
		private System.Nullable<int> _HRA;
		
		private System.Nullable<int> _TempUhalo;
		
		private System.Nullable<int> _TempLhalo;
		
		private System.Nullable<int> _ToleranceLoss;
		
		private System.Nullable<int> _ToleranceGain;
		
		private string _TankType;
		
		private System.Nullable<int> _ManifoldID;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnUPCChanging(string value);
    partial void OnUPCChanged();
    partial void OnCapacityChanging(int value);
    partial void OnCapacityChanged();
    partial void OnTankMeterTypeChanging(string value);
    partial void OnTankMeterTypeChanged();
    partial void OnHWAChanging(System.Nullable<int> value);
    partial void OnHWAChanged();
    partial void OnHVWChanging(System.Nullable<int> value);
    partial void OnHVWChanged();
    partial void OnLVWChanging(System.Nullable<int> value);
    partial void OnLVWChanged();
    partial void OnLVAChanging(System.Nullable<int> value);
    partial void OnLVAChanged();
    partial void OnHRAChanging(System.Nullable<int> value);
    partial void OnHRAChanged();
    partial void OnTempUhaloChanging(System.Nullable<int> value);
    partial void OnTempUhaloChanged();
    partial void OnTempLhaloChanging(System.Nullable<int> value);
    partial void OnTempLhaloChanged();
    partial void OnToleranceLossChanging(System.Nullable<int> value);
    partial void OnToleranceLossChanged();
    partial void OnToleranceGainChanging(System.Nullable<int> value);
    partial void OnToleranceGainChanged();
    partial void OnTankTypeChanging(string value);
    partial void OnTankTypeChanged();
    partial void OnManifoldIDChanging(System.Nullable<int> value);
    partial void OnManifoldIDChanged();
    #endregion
		
		public Fuel_Tank()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(20) NOT NULL", CanBeNull=false)]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_UPC", DbType="VarChar(20) NOT NULL", CanBeNull=false)]
		public string UPC
		{
			get
			{
				return this._UPC;
			}
			set
			{
				if ((this._UPC != value))
				{
					this.OnUPCChanging(value);
					this.SendPropertyChanging();
					this._UPC = value;
					this.SendPropertyChanged("UPC");
					this.OnUPCChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Capacity", DbType="Int NOT NULL")]
		public int Capacity
		{
			get
			{
				return this._Capacity;
			}
			set
			{
				if ((this._Capacity != value))
				{
					this.OnCapacityChanging(value);
					this.SendPropertyChanging();
					this._Capacity = value;
					this.SendPropertyChanged("Capacity");
					this.OnCapacityChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankMeterType", DbType="VarChar(50)")]
		public string TankMeterType
		{
			get
			{
				return this._TankMeterType;
			}
			set
			{
				if ((this._TankMeterType != value))
				{
					this.OnTankMeterTypeChanging(value);
					this.SendPropertyChanging();
					this._TankMeterType = value;
					this.SendPropertyChanged("TankMeterType");
					this.OnTankMeterTypeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_HWA", DbType="Int")]
		public System.Nullable<int> HWA
		{
			get
			{
				return this._HWA;
			}
			set
			{
				if ((this._HWA != value))
				{
					this.OnHWAChanging(value);
					this.SendPropertyChanging();
					this._HWA = value;
					this.SendPropertyChanged("HWA");
					this.OnHWAChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_HVW", DbType="Int")]
		public System.Nullable<int> HVW
		{
			get
			{
				return this._HVW;
			}
			set
			{
				if ((this._HVW != value))
				{
					this.OnHVWChanging(value);
					this.SendPropertyChanging();
					this._HVW = value;
					this.SendPropertyChanged("HVW");
					this.OnHVWChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_LVW", DbType="Int")]
		public System.Nullable<int> LVW
		{
			get
			{
				return this._LVW;
			}
			set
			{
				if ((this._LVW != value))
				{
					this.OnLVWChanging(value);
					this.SendPropertyChanging();
					this._LVW = value;
					this.SendPropertyChanged("LVW");
					this.OnLVWChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_LVA", DbType="Int")]
		public System.Nullable<int> LVA
		{
			get
			{
				return this._LVA;
			}
			set
			{
				if ((this._LVA != value))
				{
					this.OnLVAChanging(value);
					this.SendPropertyChanging();
					this._LVA = value;
					this.SendPropertyChanged("LVA");
					this.OnLVAChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_HRA", DbType="Int")]
		public System.Nullable<int> HRA
		{
			get
			{
				return this._HRA;
			}
			set
			{
				if ((this._HRA != value))
				{
					this.OnHRAChanging(value);
					this.SendPropertyChanging();
					this._HRA = value;
					this.SendPropertyChanged("HRA");
					this.OnHRAChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TempUhalo", DbType="Int")]
		public System.Nullable<int> TempUhalo
		{
			get
			{
				return this._TempUhalo;
			}
			set
			{
				if ((this._TempUhalo != value))
				{
					this.OnTempUhaloChanging(value);
					this.SendPropertyChanging();
					this._TempUhalo = value;
					this.SendPropertyChanged("TempUhalo");
					this.OnTempUhaloChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TempLhalo", DbType="Int")]
		public System.Nullable<int> TempLhalo
		{
			get
			{
				return this._TempLhalo;
			}
			set
			{
				if ((this._TempLhalo != value))
				{
					this.OnTempLhaloChanging(value);
					this.SendPropertyChanging();
					this._TempLhalo = value;
					this.SendPropertyChanged("TempLhalo");
					this.OnTempLhaloChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ToleranceLoss", DbType="Int")]
		public System.Nullable<int> ToleranceLoss
		{
			get
			{
				return this._ToleranceLoss;
			}
			set
			{
				if ((this._ToleranceLoss != value))
				{
					this.OnToleranceLossChanging(value);
					this.SendPropertyChanging();
					this._ToleranceLoss = value;
					this.SendPropertyChanged("ToleranceLoss");
					this.OnToleranceLossChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ToleranceGain", DbType="Int")]
		public System.Nullable<int> ToleranceGain
		{
			get
			{
				return this._ToleranceGain;
			}
			set
			{
				if ((this._ToleranceGain != value))
				{
					this.OnToleranceGainChanging(value);
					this.SendPropertyChanging();
					this._ToleranceGain = value;
					this.SendPropertyChanged("ToleranceGain");
					this.OnToleranceGainChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankType", DbType="VarChar(1) NOT NULL", CanBeNull=false)]
		public string TankType
		{
			get
			{
				return this._TankType;
			}
			set
			{
				if ((this._TankType != value))
				{
					this.OnTankTypeChanging(value);
					this.SendPropertyChanging();
					this._TankType = value;
					this.SendPropertyChanged("TankType");
					this.OnTankTypeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ManifoldID", DbType="Int")]
		public System.Nullable<int> ManifoldID
		{
			get
			{
				return this._ManifoldID;
			}
			set
			{
				if ((this._ManifoldID != value))
				{
					this.OnManifoldIDChanging(value);
					this.SendPropertyChanging();
					this._ManifoldID = value;
					this.SendPropertyChanged("ManifoldID");
					this.OnManifoldIDChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Hoses")]
	public partial class Fuel_Hose : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _DeviceID;
		
		private string _Description;
		
		private System.Nullable<int> _PumpID;
		
		private System.Nullable<int> _TankID;
		
		private System.Nullable<int> _MeterID;
		
		private string _UPC;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnDeviceIDChanging(string value);
    partial void OnDeviceIDChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnPumpIDChanging(System.Nullable<int> value);
    partial void OnPumpIDChanged();
    partial void OnTankIDChanging(System.Nullable<int> value);
    partial void OnTankIDChanged();
    partial void OnMeterIDChanging(System.Nullable<int> value);
    partial void OnMeterIDChanged();
    partial void OnUPCChanging(string value);
    partial void OnUPCChanged();
    #endregion
		
		public Fuel_Hose()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DeviceID", DbType="VarChar(6) NOT NULL", CanBeNull=false)]
		public string DeviceID
		{
			get
			{
				return this._DeviceID;
			}
			set
			{
				if ((this._DeviceID != value))
				{
					this.OnDeviceIDChanging(value);
					this.SendPropertyChanging();
					this._DeviceID = value;
					this.SendPropertyChanged("DeviceID");
					this.OnDeviceIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(10)")]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_PumpID", DbType="Int")]
		public System.Nullable<int> PumpID
		{
			get
			{
				return this._PumpID;
			}
			set
			{
				if ((this._PumpID != value))
				{
					this.OnPumpIDChanging(value);
					this.SendPropertyChanging();
					this._PumpID = value;
					this.SendPropertyChanged("PumpID");
					this.OnPumpIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankID", DbType="Int")]
		public System.Nullable<int> TankID
		{
			get
			{
				return this._TankID;
			}
			set
			{
				if ((this._TankID != value))
				{
					this.OnTankIDChanging(value);
					this.SendPropertyChanging();
					this._TankID = value;
					this.SendPropertyChanged("TankID");
					this.OnTankIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_MeterID", DbType="Int")]
		public System.Nullable<int> MeterID
		{
			get
			{
				return this._MeterID;
			}
			set
			{
				if ((this._MeterID != value))
				{
					this.OnMeterIDChanging(value);
					this.SendPropertyChanging();
					this._MeterID = value;
					this.SendPropertyChanged("MeterID");
					this.OnMeterIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_UPC", DbType="VarChar(20)")]
		public string UPC
		{
			get
			{
				return this._UPC;
			}
			set
			{
				if ((this._UPC != value))
				{
					this.OnUPCChanging(value);
					this.SendPropertyChanging();
					this._UPC = value;
					this.SendPropertyChanged("UPC");
					this.OnUPCChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Meters")]
	public partial class Fuel_Meter : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _DeviceID;
		
		private string _Description;
		
		private System.Nullable<long> _Offset;
		
		private System.Nullable<long> _Rollover;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnDeviceIDChanging(string value);
    partial void OnDeviceIDChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnOffsetChanging(System.Nullable<long> value);
    partial void OnOffsetChanged();
    partial void OnRolloverChanging(System.Nullable<long> value);
    partial void OnRolloverChanged();
    #endregion
		
		public Fuel_Meter()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DeviceID", DbType="VarChar(6) NOT NULL", CanBeNull=false)]
		public string DeviceID
		{
			get
			{
				return this._DeviceID;
			}
			set
			{
				if ((this._DeviceID != value))
				{
					this.OnDeviceIDChanging(value);
					this.SendPropertyChanging();
					this._DeviceID = value;
					this.SendPropertyChanged("DeviceID");
					this.OnDeviceIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(20)")]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Offset", DbType="BigInt")]
		public System.Nullable<long> Offset
		{
			get
			{
				return this._Offset;
			}
			set
			{
				if ((this._Offset != value))
				{
					this.OnOffsetChanging(value);
					this.SendPropertyChanging();
					this._Offset = value;
					this.SendPropertyChanged("Offset");
					this.OnOffsetChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Rollover", DbType="BigInt")]
		public System.Nullable<long> Rollover
		{
			get
			{
				return this._Rollover;
			}
			set
			{
				if ((this._Rollover != value))
				{
					this.OnRolloverChanging(value);
					this.SendPropertyChanging();
					this._Rollover = value;
					this.SendPropertyChanged("Rollover");
					this.OnRolloverChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_ProfilePumpModes")]
	public partial class Fuel_ProfilePumpMode : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ProfileID;
		
		private int _PumpID;
		
		private string _AuthMode;
		
		private string _DispensorMode;
		
		private string _AuthModeMask;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnProfileIDChanging(int value);
    partial void OnProfileIDChanged();
    partial void OnPumpIDChanging(int value);
    partial void OnPumpIDChanged();
    partial void OnAuthModeChanging(string value);
    partial void OnAuthModeChanged();
    partial void OnDispensorModeChanging(string value);
    partial void OnDispensorModeChanged();
    partial void OnAuthModeMaskChanging(string value);
    partial void OnAuthModeMaskChanged();
    #endregion
		
		public Fuel_ProfilePumpMode()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ProfileID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ProfileID
		{
			get
			{
				return this._ProfileID;
			}
			set
			{
				if ((this._ProfileID != value))
				{
					this.OnProfileIDChanging(value);
					this.SendPropertyChanging();
					this._ProfileID = value;
					this.SendPropertyChanged("ProfileID");
					this.OnProfileIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_PumpID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int PumpID
		{
			get
			{
				return this._PumpID;
			}
			set
			{
				if ((this._PumpID != value))
				{
					this.OnPumpIDChanging(value);
					this.SendPropertyChanging();
					this._PumpID = value;
					this.SendPropertyChanged("PumpID");
					this.OnPumpIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_AuthMode", DbType="VarChar(8) NOT NULL", CanBeNull=false)]
		public string AuthMode
		{
			get
			{
				return this._AuthMode;
			}
			set
			{
				if ((this._AuthMode != value))
				{
					this.OnAuthModeChanging(value);
					this.SendPropertyChanging();
					this._AuthMode = value;
					this.SendPropertyChanged("AuthMode");
					this.OnAuthModeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DispensorMode", DbType="VarChar(8) NOT NULL", CanBeNull=false)]
		public string DispensorMode
		{
			get
			{
				return this._DispensorMode;
			}
			set
			{
				if ((this._DispensorMode != value))
				{
					this.OnDispensorModeChanging(value);
					this.SendPropertyChanging();
					this._DispensorMode = value;
					this.SendPropertyChanged("DispensorMode");
					this.OnDispensorModeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_AuthModeMask", DbType="VarChar(20) NOT NULL", CanBeNull=false)]
		public string AuthModeMask
		{
			get
			{
				return this._AuthModeMask;
			}
			set
			{
				if ((this._AuthModeMask != value))
				{
					this.OnAuthModeMaskChanging(value);
					this.SendPropertyChanging();
					this._AuthModeMask = value;
					this.SendPropertyChanged("AuthModeMask");
					this.OnAuthModeMaskChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Profiles")]
	public partial class Fuel_Profile : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _Description;
		
		private System.Nullable<System.DateTime> _FromTime;
		
		private System.Nullable<System.DateTime> _ToTime;
		
		private bool _Enabled;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnFromTimeChanging(System.Nullable<System.DateTime> value);
    partial void OnFromTimeChanged();
    partial void OnToTimeChanging(System.Nullable<System.DateTime> value);
    partial void OnToTimeChanged();
    partial void OnEnabledChanging(bool value);
    partial void OnEnabledChanged();
    #endregion
		
		public Fuel_Profile()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(50)")]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_FromTime", DbType="DateTime")]
		public System.Nullable<System.DateTime> FromTime
		{
			get
			{
				return this._FromTime;
			}
			set
			{
				if ((this._FromTime != value))
				{
					this.OnFromTimeChanging(value);
					this.SendPropertyChanging();
					this._FromTime = value;
					this.SendPropertyChanged("FromTime");
					this.OnFromTimeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ToTime", DbType="DateTime")]
		public System.Nullable<System.DateTime> ToTime
		{
			get
			{
				return this._ToTime;
			}
			set
			{
				if ((this._ToTime != value))
				{
					this.OnToTimeChanging(value);
					this.SendPropertyChanging();
					this._ToTime = value;
					this.SendPropertyChanged("ToTime");
					this.OnToTimeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Enabled", DbType="Bit NOT NULL")]
		public bool Enabled
		{
			get
			{
				return this._Enabled;
			}
			set
			{
				if ((this._Enabled != value))
				{
					this.OnEnabledChanging(value);
					this.SendPropertyChanging();
					this._Enabled = value;
					this.SendPropertyChanged("Enabled");
					this.OnEnabledChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_PumpModels")]
	public partial class Fuel_PumpModel : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _Manufacturer;
		
		private string _Name;
		
		private string _Description;
		
		private System.Nullable<short> _Hoses;
		
		private System.Nullable<short> _PriceLevels;
		
		private System.Nullable<bool> _ElectronicTotals;
		
		private string _PresetType;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnManufacturerChanging(string value);
    partial void OnManufacturerChanged();
    partial void OnNameChanging(string value);
    partial void OnNameChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnHosesChanging(System.Nullable<short> value);
    partial void OnHosesChanged();
    partial void OnPriceLevelsChanging(System.Nullable<short> value);
    partial void OnPriceLevelsChanged();
    partial void OnElectronicTotalsChanging(System.Nullable<bool> value);
    partial void OnElectronicTotalsChanged();
    partial void OnPresetTypeChanging(string value);
    partial void OnPresetTypeChanged();
    #endregion
		
		public Fuel_PumpModel()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Manufacturer", DbType="VarChar(50)")]
		public string Manufacturer
		{
			get
			{
				return this._Manufacturer;
			}
			set
			{
				if ((this._Manufacturer != value))
				{
					this.OnManufacturerChanging(value);
					this.SendPropertyChanging();
					this._Manufacturer = value;
					this.SendPropertyChanged("Manufacturer");
					this.OnManufacturerChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Name", DbType="VarChar(50)")]
		public string Name
		{
			get
			{
				return this._Name;
			}
			set
			{
				if ((this._Name != value))
				{
					this.OnNameChanging(value);
					this.SendPropertyChanging();
					this._Name = value;
					this.SendPropertyChanged("Name");
					this.OnNameChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(100)")]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Hoses", DbType="SmallInt")]
		public System.Nullable<short> Hoses
		{
			get
			{
				return this._Hoses;
			}
			set
			{
				if ((this._Hoses != value))
				{
					this.OnHosesChanging(value);
					this.SendPropertyChanging();
					this._Hoses = value;
					this.SendPropertyChanged("Hoses");
					this.OnHosesChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_PriceLevels", DbType="SmallInt")]
		public System.Nullable<short> PriceLevels
		{
			get
			{
				return this._PriceLevels;
			}
			set
			{
				if ((this._PriceLevels != value))
				{
					this.OnPriceLevelsChanging(value);
					this.SendPropertyChanging();
					this._PriceLevels = value;
					this.SendPropertyChanged("PriceLevels");
					this.OnPriceLevelsChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ElectronicTotals", DbType="Bit")]
		public System.Nullable<bool> ElectronicTotals
		{
			get
			{
				return this._ElectronicTotals;
			}
			set
			{
				if ((this._ElectronicTotals != value))
				{
					this.OnElectronicTotalsChanging(value);
					this.SendPropertyChanging();
					this._ElectronicTotals = value;
					this.SendPropertyChanged("ElectronicTotals");
					this.OnElectronicTotalsChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_PresetType", DbType="VarChar(1)")]
		public string PresetType
		{
			get
			{
				return this._PresetType;
			}
			set
			{
				if ((this._PresetType != value))
				{
					this.OnPresetTypeChanging(value);
					this.SendPropertyChanging();
					this._PresetType = value;
					this.SendPropertyChanged("PresetType");
					this.OnPresetTypeChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Pumps")]
	public partial class Fuel_Pump : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private string _Description;
		
		private string _DFValue;
		
		private string _DFVolume;
		
		private string _DFPrice;
		
		private System.Nullable<int> _H1;
		
		private System.Nullable<int> _H2;
		
		private System.Nullable<int> _H3;
		
		private System.Nullable<int> _H4;
		
		private short _StackSize;
		
		private int _PumpModelID;
		
		private string _LoopId;
		
		private string _CategoryType;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnDescriptionChanging(string value);
    partial void OnDescriptionChanged();
    partial void OnDFValueChanging(string value);
    partial void OnDFValueChanged();
    partial void OnDFVolumeChanging(string value);
    partial void OnDFVolumeChanged();
    partial void OnDFPriceChanging(string value);
    partial void OnDFPriceChanged();
    partial void OnH1Changing(System.Nullable<int> value);
    partial void OnH1Changed();
    partial void OnH2Changing(System.Nullable<int> value);
    partial void OnH2Changed();
    partial void OnH3Changing(System.Nullable<int> value);
    partial void OnH3Changed();
    partial void OnH4Changing(System.Nullable<int> value);
    partial void OnH4Changed();
    partial void OnStackSizeChanging(short value);
    partial void OnStackSizeChanged();
    partial void OnPumpModelIDChanging(int value);
    partial void OnPumpModelIDChanged();
    partial void OnLoopIdChanging(string value);
    partial void OnLoopIdChanged();
    partial void OnCategoryTypeChanging(string value);
    partial void OnCategoryTypeChanged();
    #endregion
		
		public Fuel_Pump()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Description", DbType="VarChar(20) NOT NULL", CanBeNull=false)]
		public string Description
		{
			get
			{
				return this._Description;
			}
			set
			{
				if ((this._Description != value))
				{
					this.OnDescriptionChanging(value);
					this.SendPropertyChanging();
					this._Description = value;
					this.SendPropertyChanged("Description");
					this.OnDescriptionChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DFValue", DbType="VarChar(10) NOT NULL", CanBeNull=false)]
		public string DFValue
		{
			get
			{
				return this._DFValue;
			}
			set
			{
				if ((this._DFValue != value))
				{
					this.OnDFValueChanging(value);
					this.SendPropertyChanging();
					this._DFValue = value;
					this.SendPropertyChanged("DFValue");
					this.OnDFValueChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DFVolume", DbType="VarChar(10) NOT NULL", CanBeNull=false)]
		public string DFVolume
		{
			get
			{
				return this._DFVolume;
			}
			set
			{
				if ((this._DFVolume != value))
				{
					this.OnDFVolumeChanging(value);
					this.SendPropertyChanging();
					this._DFVolume = value;
					this.SendPropertyChanged("DFVolume");
					this.OnDFVolumeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_DFPrice", DbType="VarChar(10) NOT NULL", CanBeNull=false)]
		public string DFPrice
		{
			get
			{
				return this._DFPrice;
			}
			set
			{
				if ((this._DFPrice != value))
				{
					this.OnDFPriceChanging(value);
					this.SendPropertyChanging();
					this._DFPrice = value;
					this.SendPropertyChanged("DFPrice");
					this.OnDFPriceChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_H1", DbType="Int")]
		public System.Nullable<int> H1
		{
			get
			{
				return this._H1;
			}
			set
			{
				if ((this._H1 != value))
				{
					this.OnH1Changing(value);
					this.SendPropertyChanging();
					this._H1 = value;
					this.SendPropertyChanged("H1");
					this.OnH1Changed();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_H2", DbType="Int")]
		public System.Nullable<int> H2
		{
			get
			{
				return this._H2;
			}
			set
			{
				if ((this._H2 != value))
				{
					this.OnH2Changing(value);
					this.SendPropertyChanging();
					this._H2 = value;
					this.SendPropertyChanged("H2");
					this.OnH2Changed();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_H3", DbType="Int")]
		public System.Nullable<int> H3
		{
			get
			{
				return this._H3;
			}
			set
			{
				if ((this._H3 != value))
				{
					this.OnH3Changing(value);
					this.SendPropertyChanging();
					this._H3 = value;
					this.SendPropertyChanged("H3");
					this.OnH3Changed();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_H4", DbType="Int")]
		public System.Nullable<int> H4
		{
			get
			{
				return this._H4;
			}
			set
			{
				if ((this._H4 != value))
				{
					this.OnH4Changing(value);
					this.SendPropertyChanging();
					this._H4 = value;
					this.SendPropertyChanged("H4");
					this.OnH4Changed();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_StackSize", DbType="SmallInt NOT NULL")]
		public short StackSize
		{
			get
			{
				return this._StackSize;
			}
			set
			{
				if ((this._StackSize != value))
				{
					this.OnStackSizeChanging(value);
					this.SendPropertyChanging();
					this._StackSize = value;
					this.SendPropertyChanged("StackSize");
					this.OnStackSizeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_PumpModelID", DbType="Int NOT NULL")]
		public int PumpModelID
		{
			get
			{
				return this._PumpModelID;
			}
			set
			{
				if ((this._PumpModelID != value))
				{
					this.OnPumpModelIDChanging(value);
					this.SendPropertyChanging();
					this._PumpModelID = value;
					this.SendPropertyChanged("PumpModelID");
					this.OnPumpModelIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_LoopId", DbType="VarChar(10) NOT NULL", CanBeNull=false)]
		public string LoopId
		{
			get
			{
				return this._LoopId;
			}
			set
			{
				if ((this._LoopId != value))
				{
					this.OnLoopIdChanging(value);
					this.SendPropertyChanging();
					this._LoopId = value;
					this.SendPropertyChanged("LoopId");
					this.OnLoopIdChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_CategoryType", DbType="VarChar(1)")]
		public string CategoryType
		{
			get
			{
				return this._CategoryType;
			}
			set
			{
				if ((this._CategoryType != value))
				{
					this.OnCategoryTypeChanging(value);
					this.SendPropertyChanging();
					this._CategoryType = value;
					this.SendPropertyChanged("CategoryType");
					this.OnCategoryTypeChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_Recon")]
	public partial class Fuel_Recon : INotifyPropertyChanging, INotifyPropertyChanged
	{
		
		private static PropertyChangingEventArgs emptyChangingEventArgs = new PropertyChangingEventArgs(String.Empty);
		
		private int _ID;
		
		private int _TransNo;
		
		private short _Branch;
		
		private short _Station;
		
		private string _TheUser;
		
		private System.Nullable<int> _Status;
		
		private System.DateTime _Logged;
		
		private string _Type;
		
		private System.Nullable<System.DateTime> _StartDate;
		
		private System.Nullable<System.DateTime> _EndDate;
		
		private System.Nullable<decimal> _SalesQty;
		
		private System.Nullable<decimal> _MeterQty;
		
		private System.Nullable<decimal> _TankLevelStart;
		
		private System.Nullable<decimal> _TankLevelEnd;
		
		private System.Nullable<decimal> _TestDeliveryQty;
		
		private System.Nullable<decimal> _SMReceiptsQty;
		
		private string _UPC;
		
		private string _TankDesc;
		
		private string _Note;
		
    #region Extensibility Method Definitions
    partial void OnLoaded();
    partial void OnValidate(System.Data.Linq.ChangeAction action);
    partial void OnCreated();
    partial void OnIDChanging(int value);
    partial void OnIDChanged();
    partial void OnTransNoChanging(int value);
    partial void OnTransNoChanged();
    partial void OnBranchChanging(short value);
    partial void OnBranchChanged();
    partial void OnStationChanging(short value);
    partial void OnStationChanged();
    partial void OnTheUserChanging(string value);
    partial void OnTheUserChanged();
    partial void OnStatusChanging(System.Nullable<int> value);
    partial void OnStatusChanged();
    partial void OnLoggedChanging(System.DateTime value);
    partial void OnLoggedChanged();
    partial void OnTypeChanging(string value);
    partial void OnTypeChanged();
    partial void OnStartDateChanging(System.Nullable<System.DateTime> value);
    partial void OnStartDateChanged();
    partial void OnEndDateChanging(System.Nullable<System.DateTime> value);
    partial void OnEndDateChanged();
    partial void OnSalesQtyChanging(System.Nullable<decimal> value);
    partial void OnSalesQtyChanged();
    partial void OnMeterQtyChanging(System.Nullable<decimal> value);
    partial void OnMeterQtyChanged();
    partial void OnTankLevelStartChanging(System.Nullable<decimal> value);
    partial void OnTankLevelStartChanged();
    partial void OnTankLevelEndChanging(System.Nullable<decimal> value);
    partial void OnTankLevelEndChanged();
    partial void OnTestDeliveryQtyChanging(System.Nullable<decimal> value);
    partial void OnTestDeliveryQtyChanged();
    partial void OnSMReceiptsQtyChanging(System.Nullable<decimal> value);
    partial void OnSMReceiptsQtyChanged();
    partial void OnUPCChanging(string value);
    partial void OnUPCChanged();
    partial void OnTankDescChanging(string value);
    partial void OnTankDescChanged();
    partial void OnNoteChanging(string value);
    partial void OnNoteChanged();
    #endregion
		
		public Fuel_Recon()
		{
			OnCreated();
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ID", AutoSync=AutoSync.Always, DbType="Int NOT NULL IDENTITY", IsDbGenerated=true)]
		public int ID
		{
			get
			{
				return this._ID;
			}
			set
			{
				if ((this._ID != value))
				{
					this.OnIDChanging(value);
					this.SendPropertyChanging();
					this._ID = value;
					this.SendPropertyChanged("ID");
					this.OnIDChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TransNo", DbType="Int NOT NULL", IsPrimaryKey=true)]
		public int TransNo
		{
			get
			{
				return this._TransNo;
			}
			set
			{
				if ((this._TransNo != value))
				{
					this.OnTransNoChanging(value);
					this.SendPropertyChanging();
					this._TransNo = value;
					this.SendPropertyChanged("TransNo");
					this.OnTransNoChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Branch", DbType="SmallInt NOT NULL", IsPrimaryKey=true)]
		public short Branch
		{
			get
			{
				return this._Branch;
			}
			set
			{
				if ((this._Branch != value))
				{
					this.OnBranchChanging(value);
					this.SendPropertyChanging();
					this._Branch = value;
					this.SendPropertyChanged("Branch");
					this.OnBranchChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Station", DbType="SmallInt NOT NULL", IsPrimaryKey=true)]
		public short Station
		{
			get
			{
				return this._Station;
			}
			set
			{
				if ((this._Station != value))
				{
					this.OnStationChanging(value);
					this.SendPropertyChanging();
					this._Station = value;
					this.SendPropertyChanged("Station");
					this.OnStationChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TheUser", DbType="NChar(10)")]
		public string TheUser
		{
			get
			{
				return this._TheUser;
			}
			set
			{
				if ((this._TheUser != value))
				{
					this.OnTheUserChanging(value);
					this.SendPropertyChanging();
					this._TheUser = value;
					this.SendPropertyChanged("TheUser");
					this.OnTheUserChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Status", DbType="Int")]
		public System.Nullable<int> Status
		{
			get
			{
				return this._Status;
			}
			set
			{
				if ((this._Status != value))
				{
					this.OnStatusChanging(value);
					this.SendPropertyChanging();
					this._Status = value;
					this.SendPropertyChanged("Status");
					this.OnStatusChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Logged", DbType="DateTime NOT NULL")]
		public System.DateTime Logged
		{
			get
			{
				return this._Logged;
			}
			set
			{
				if ((this._Logged != value))
				{
					this.OnLoggedChanging(value);
					this.SendPropertyChanging();
					this._Logged = value;
					this.SendPropertyChanged("Logged");
					this.OnLoggedChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Type", DbType="VarChar(1)")]
		public string Type
		{
			get
			{
				return this._Type;
			}
			set
			{
				if ((this._Type != value))
				{
					this.OnTypeChanging(value);
					this.SendPropertyChanging();
					this._Type = value;
					this.SendPropertyChanged("Type");
					this.OnTypeChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_StartDate", DbType="DateTime")]
		public System.Nullable<System.DateTime> StartDate
		{
			get
			{
				return this._StartDate;
			}
			set
			{
				if ((this._StartDate != value))
				{
					this.OnStartDateChanging(value);
					this.SendPropertyChanging();
					this._StartDate = value;
					this.SendPropertyChanged("StartDate");
					this.OnStartDateChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_EndDate", DbType="DateTime")]
		public System.Nullable<System.DateTime> EndDate
		{
			get
			{
				return this._EndDate;
			}
			set
			{
				if ((this._EndDate != value))
				{
					this.OnEndDateChanging(value);
					this.SendPropertyChanging();
					this._EndDate = value;
					this.SendPropertyChanged("EndDate");
					this.OnEndDateChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_SalesQty", DbType="Money")]
		public System.Nullable<decimal> SalesQty
		{
			get
			{
				return this._SalesQty;
			}
			set
			{
				if ((this._SalesQty != value))
				{
					this.OnSalesQtyChanging(value);
					this.SendPropertyChanging();
					this._SalesQty = value;
					this.SendPropertyChanged("SalesQty");
					this.OnSalesQtyChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_MeterQty", DbType="Money")]
		public System.Nullable<decimal> MeterQty
		{
			get
			{
				return this._MeterQty;
			}
			set
			{
				if ((this._MeterQty != value))
				{
					this.OnMeterQtyChanging(value);
					this.SendPropertyChanging();
					this._MeterQty = value;
					this.SendPropertyChanged("MeterQty");
					this.OnMeterQtyChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankLevelStart", DbType="Money")]
		public System.Nullable<decimal> TankLevelStart
		{
			get
			{
				return this._TankLevelStart;
			}
			set
			{
				if ((this._TankLevelStart != value))
				{
					this.OnTankLevelStartChanging(value);
					this.SendPropertyChanging();
					this._TankLevelStart = value;
					this.SendPropertyChanged("TankLevelStart");
					this.OnTankLevelStartChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankLevelEnd", DbType="Money")]
		public System.Nullable<decimal> TankLevelEnd
		{
			get
			{
				return this._TankLevelEnd;
			}
			set
			{
				if ((this._TankLevelEnd != value))
				{
					this.OnTankLevelEndChanging(value);
					this.SendPropertyChanging();
					this._TankLevelEnd = value;
					this.SendPropertyChanged("TankLevelEnd");
					this.OnTankLevelEndChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TestDeliveryQty", DbType="Money")]
		public System.Nullable<decimal> TestDeliveryQty
		{
			get
			{
				return this._TestDeliveryQty;
			}
			set
			{
				if ((this._TestDeliveryQty != value))
				{
					this.OnTestDeliveryQtyChanging(value);
					this.SendPropertyChanging();
					this._TestDeliveryQty = value;
					this.SendPropertyChanged("TestDeliveryQty");
					this.OnTestDeliveryQtyChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_SMReceiptsQty", DbType="Money")]
		public System.Nullable<decimal> SMReceiptsQty
		{
			get
			{
				return this._SMReceiptsQty;
			}
			set
			{
				if ((this._SMReceiptsQty != value))
				{
					this.OnSMReceiptsQtyChanging(value);
					this.SendPropertyChanging();
					this._SMReceiptsQty = value;
					this.SendPropertyChanged("SMReceiptsQty");
					this.OnSMReceiptsQtyChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_UPC", DbType="VarChar(20)")]
		public string UPC
		{
			get
			{
				return this._UPC;
			}
			set
			{
				if ((this._UPC != value))
				{
					this.OnUPCChanging(value);
					this.SendPropertyChanging();
					this._UPC = value;
					this.SendPropertyChanged("UPC");
					this.OnUPCChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TankDesc", DbType="VarChar(20)")]
		public string TankDesc
		{
			get
			{
				return this._TankDesc;
			}
			set
			{
				if ((this._TankDesc != value))
				{
					this.OnTankDescChanging(value);
					this.SendPropertyChanging();
					this._TankDesc = value;
					this.SendPropertyChanged("TankDesc");
					this.OnTankDescChanged();
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Note", DbType="VarChar(100)")]
		public string Note
		{
			get
			{
				return this._Note;
			}
			set
			{
				if ((this._Note != value))
				{
					this.OnNoteChanging(value);
					this.SendPropertyChanging();
					this._Note = value;
					this.SendPropertyChanged("Note");
					this.OnNoteChanged();
				}
			}
		}
		
		public event PropertyChangingEventHandler PropertyChanging;
		
		public event PropertyChangedEventHandler PropertyChanged;
		
		protected virtual void SendPropertyChanging()
		{
			if ((this.PropertyChanging != null))
			{
				this.PropertyChanging(this, emptyChangingEventArgs);
			}
		}
		
		protected virtual void SendPropertyChanged(String propertyName)
		{
			if ((this.PropertyChanged != null))
			{
				this.PropertyChanged(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.Fuel_ReconDetails")]
	public partial class Fuel_ReconDetail
	{
		
		private int _TransNo;
		
		private short _Branch;
		
		private short _Station;
		
		private short _Line;
		
		private System.Nullable<decimal> _OpenValue;
		
		private System.Nullable<decimal> _CloseValue;
		
		private System.Nullable<decimal> _MovementValue;
		
		private string _Type;
		
		private int _ItemID;
		
		public Fuel_ReconDetail()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_TransNo", DbType="Int NOT NULL")]
		public int TransNo
		{
			get
			{
				return this._TransNo;
			}
			set
			{
				if ((this._TransNo != value))
				{
					this._TransNo = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Branch", DbType="SmallInt NOT NULL")]
		public short Branch
		{
			get
			{
				return this._Branch;
			}
			set
			{
				if ((this._Branch != value))
				{
					this._Branch = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Station", DbType="SmallInt NOT NULL")]
		public short Station
		{
			get
			{
				return this._Station;
			}
			set
			{
				if ((this._Station != value))
				{
					this._Station = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Line", DbType="SmallInt NOT NULL")]
		public short Line
		{
			get
			{
				return this._Line;
			}
			set
			{
				if ((this._Line != value))
				{
					this._Line = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_OpenValue", DbType="Money")]
		public System.Nullable<decimal> OpenValue
		{
			get
			{
				return this._OpenValue;
			}
			set
			{
				if ((this._OpenValue != value))
				{
					this._OpenValue = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_CloseValue", DbType="Money")]
		public System.Nullable<decimal> CloseValue
		{
			get
			{
				return this._CloseValue;
			}
			set
			{
				if ((this._CloseValue != value))
				{
					this._CloseValue = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_MovementValue", DbType="Money")]
		public System.Nullable<decimal> MovementValue
		{
			get
			{
				return this._MovementValue;
			}
			set
			{
				if ((this._MovementValue != value))
				{
					this._MovementValue = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_Type", DbType="VarChar(1) NOT NULL", CanBeNull=false)]
		public string Type
		{
			get
			{
				return this._Type;
			}
			set
			{
				if ((this._Type != value))
				{
					this._Type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ItemID", DbType="Int NOT NULL")]
		public int ItemID
		{
			get
			{
				return this._ItemID;
			}
			set
			{
				if ((this._ItemID != value))
				{
					this._ItemID = value;
				}
			}
		}
	}
}
#pragma warning restore 1591
