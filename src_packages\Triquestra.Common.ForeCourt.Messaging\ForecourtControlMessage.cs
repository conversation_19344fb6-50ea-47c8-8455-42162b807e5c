﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging
{
    public class ForecourtControlMessage:ForecourtMessage, IForecourtControlMessage
    {
        public ForecourtControlMessage(int sequenceNo, int sourceId, int targetId,
            ForecourtMessageClasses messageClass)
            : base(messageClass, sequenceNo)
        {
            SequenceNo = sequenceNo;
            SourceId = sourceId;
            TargetId = targetId;            
        }

        public int SourceId { get; set; }

        public int TargetId { get; set; }

        

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            xdoc.Root.Element("Header").Add(new XAttribute("SourceID", SourceId));
            xdoc.Root.Element("Header").Add(new XAttribute("TargetID", TargetId));
            return xdoc;
        }

    }
}
