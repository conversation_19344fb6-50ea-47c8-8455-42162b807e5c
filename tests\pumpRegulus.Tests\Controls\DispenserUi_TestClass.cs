﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Triquestra.Common.PumpEsm.Controls;
using Moq;
using Triquestra.Common.PumpEsm.Messaging;
using System.Linq;
using System.Collections.Generic;

namespace pumpRegulus.Tests.Controls
{
    [TestClass]
    public class DispenserUi_TestClass
    {
        [TestMethod]
        public void DispenserUi_TestClass_NoDeliveryCreatedWhenWeCallTwiceWithTheSameDeliveryId()
        {
            int blendId = 1;

            int deliveryId = 2;

            DispenserUi dispenserControl = new DispenserUi();

            dispenserControl.Blends = new List<IForecourtBlend>
            {
                new ForecourtBlend( blendId, "Blend 1")
            };

            Assert.AreEqual(0, dispenserControl.DeliveryList.Count(), "No deliveries expected");

            Mock<IDelivery> firstDelivery = new Mock<IDelivery>();

            firstDelivery.Setup(t => t.BlendId).Returns(blendId);
            firstDelivery.Setup(t => t.DeliveryId).Returns(deliveryId);

            dispenserControl.StartDelivery(firstDelivery.Object);

            Assert.AreEqual(1, dispenserControl.DeliveryList.Count(), "1 delivery expected");

            dispenserControl.SetDeliveryState(deliveryId, DeliveryStates.COMPLETED);

            Mock<IDelivery> secondDelivery = new Mock<IDelivery>();

            int deliveryId2 = 3;
            secondDelivery.Setup(t => t.BlendId).Returns(blendId);
            secondDelivery.Setup(t => t.DeliveryId).Returns(deliveryId2);

            dispenserControl.StartDelivery(secondDelivery.Object);

            Assert.AreEqual(2, dispenserControl.DeliveryList.Count(), "2 deliveries expected");

            Mock<IDelivery> thirdDelivery = new Mock<IDelivery>();

            // new repeat message with FIRST delivery Id
            thirdDelivery.Setup(t => t.BlendId).Returns(blendId);
            thirdDelivery.Setup(t => t.DeliveryId).Returns(deliveryId);

            dispenserControl.StartDelivery(thirdDelivery.Object);

            Assert.AreEqual(2, dispenserControl.DeliveryList.Count(), "2 deliveries expected");
        }
    }
}
