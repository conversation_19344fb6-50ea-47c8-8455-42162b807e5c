﻿using System;
using System.Net.Sockets;
using System.Text;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Comms
{
    public interface IForecourtConnection
    {

        Socket TcpSocket { get; }

        bool IsConnected { get; }

        void Connect();

        void Disconnect();

        void SendCommand(ForecourtCommandMessageTypes commandType);

        void SendCommand(int sourceId, ForecourtCommandMessageTypes commandType);

        void SendCommand(int sourceId, int targetId, ForecourtCommandMessageTypes commandType);

        void SendCommand(IForecourtMessage command);

        void SendSystemRequest(ForecourtCommandMessageTypes commandType, string name);
        /// <summary>
        /// <param name="sender">The list of messages to process. (List&lt;IForecourtMessage&gt;)</param>
        /// </summary>
        event EventHandler MessageReceived;

        event EventHandler Connected;

    }

    internal class SocketReceiveState
    {
        public Socket WorkSocket = null;
        public StringBuilder Data = new StringBuilder();
        public const int BufferSize = 0x20000;  //128KB while so far biggest msg received was 9938 bytes
        public byte[] buffer = new byte[BufferSize];
        public int ElementCount = 0;
    }
}
