﻿using System;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DomsJplProtocol.Network;
using DomsJplProtocol.Services;

namespace DomsJplProtocol.Tests
{
    [TestClass]
    public class DomsControllerIntegrationTests
    {
        // NOTE: These tests require a real DOMS controller to be available
        // For unit testing without a controller, you would need to mock the responses
        
        private const string CONTROLLER_IP = "127.0.0.1"; // Change to your controller IP
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task ConnectToController_ShouldSucceed()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                bool connected = await client.ConnectAsync();
                
                // Assert
                Assert.IsTrue(connected);
                Assert.IsTrue(client.IsConnected);
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task LogonToController_ShouldSucceed()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                await client.ConnectAsync();
                var response = await client.LogonAsync(
                    "POS,APPL_ID=TEST,RI", 
                    "0000", 
                    "TEST_1.0");
                
                // Assert
                Assert.IsNotNull(response);
                Assert.IsTrue(client.IsLoggedOn);
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
        
        [TestMethod]
        [Ignore("Integration test requires real controller")]
        public async Task GetDateTimeFromController_ShouldReturnValidData()
        {
            // Arrange
            var client = PssConnectionFactory.CreateUnencryptedClient(CONTROLLER_IP);
            
            try
            {
                // Act
                await client.ConnectAsync();
                await client.LogonAsync("POS,APPL_ID=TEST,RI", "0000", "TEST_1.0");
                
                var service = new ForecourtControllerService(client);
                var dateTimeResponse = await service.GetDateTimeAsync();
                
                // Assert
                Assert.IsNotNull(dateTimeResponse);
                Assert.IsNotNull(dateTimeResponse.Data);
                Assert.IsFalse(string.IsNullOrEmpty(dateTimeResponse.Data.FcDateAndTime));
            }
            finally
            {
                await client.DisconnectAsync();
            }
        }
    }
}
