﻿using System;
using System.Xml;

namespace Triquestra.Common.PumpEsm.Base
{
    public class PumpEsmRequest
    {        
        public string Action
        {
            get { return request.SelectSingleNode("action").InnerText; }
        }

        protected XmlNode request;

        /// <summary>
        /// Creates an instance of the object.
        /// </summary>
        /// <param name="requestDoc">The request in XML format.</param>
        public PumpEsmRequest(XmlDocument requestDoc)
        {
            this.request = requestDoc.SelectSingleNode(@"/pumpesmrequest");
            var msgNode = request.SelectSingleNode("message");
            if (msgNode != null)
                Message = msgNode.InnerText;
            else
                Message = null;
        }

        
        /// <summary>
        /// Original message passed by Core
        /// </summary>
        public string Message { get; set; }        
    }
}
