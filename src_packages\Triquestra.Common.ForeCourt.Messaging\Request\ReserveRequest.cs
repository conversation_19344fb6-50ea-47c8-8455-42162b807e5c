﻿using System.Xml.Linq;

namespace Triquestra.Common.PumpEsm.Messaging.Request
{
    public class ReserveRequest:ForecourtCommandRequest
    {
        public decimal Limit { get; set; }
        public IForecourtBlend Blend { get; set; }

        public ReserveRequest(int sequenceNo, int sourceId, int targetId, decimal limit, IForecourtBlend blend) 
            : base(sequenceNo, sourceId, targetId, ForecourtCommandMessageTypes.RESERVE)
        {
            Limit = limit;
            Blend = blend;
        }

        public override XDocument Serialise()
        {
            var xdoc = base.Serialise();
            if (xdoc.Root == null) return xdoc;
            var req = xdoc.Root.Element("CommandReq");
            if (req == null) return xdoc;
            req.Add(
                new XElement("DispenserReserve",
                    new XAttribute("Limit",Limit),
                    new XElement("Blends",
                        new XElement("Blend",
                            new XAttribute("ID",Blend.BlendId),
                            new XAttribute("Name",Blend.BlendName)))
                    )
                );
            return xdoc;
        }
    }
}
