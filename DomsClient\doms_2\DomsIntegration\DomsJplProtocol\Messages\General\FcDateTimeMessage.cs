﻿using System;
using System.Text.Json.Serialization;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Messages.General
{
    public class FcDateTimeRequest : JplRequest
    {
        public class FcDateTimeData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }
        }
    }

    public class FcDateTimeResponse : JplResponse
    {
        public class FcDateTimeResponseData
        {
            [JsonPropertyName("FcDateAndTime")]
            public string FcDateAndTime { get; set; }

            [JsonPropertyName("LastDateAndTimeSetting")]
            public string LastDateAndTimeSetting { get; set; }
        }
    }
}
