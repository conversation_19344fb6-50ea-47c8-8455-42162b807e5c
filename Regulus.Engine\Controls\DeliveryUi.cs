﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Triquestra.Common.PumpEsm.Messaging;

namespace Triquestra.Common.PumpEsm.Controls
{
    public sealed partial class DeliveryUi : UserControl,IDelivery
    {
 
        public DeliveryUi()
        {
            InitializeComponent();
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            BackColor = Color.Transparent;
            ProductNameText.BackColor = Color.Transparent;
            VolumeText.BackColor = Color.Transparent;
            PriceText.BackColor = Color.Transparent;
            ProductNameText.Text = "";
            PriceText.Text = "";
            VolumeText.Text = "";
            ImgDelivery.Visible = false;
        }
        private DeliveryStates _state;

        public int DeliveryId { get; set; }
        public int DispenserId { get; set; }
        public int NozzleId { get; set; }
        public int BlendId { get; set; }
        public int TankId { get; set; }
        public AuthModes Mode { get; set; }
        public LimitTypes LimitType { get; set; }
        public decimal Limit { get; set; }

        public void EnableStackImage()
        {
            ProductNameText.Visible = false;
            PriceText.Visible = false;
            VolumeText.Visible = false;
            ImgDelivery.Visible = true;
        }

        public void DisableStackImage()
        {
            ProductNameText.Visible = true;
            PriceText.Visible = true;
            VolumeText.Visible = true;
            ImgDelivery.Visible = false;
        }

        private DeliveryExceptions _deliveryException;
        private bool _isTimedOut;
        public DeliveryExceptions Exception
        {
            get { return _deliveryException; }
            set
            {
                _deliveryException = value;
                if (value == DeliveryExceptions.AUTHORIZED_ABORTED)
                    _isTimedOut = true;
            }
        }
        private decimal _amount;
        public decimal Amount
        {
            get { return _amount; }
            set
            {
                //CR17090 Req 3: round price to 2 decimal chars
                _amount = value;
                //b146880 round to 2 decimal places using 'emotional' rules
                var val = Math.Truncate(value * 100m + 0.4m) / 100m;
                //b146948 it seems like POS calculates on rounded values                
                //b148880 - UPD: it started rounding to int values when possible. Changing to C2
                PriceText.Text = val.ToString("C2");
            }
        }

        public decimal Price { get; set; }
        public int PriceId { get; set; }
        public int PriceLevel { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsCurrent { get; set; }
        public int? Owner { get; set; }

        private decimal _volume;
        public decimal Volume
        {
            get { return _volume; }
            set
            {
                _volume = value;
                VolumeText.Text = _volume + @" LT";
            }
        }

        public string BlendName
        {
            get { return ProductNameText.Text; }
            set { ProductNameText.Text = value; }
        }

        public DeliveryStates State
        {
            get { return _state; }
            set
            {
                if (_state == value) return;
                _state = value;
                if (IsLocked())//b146013 the timeout thing needs an additional BA assessment
                    LockDelivery();
                else
                    UnlockDelivery();
            }
        }
        /// <summary>
        /// Returns true if the current delivery is locked
        /// <remarks>It doesn't specify a workstation locking the delivery</remarks>
        /// </summary>
        /// <returns></returns>
        public bool IsLocked()
        {
            return _state == DeliveryStates.REFUND_FINALIZING || _state == DeliveryStates.FINALIZING;
        }

        public bool IsLockedByStation(int stationId)
        {
            return (_state == DeliveryStates.REFUND_FINALIZING || _state == DeliveryStates.FINALIZING) && (Owner == null || Owner == stationId);
        }

        public void LockDelivery()
        {
            VolumeText.ForeColor = Color.Red;
            PriceText.ForeColor = Color.Red;
            ProductNameText.ForeColor = Color.Red;

        }

        public void UnlockDelivery()
        {
            VolumeText.ForeColor = Color.Black;
            PriceText.ForeColor = Color.Black;
            ProductNameText.ForeColor = Color.Black;           
        }

        public bool IsTimedOut()
        {
            return _isTimedOut;
        }

        public event EventHandler OnUserClick
        {
            add
            {
                this.Click += value;
                this.PriceText.Click += value;
                this.VolumeText.Click += value;
                this.ProductNameText.Click += value;
                this.ImgDelivery.Click += value;
            }
            remove
            {
                this.Click -= value;
                this.PriceText.Click -= value;
                this.VolumeText.Click -= value;
                this.ProductNameText.Click -= value;
                this.ImgDelivery.Click -= value;
            }
        }
    }
}
