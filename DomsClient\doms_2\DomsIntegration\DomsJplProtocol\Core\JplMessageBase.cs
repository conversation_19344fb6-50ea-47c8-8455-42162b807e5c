﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace DomsJplProtocol.Core
{
    // Base message class
    public abstract class JplMessage
    {
        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("subCode")]
        public string SubCode { get; set; }

        [JsonPropertyName("correlationId")]
        public object CorrelationId { get; set; }
    }

    // Request message base
    public abstract class JplRequest : JplMessage
    {
        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Response message base
    public abstract class JplResponse : JplMessage
    {
        [JsonPropertyName("solicited")]
        public bool Solicited { get; set; }

        [JsonPropertyName("data")]
        public object Data { get; set; }
    }

    // Enumeration wrapper
    public class EnumValue<T>
    {
        [JsonPropertyName("enum")]
        public Dictionary<string, string> Enum { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }
        
        // Helper method to get the enum value as a string
        public string GetValueString()
        {
            return Value;
        }
        
        // Helper method to check if a specific enum value is set
        public bool IsValue(string enumKey)
        {
            if (Enum == null || !Enum.ContainsKey(enumKey))
                return false;
                
            return Enum[enumKey] == Value;
        }
    }

    // Bit flags wrapper
    public class BitFlags
    {
        [JsonPropertyName("value")]
        public int Value { get; set; }

        [JsonPropertyName("bits")]
        public Dictionary<string, int> Bits { get; set; }
        
        // Helper method to check if a specific bit is set
        public bool IsBitSet(string bitName)
        {
            if (Bits == null || !Bits.ContainsKey(bitName))
                return false;
                
            return (Value & Bits[bitName]) != 0;
        }
    }

    // Multi-message container
    public class MultiMessageResponse : JplResponse
    {
        public class MultiMessageData
        {
            [JsonPropertyName("messages")]
            public List<JplResponse> Messages { get; set; }
        }
    }

    // Reject message
    public class RejectMessageResponse : JplResponse
    {
        public class RejectData
        {
            [JsonPropertyName("RejectedExtendedMsgCode")]
            public string RejectedExtendedMsgCode { get; set; }

            [JsonPropertyName("RejectedMsgSubc")]
            public string RejectedMsgSubc { get; set; }

            [JsonPropertyName("RejectCode")]
            public EnumValue<string> RejectCode { get; set; }

            [JsonPropertyName("RejectInfo")]
            public string RejectInfo { get; set; }

            [JsonPropertyName("RejectInfoText")]
            public string RejectInfoText { get; set; }
        }
    }
    
    // Heartbeat message
    public class HeartbeatMessage : JplResponse
    {
        public class HeartbeatData
        {
            // Empty data object
        }
    }
}
