﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsJplProtocol.Core;
using DomsJplProtocol.Messages.PriceDisplay;
using DomsJplProtocol.Network;

namespace DomsJplProtocol.Services
{
    public class PriceDisplayService
    {
        private readonly PssClient _client;

        public PriceDisplayService(PssClient client)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
        }

        public async Task<PriceDisplayStatusResponse> GetPriceDisplayStatusAsync(string priceDisplayId, 
            CancellationToken cancellationToken = default)
        {
            var request = new PriceDisplayStatusRequest
            {
                Name = "PriceDisplayStatus_req", // Not included in the constants yet
                SubCode = JplConstants.SubCodes.SUBC_00H,
                Data = new PriceDisplayStatusRequest.PriceDisplayStatusRequestData
                {
                    PriceDisplayId = priceDisplayId
                }
            };

            return await _client.SendRequestAsync<PriceDisplayStatusResponse>(request, cancellationToken);
        }
    }
}
