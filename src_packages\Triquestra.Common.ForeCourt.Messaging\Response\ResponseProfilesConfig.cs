﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ResponseProfilesConfig : ConfigResponse
    {
        public ResponseProfilesConfig(ForecourtCommandResults commandResult, int currentProfileIndex, IEnumerable<IAuthModeProfile> profiles) 
            : base(ForecourtConfigurationTypes.GET_AUTHMODE_PROFILES, commandResult)
        {
            CurrentProfileIndex = currentProfileIndex;
            Profiles = profiles;
        }

        public int CurrentProfileIndex { get; set; }

        public IEnumerable<IAuthModeProfile> Profiles { get; set; }


        public static ResponseProfilesConfig Deserialise(XContainer messageNode, ForecourtCommandResults result)
        {
            var profilesNode = messageNode.Element("AuthModeProfiles");
            if (profilesNode == null)
            {
                throw new XmlSchemaException("Node does not have <AuthModeProfiles> node");
            }
            var currentProfileIndex = 0;
            if (profilesNode.Attribute("CurrentProfileIndex") == null ||
                !int.TryParse(profilesNode.Attribute("CurrentProfileIndex").Value, out currentProfileIndex))
            {
                 throw new XmlSchemaException("Node does not have CurrentProfileIndex attribute");
            }
            var profiles = profilesNode.Descendants("Profile").Select(profileX =>
                new AuthModeProfile
                {
                    Name = (profileX.Attribute("Name") != null ? profileX.Attribute("Name").Value : string.Empty),
                    AuthModeMask =
                        (profileX.Attribute("AuthModeMask") != null
                            ? profileX.Attribute("AuthModeMask").Value
                            : string.Empty)
                }).Cast<IAuthModeProfile>().ToList();
            return new ResponseProfilesConfig(result, currentProfileIndex, profiles);
        }

    }
}
