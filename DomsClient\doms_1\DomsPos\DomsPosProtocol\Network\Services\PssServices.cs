﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network.Services
{
    public abstract class PssServiceBase
    {
        protected readonly PssClient Client;

        protected PssServiceBase(PssClient client)
        {
            Client = client ?? throw new ArgumentNullException(nameof(client));
        }

        protected async Task<T> SendRequestAsync<T>(JplRequest request, CancellationToken cancellationToken = default(CancellationToken))
            where T : JplResponse
        {
            return await Client.SendRequestAsync<T>(request, cancellationToken);
        }
    }

    public class ForecourtControllerService : PssServiceBase
    {
        public ForecourtControllerService(PssClient client) : base(client) { }

        public async Task<FcStatusResponse> GetStatusAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcStatusRequest
            {
                Name = MessageNames.FC_STATUS_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcStatusRequest.FcStatusData()
            };

            return await SendRequestAsync<FcStatusResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> GetDateTimeAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcDateTimeRequest
            {
                Name = MessageNames.FC_DATE_TIME_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData()
            };

            return await SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }

        public async Task<FcDateTimeResponse> SetDateTimeAsync(DateTime dateTime, CancellationToken cancellationToken = default(CancellationToken))
        {
            var request = new FcDateTimeRequest
            {
                Name = MessageNames.CHANGE_FC_DATE_TIME_REQ,
                SubCode = SubCodes.SUBC_00H,
                Data = new FcDateTimeRequest.FcDateTimeData
                {
                    FcDateAndTime = dateTime.ToString("yyyyMMddHHmmss")
                }
            };

            return await SendRequestAsync<FcDateTimeResponse>(request, cancellationToken);
        }
    }
}
