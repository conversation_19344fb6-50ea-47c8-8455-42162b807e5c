﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Diagnostics;

namespace Triquestra.Common.PumpEsm.Base.Helpers
{
    static class EventLogger
    {
        internal static void LogError(Exception ex)
        {
            string sSource = "Infinity PumpESM Base Library";
            string sLog = "Application";
            string sEvent = ex.Message;
            string sStack = ex.StackTrace;

            try
            {
                if (!EventLog.SourceExists(sSource))
                    EventLog.CreateEventSource(sSource, sLog);

                EventLog.WriteEntry(sSource, sEvent + System.Environment.NewLine + sStack, EventLogEntryType.Error);
            }
            catch
            {
                //MessageBox.Show("Could not write to error log: " + e.Message, "Infinity Loyalty Error logging",
                //    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        internal static void LogInformation(string information)
        {
            string sSource = "Infinity EFTESM Base Library";
            string sLog = "Application";

            try
            {
                if (!EventLog.SourceExists(sSource))
                    EventLog.CreateEventSource(sSource, sLog);

                EventLog.WriteEntry(sSource, information, EventLogEntryType.Information);
            }
            catch
            {
                //MessageBox.Show("Could not write to error log: " + e.Message, "Infinity Loyalty Error logging",
                //    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
