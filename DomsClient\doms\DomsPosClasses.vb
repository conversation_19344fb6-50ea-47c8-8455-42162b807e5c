﻿Imports Newtonsoft.Json

Public Module DomsPosClasses

#Region "Shared Types"

  Public Class BitFlagType(Of T)
    Public value As Integer
    Public bits As T
  End Class

  Public Class EnumType(Of T)
    Public value As String
    Public [enum] As T

    Public Sub New()
    End Sub

    Public Sub New(value As String)
      Me.value = value
    End Sub

  End Class


  Public Class DomsPosBaseType
    Public name As String
    Public subCode As String

    Public Sub New()

    End Sub

    Public Sub New(Name As String, SubCode As String)
      Me.name = Name
      Me.subCode = SubCode
    End Sub
  End Class

  Public Class DomsPosResponseType
    Inherits DomsPosBaseType

    Public solicited As Boolean

  End Class

#End Region

#Region "RejectMessage"

  Public Class RejectMessageRespType
    Inherits DomsPosResponseType

    Public Class RejectCodeType
      Public unknown_MsgCode As String
      Public syntax_error As String
      Public access_error As String
    End Class


    Public Class RejectMessageRespDataType
      Public RejectedMsgCode As String
      Public RejectedExtendedMsgCode As String
      Public RejectedMsgSubc As String
      Public RejectCode As EnumType(Of RejectCodeType)
      Public RejectInfo As String
      Public RejectInfoText As String
    End Class

    Public data As RejectMessageRespDataType
  End Class

#End Region

#Region "MultiMessage"
  Public Class DomsPosResponseWithData
    Inherits DomsPosResponseType

    Public data As Object

  End Class

  Public Class MultiMessageRespType
    Inherits DomsPosResponseType

    Class MultiMessageDataType
      Public messages As List(Of DomsPosResponseWithData)
    End Class

    Public data As MultiMessageDataType

  End Class

#End Region

#Region "Heartbeat"
  Public Class HearBeatRespType
    Inherits DomsPosBaseType

    Public data As New Object()

    Public Sub New()
      MyBase.New("heartbeat", "00H")
    End Sub
  End Class
#End Region

#Region "Passthrough"

  Public Class PassthroughReqType
    Inherits DomsPosBaseType

    Public Class PassthroughReqDataType
      Public dppMsg As String
    End Class

    Public data As PassthroughReqDataType

    Public Sub New(dppMsg() As Byte)
      MyBase.New("passthrough_req", "00H")
      Me.data = New PassthroughReqDataType()
      Me.data.dppMsg = Convert.ToBase64String(dppMsg)
    End Sub
  End Class

  Public Class PassThroughRespType
    Inherits DomsPosResponseType

    Public Class PassThroughRespDataType
      Public dppMsg As String
    End Class

    Public data As PassThroughRespDataType

    Public Function ToByteArray() As Byte()
      If Me.data IsNot Nothing AndAlso Not String.IsNullOrEmpty(Me.data.dppMsg) Then
        Return Convert.FromBase64String(Me.data.dppMsg)
      Else
        Return Nothing
      End If
    End Function

  End Class

#End Region

#Region "BackOfficeRecords"

  Public Class BackOfficeRecordReqType
    Inherits DomsPosBaseType

    Public data As New Object

    Public Sub New()
      MyBase.New("BackOfficeRecord_req", "02H")
    End Sub

  End Class

  Public Class BackOfficeRecordRespType
    Inherits DomsPosResponseType

    Public Class BackOfficeRecordRespDataType

      Public Class BorFormatIdType
        Public XML_BOR_FORMAT As String
      End Class

      Public BorSeqNo As String
      Public BorFormatId As EnumType(Of BorFormatIdType)
      Public BorData As String
    End Class

    Public data As BackOfficeRecordRespDataType
  End Class

  Public Class ClearBackOfficeRecordReqType
    Inherits DomsPosBaseType

    Public Class ClearBackOfficeRecordReqDataType
      Public BorSeqNo As String
    End Class

    Public data As ClearBackOfficeRecordReqDataType

    Public Sub New(BorSeqNo As String)
      MyBase.New("clear_BackOfficeRecord_req", "00H")
      Me.data = New ClearBackOfficeRecordReqDataType()
      Me.data.BorSeqNo = BorSeqNo
    End Sub

  End Class

  Public Class ClearBackOfficeRecordRespType
    Inherits DomsPosResponseType

    Public Class ClearBackOfficeRecordRespDataType

      Public Class BorBufferStatusType
        Public BufferNotEmpty As Integer
      End Class

      Public BorBufferStatus As BitFlagType(Of BorBufferStatusType)
    End Class

    Public data As ClearBackOfficeRecordRespDataType

  End Class

#End Region

#Region "FcStatus"

  Public Class FcStatusRespType
    Inherits DomsPosResponseType

    Public Class FcStatusRespDataType
      Public Class FcStatus1FlagsType
        Public PumpTotalsReady As Integer
        Public InstallationDataReceived As Integer
        Public FallbackMode As Integer
        Public FallbackTotalsNonZero As Integer
        Public RamErrorDetectedInFc As Integer
        Public OpWithStoredTransDisabled As Integer
        Public OptSaleDisabled As Integer
        Public CurrencyCodeIsEuro As Integer
      End Class

      Public Class FcStatus2FlagsType
        Public ServiceMsgReady As Integer
        Public UnsolicitedStatusUpdateOn As Integer
        Public HwSwIncompatibilityWithinFc As Integer
        Public RtcError As Integer
        Public NoAdditionalParametersAssignedToGrades As Integer
        Public BackOfficeRecordExists As Integer
      End Class

      Public FcStatus1Flags As BitFlagType(Of FcStatus1FlagsType)
      Public FcStatus2Flags As BitFlagType(Of FcStatus2FlagsType)

      Public FcServiceMsgSeqNo As String
      Public FcMasterResetDateAndTime As String
      Public FcMasterResetCode As String
      Public FcResetDateAndTime As String
      Public FcResetCode As String
    End Class

    Public data As FcStatusRespDataType

  End Class
#End Region

#Region "FcInstallStatus"

  Public Class FcInstallStatusRespType
    Inherits DomsPosResponseType

    Public Class InstalledFcDeviceGroupsType
      Public ExtendedInstallMsgCode As String
      Public FcDeviceId As List(Of String)
    End Class

    Public Class FcInstallStatusRespDataType
      Public InstalledFcDeviceGroups As List(Of InstalledFcDeviceGroupsType)
    End Class

    Public data As FcInstallStatusRespDataType
  End Class
#End Region

#Region "FcPriceSet"
  Public Class FcPriceSetDataType
    Public UserId As String
    Public FcPriceSetId As String
    Public FcPriceGroupId As List(Of String)
    Public FcGradeId As List(Of String)
    Public FcPriceGroups As List(Of List(Of String))
    Public FcPriceSetDateAndTime As String
    Public PriceSetActivationDateAndTime As String = "00000000000000"
  End Class


  Public Class FcPriceSetReqType
    Inherits DomsPosBaseType

    Public Class FcPriceSetReqDataType
      Public PriceSetType As String
    End Class

    Public data As FcPriceSetReqDataType

    Public Sub New()
      MyBase.New("FcPriceSet_req", "03H")
      Me.data = New FcPriceSetReqDataType()
      Me.data.PriceSetType = "00H"
    End Sub

  End Class

  Public Class FcPriceSetRespType
    Inherits DomsPosResponseType

    Public data As FcPriceSetDataType
  End Class

  Public Class ChangeFcPriceSetReqType
    Inherits DomsPosBaseType

    Public data As FcPriceSetDataType

    Public Sub New(data As FcPriceSetDataType)
      MyBase.New("change_FcPriceSet_req", "03H")
      Me.data = data
    End Sub
  End Class

#End Region

#Region "FcLogon"
  Public Class FcLogonReqType
    Inherits DomsPosBaseType

    Public Class FcLogonReqDataType

      Public Class UnsolgMsgType
        Public ExtMsgCode As String
        Public MsgSubc As String

        Public Sub New(ExtMsgCode As String, MsgSubc As String)
          Me.ExtMsgCode = ExtMsgCode
          Me.MsgSubc = MsgSubc
        End Sub
      End Class

      Public Class FcLogonParsType
        Public UnsolMsgList As List(Of UnsolgMsgType)
      End Class

      Public FcAccessCode As String
      Public CountryCode As String
      Public PosVersionId As String
      Public UnsolicitedApcList() As Integer
      Public FcLogonPars As FcLogonParsType
      Public AllowFutures As Boolean = True
    End Class

    Public data As FcLogonReqDataType

    Public Sub New(FcAccessCode As String, CountryCode As String, PosVersionId As String, UnsolicitedApcList() As Integer, Optional UnSolMsgList As List(Of FcLogonReqDataType.UnsolgMsgType) = Nothing)
      MyBase.New("FcLogon_req", IIf(UnSolMsgList Is Nothing, "00H", "01H"))
      Me.data = New FcLogonReqDataType()

      Me.data.FcAccessCode = FcAccessCode
      Me.data.CountryCode = CountryCode
      Me.data.PosVersionId = PosVersionId
      Me.data.UnsolicitedApcList = UnsolicitedApcList

      If UnSolMsgList IsNot Nothing Then
        Me.data.FcLogonPars = New FcLogonReqDataType.FcLogonParsType
        Me.data.FcLogonPars.UnsolMsgList = UnSolMsgList
      End If

    End Sub
  End Class
#End Region

#Region "Fuelling point related messages"

#Region "FpStatus"

  Public Class FpStatusRespType
    Inherits DomsPosResponseType

    Public Class FpStatusRespDataType
      Public Class FpSubStateFlags
        Public IsLockedByPos As Integer
        Public IsSupervised As Integer
        Public IsOnline As Integer
        Public IsEstopped As Integer
        Public HasFreeBuffer As Integer
        Public IsInErrorState As Integer
        Public HasActiveGrades As Integer
        Public IsPreset As Integer
      End Class

      Public Class FpMainStateType
        Public Unconfigured As String
        Public Closed As String
        Public Idle As String
        Public [Error] As String
        Public Calling As String
        Public PreAuthorized As String
        Public Starting As String
        Public Starting_paused As String
        Public Starting_terminated As String
        Public Fuelling As String
        Public Fuelling_paused As String
        Public Fuelling_terminated As String
        Public Unavailable As String
        Public Unavailable_and_calling As String
      End Class

      Public Class FpSupplStatusParsType
        Public FuellingDataVol_e As String
        Public FuellingDataMon_e As String
      End Class

      Public FpId As String
      Public SmId As String
      Public FpMainState As EnumType(Of FpMainStateType)
      Public FpSubStates As BitFlagType(Of FpSubStateFlags)
      Public FpSupplStatusPars As FpSupplStatusParsType
    End Class

    Public data As FpStatusRespDataType

    Public Function MainStateText()
      Dim s As String = Nothing

      Select Case Me.data.FpMainState.value
        Case "00H" : s = "Unconfigured"
        Case "01H" : s = "Closed"
        Case "02H" : s = "Idle"
        Case "03H" : s = "Error"
        Case "04H" : s = "Calling"
        Case "05H" : s = "PreAuthorized"
        Case "06H" : s = "Starting"
        Case "07H" : s = "Starting paused"
        Case "08H" : s = "Starting terminated"
        Case "09H" : s = "Fuelling"
        Case "0AH" : s = "Fuelling Paused"
        Case "0BH" : s = "Fuelling Terminated"
        Case "0CH" : s = "Unavailable"
        Case "0DH" : s = "Unavailable and Calling"
      End Select

      Return s
    End Function
  End Class

#End Region

#Region "FpError"

  Public Class FpErrorRespType
    Inherits DomsPosResponseType

    Public Class FpErrorType
      Public value As String
    End Class

    Public Class FpErrorRespDataType
      Public FpId As String
      Public FpErrorCode As EnumType(Of FpErrorType)

      Public FpErrorDateAndTime As String
      Public PumpProtocolId As String
      Public PumpErrorCode As String
    End Class

    Public data As FpErrorRespDataType

    Public Function ErrorText() As String
      Dim s As String = Nothing

      Select Case Me.data.FpErrorCode.value
        Case "00"
          s = "No Error"
        Case "01"
          s = "Unspecified HW errror"
        Case "02"
          s = "Unspecified SW error"
        Case "03"
          s = "PROM Error"
        Case "04"
          s = "RAM Error"
        Case "06"
          s = "Pulse Error"
        Case "07"
          s = "Display Error"
        Case "08"
          s = "Output Control Error"
        Case "10"
          s = "Preset Overrun Error"
        Case "11"
          s = "Preset Grade Error"
        Case "12"
          s = "calculation Error"
        Case "13"
          s = "Blend Error"
        Case "14"
          s = "unexpected Pump Start"
        Case "15"
          s = "Transaction data error"
        Case "16"
          s = "Pump Data Sequence Error"
        Case "17"
          s = "Fp and Pump Installation Mismatch"
        Case "18"
          s = "Error Code Unavailable"
        Case "19"
          s = "MKS Pumps"
        Case "20"
          s = "Security Telegram Error"
        Case "21"
          s = "Fp Reset from POS"
        Case "22"
          s = "Pump Totals Mismatch"
        Case "23"
          s = "Grade Mismatch"
        Case "24"
          s = "Sub Pump Error"
        Case "25"
          s = "Battery Error"
        Case "26"
          s = "TDS80 Pump Only"
        Case "27"
          s = "Fuelling Data Used Error"
        Case "28"
          s = "Max Number of Consecutive Zero Transactions Reached"
        Case "29"
          s = "Illegal Price"
        Case "58"
          s = "ATC Error"
      End Select

      Return s
    End Function
  End Class


  Public Class FpErrorMsgReqType
    Inherits DomsPosBaseType

    Public Class FpErrorMsgReqDataType
      Public FpId As String
    End Class

    Public data As FpErrorMsgReqDataType

    Public Sub New(FpId As Integer)
      MyBase.New("FpErrorMsg_req", "00H")
      Me.data = New FpErrorMsgReqDataType
      Me.data.FpId = FpId.ToString()
    End Sub
  End Class

#End Region

#Region "clear_FpError"

  Public Class FpClearErrorReqType
    Inherits DomsPosBaseType

    Public Class FpClearErrorReqDataType
      Public FpId As String
      Public FpErrorCode As String
    End Class

    Public data As FpClearErrorReqDataType

    Public Sub New(FpId As Integer, FpErrorCode As String)
      MyBase.New("clear_FpError_req", "00H")
      Me.data = New FpClearErrorReqDataType
      Me.data.FpId = FpId.ToString()
      Me.data.FpErrorCode = FpErrorCode
    End Sub
  End Class

#End Region

#Region "authorize_Fp"
  Public Class FpAuthReqType
    Inherits DomsPosBaseType

    Public Class FpAuthReqDataType
      Public FpId, PosId As String
    End Class

    Public data As FpAuthReqDataType

    Public Sub New(FpId As Integer, PosId As Integer)
      MyBase.New("authorize_Fp_req", "00H")
      Me.data = New FpAuthReqDataType()
      Me.data.FpId = FpId.ToString()
      Me.data.PosId = PosId.ToString()
    End Sub
  End Class
#End Region

#Region "cancel_FpAuth"

  Public Class FpCancelAuthReqType
    Inherits DomsPosBaseType

    Public Class FpCancelAuthReqDataType
      Public FpId, PosId As String
    End Class

    Public data As FpCancelAuthReqDataType

    Public Sub New(FpId As Integer, PosId As Integer)
      MyBase.New("cancel_FpAuth_req", "00H")
      Me.data = New FpCancelAuthReqDataType()
      Me.data.FpId = FpId.ToString()
      Me.data.PosId = PosId.ToString()
    End Sub

  End Class

#End Region

#Region "FpFuellingData"

  Public Class FpFuellingDataReqType
    Inherits DomsPosBaseType

    Public Class FpFuellingDataReqDataType
      Public FpId As String
    End Class

    Public data As FpFuellingDataReqDataType

    Public Sub New(FpId As Integer)
      MyBase.New("FpFuellingData_req", "01H")
      Me.data = New FpFuellingDataReqDataType
      Me.data.FpId = FpId
    End Sub
  End Class

  Public Class FpFuellingDataRespType
    Inherits DomsPosResponseType

    Public Class FpFuellingDataDataType
      Public FpId As String
      Public Vol_e As String
      Public Money_e As String
    End Class

    Public data As FpFuellingDataDataType
  End Class

#End Region

#Region "FpSupTransBufStatus"

  Public Class FpSupTransBufStatusRespType
    Inherits DomsPosResponseType

    Public Class FpSupTransBufStatusRespDataType
      Public Class TransType
        Public Class TransInfoFlagsType
          Public StoredTrans As Integer
          Public ErrorTrans As Integer
          Public TransGreaterThanMinLimit As Integer
          Public PrepayModeUsed As Integer
          Public MoneyDueIsNegative As Integer
        End Class

        Public TransSeqNo As String
        Public SmId As String
        Public TransLockId As String
        Public TransInfoFlags As BitFlagType(Of TransInfoFlagsType)
        Public MoneyDue_e As String
        Public Vol_e As String
        Public FcGradeId As String
      End Class

      Public FpId As String
      Public TransInSupBuffer As List(Of TransType)
    End Class

    Public data As FpSupTransBufStatusRespDataType

  End Class

#End Region

#Region "FpSupTrans"

  Public Class FpSupTransReqType
    Inherits DomsPosBaseType
    Public Class TransParIdType
      Public Shared FcShiftNo As String = "30"
      Public Shared ReceiptNo As String = "31"
      Public Shared AuthId As String = "41"
      Public Shared SmId As String = "42"
      Public Shared FmId As String = "43"
      Public Shared FpId As String = "44"
      Public Shared FcPriceGroupId As String = "45"
      Public Shared FcPriceSetId As String = "46"
      Public Shared StartLimit As String = "47"
      Public Shared StartLimit_e As String = "48"
      Public Shared CurrencyCode As String = "49"
      Public Shared FcGradeId As String = "51"
      Public Shared Price As String = "52"
      Public Shared Vol As String = "53"
      Public Shared Money As String = "54"
      Public Shared SecurityTelegram As String = "55"
      Public Shared FpGradeOptionNo As String = "56"
      Public Shared FcGradeDescriptor As String = "57"
      Public Shared SecurityTelegramTypePSS As String = "58"
      Public Shared SecurityTelegramTypePSS_e As String = "59"
      Public Shared StartDate As String = "61"
      Public Shared StartTime As String = "62"
      Public Shared TransSeqNo As String = "63"
      Public Shared Price_e As String = "64"
      Public Shared Vol_e As String = "65"
      Public Shared Money_e As String = "66"
      Public Shared TransTerminationStatus As String = "71"
      Public Shared TransErrorCode As String = "72"
      Public Shared FinishDate As String = "73"
      Public Shared FinishTime As String = "74"
      Public Shared PumpPreset As String = "75"
      Public Shared PumpPreset_e As String = "76"
      Public Shared TransTankConsumptions As String = "78"
      Public Shared TransAtcInfo As String = "79"
      Public Shared FpTransReturnData As String = "80"
      Public Shared FpTransReturnData2 As String = "81"
      Public Shared AuthConfirmInfo As String = "82"
    End Class

    Public Class FpSupTransReqDataType
      Public FpId As String
      Public TransSeqNo As String
      Public PosId As String
      Public TransParId As List(Of String)

    End Class

    Public data As FpSupTransReqDataType

    Public Sub New(FpId As Integer, TransSeqNo As Integer, PosId As Integer, Optional TransParIdList As List(Of String) = Nothing)
      MyBase.New("FpSupTrans_req", "00H")
      data = New FpSupTransReqDataType()
      Me.data.FpId = FpId.ToString()
      Me.data.TransSeqNo = TransSeqNo
      Me.data.PosId = PosId.ToString()

      If TransParIdList Is Nothing Then
        Me.data.TransParId = New List(Of String)
      Else
        Me.data.TransParId = TransParIdList
      End If
    End Sub

  End Class

  Public Class FpSupTransRespType
    Inherits DomsPosResponseType
    Public Class FpSupTransRespDataType
      Public Class TransParsType
        Public Money_e As String
        Public Vol_e As String
      End Class

      Public FpId As String
      Public TransSeqNo As String
      Public TransPars As TransParsType
    End Class

    Public data As FpSupTransRespDataType
  End Class


#End Region

#Region "unlock_FpSupTrans"
  Public Class FpUnLockSupTransReqType
    Inherits DomsPosBaseType

    Public Class FpUnLockSupTransReqDataType
      Public FpId As String
      Public PosId As String
      Public TransSeqNo As String
    End Class

    Public data As FpUnLockSupTransReqDataType

    Public Sub New(FpId As Integer, TransSeqNo As Integer, PosId As Integer)
      MyBase.New("unlock_FpSupTrans_req", "00H")
      Me.data = New FpUnLockSupTransReqDataType()
      Me.data.FpId = FpId.ToString()
      Me.data.PosId = PosId.ToString()
      Me.data.TransSeqNo = TransSeqNo.ToString()
    End Sub
  End Class
#End Region

#Region "clear_FpSupTrans"
  Public Class FpClrSupTransBufReqType
    Inherits DomsPosBaseType

    Public Class FpClrSupTransBufReqDataType
      Public FpId As String
      Public PosId As String
      Public TransSeqNo As String
      Public Vol_e As String
      Public Money_e As String
      Public PaymentParameters As New Object
    End Class

    Public data As FpClrSupTransBufReqDataType

    Public Sub New(FpId As Integer, TransSeqNo As Integer, Vol_e As String, Money_e As String, PosId As String)
      MyBase.New("clear_FpSupTrans_req", "04H")
      Me.data = New FpClrSupTransBufReqDataType()
      Me.data.FpId = FpId.ToString()
      Me.data.PosId = PosId.ToString()
      Me.data.TransSeqNo = TransSeqNo.ToString()
      Me.data.Vol_e = Vol_e
      Me.data.Money_e = Money_e
    End Sub

  End Class

#End Region

#Region "FpGradeTotals"

  Public Class FpGradeTotalsReqType
    Inherits DomsPosBaseType

    Public Class FpGradeTotalsReqDataType
      Public FpId As String
    End Class

    Public data As FpGradeTotalsReqDataType

    Public Sub New(FpId As Integer)
      MyBase.New("FpGradeTotals_req", "00H")
      Me.data = New FpGradeTotalsReqDataType()
      Me.data.FpId = FpId.ToString()
    End Sub

  End Class

  Public Class FpGradeTotalsRespType
    Inherits DomsPosResponseType

    Public Class FpGradeTotalsRespDataType

      Public Class GradeTotalType
        Public FpGradeOptionNo As Integer
        Public FcGradeId As String
        Public FpGradeVolTotal As String
      End Class


      Public FpId As String
      Public FpGrandVolTotal As String
      Public FpGrandMoneyTotal As String

      Public FpGradeOptions As List(Of GradeTotalType)
    End Class

    Public data As FpGradeTotalsRespDataType
  End Class


#End Region

#Region "PumpGradeTotals"

  Public Class PumpGradeTotalsReqType
    Inherits DomsPosBaseType
    Public Class PumpGradeTotalsReqDataType
      Public FpId As String
    End Class

    Public data As PumpGradeTotalsReqDataType

    Public Sub New(FpId As Integer)
      MyBase.New("PumpGradeTotals_req", "00H")
      Me.data = New PumpGradeTotalsReqDataType()
      Me.data.FpId = FpId.ToString()
    End Sub

  End Class

  Public Class PumpGradeTotalsRespType
    Inherits DomsPosResponseType

    Public Class PumpGradeTotalsRespDataType

      Public Class PumpTotalType
        Public FpGradeOptionNo As Integer
        Public FcGradeId As String
        Public PuGradeVolTotal As String
      End Class

      Public FpId As String
      Public PuGrandVolTotal As String
      Public PuGrandMoneyTotal As String
      Public FpGradeOptions As List(Of PumpTotalType)
    End Class

    Public data As PumpGradeTotalsRespDataType
  End Class

#End Region

#End Region

#Region "Tank gauge related messages"

#Region "TgStatus"
  Public Class TgStatusRespType
    Inherits DomsPosResponseType

    Public Class TgStatusRespDataType
      Public Class TgMainStateType
        Public Unconfigured As String
        Public Operative As String
        Public Alarm As String
        Public [Error] As String
      End Class

      Public Class TgSubStateFlags
        Public TankGaugeOnline As Integer
        Public TankGaugeAlarmActive As Integer
        Public TankGaugeErrorActive As Integer
        Public TicketedDeliveryInProgress As Integer
        Public TicketedDeliveryDataReady As Integer
        Public DeliveryInProgress As Integer
        Public DeliveryDataReady As Integer
        Public AllAvailableInventoryDataReady As Integer
      End Class

      Public Class TgAlarmStatusFlags
        Public HighLevelAlarm As Integer
        Public HighHighLevelAlarm As Integer
        Public LowLevelAlarm As Integer
        Public LowLowLevelAlarm As Integer
        Public HighWaterAlarm As Integer
        Public TankLeakAlarm As Integer
        Public TankDataMissingAlarm As Integer
        Public HighHighWaterAlarm As Integer
        Public TicketedDeliveryDataLost As Integer
        Public DeliveryDataLost As Integer
        Public OtherAlarm As Integer
      End Class

      Public TgId As String
      Public TgMainState As EnumType(Of TgMainStateType)
      Public TgSubStates As BitFlagType(Of TgSubStateFlags)
      Public TgAlarmStatus As BitFlagType(Of TgAlarmStatusFlags)
    End Class

    Public data As TgStatusRespDataType

    Public Function MainStateText()
      Dim s As String = Nothing

      Select Case Me.data.TgMainState.value
        Case "00H" : s = "Unconfigured"
        Case "02H" : s = "Operative"
        Case "03H" : s = "Alarm"
        Case "04H" : s = "Error"
      End Select

      Return s
    End Function

  End Class

#End Region

#Region "TgData"

  Public Class TgDataReqType
    Inherits DomsPosBaseType

    Public Class TankDataItemIdType
      Public Shared TankProductLevel As String = "01"
      Public Shared TankWaterLevel As String = "02"
      Public Shared TankTotalObservedVol As String = "03"
      Public Shared TankWaterVol As String = "04"
      Public Shared TankGrossObservedVol As String = "05"
      Public Shared TankGrossStdVol As String = "06"
      Public Shared TankAvailableRoom As String = "07"
      Public Shared TankAverageTemp As String = "08"
      Public Shared TankDataLastUpdateDateAndTime As String = "09"
      Public Shared TankMaxSafeFillCapacity As String = "10"
      Public Shared TankShellCapacity As String = "11"
      Public Shared TankProductMass As String = "14"
      Public Shared TankProductDensity As String = "15"
      Public Shared TankProductTcDensity As String = "16"
      Public Shared TankDensityProbeTemp As String = "17"
      Public Shared TankSludgeLevel As String = "18"
      Public Shared TankOilSepOilThickness As String = "19"
      Public Shared TankOilSepOilVolume As String = "20"
      Public Shared TankTempSensor1 As String = "41"
      Public Shared TankTempSensor2 As String = "42"
      Public Shared TankTempSensor3 As String = "43"
    End Class
    Public Class TgDataReqDataType
      Public TgId As String
      Public TankDataItemId As List(Of String)
    End Class

    Public data As TgDataReqDataType

    Public Sub New(TgId As Integer, TankDataItemId As List(Of String))
      MyBase.New("TgData_req", "00H")
      Me.data = New TgDataReqDataType
      Me.data.TgId = TgId.ToString()
      Me.data.TankDataItemId = TankDataItemId
    End Sub

  End Class

  Public Class TgDataRespType
    Inherits DomsPosResponseType

    Public Class SignedTemperatureType
      Public Class SignType
        Public Positive As String
        Public Negative As String
      End Class

      Public FcSign As EnumType(Of SignType)
      Public Temperature As String
    End Class

    Public Class TankDataItemsType
      Public TankProductLevel As String
      Public TankWaterLevel As String
      Public TankWaterVol As String
      Public TankAvailableRoom As String
      Public TankGrossObservedVol As String
      Public TankMaxSafeFillCapacity As String
      Public TankDataLastUpdateDateAndTime As String
      Public TankAverageTemp As SignedTemperatureType
    End Class

    Public Class TgDataRespDataType
      Public TgId As String
      Public TgSubStates As BitFlagType(Of TgStatusRespType.TgStatusRespDataType.TgSubStateFlags)
      Public TankDataItems As TankDataItemsType
    End Class

    Public data As TgDataRespDataType
  End Class

#End Region

#Region "TankDeliveryData"
  Public Class TankDeliveryDatareqType
    Inherits DomsPosBaseType

    Public Class TankDeliveryDataItemIdType
      Public Shared TgProductCode As String = "01"
      Public Shared TankDeliverySeqNo As String = "02"
      Public Shared TankDeliveryStartDateAndTime As String = "03"
      Public Shared TankDeliveryStartProdVol As String = "04"
      Public Shared TankDeliveryStartWaterVol As String = "05"
      Public Shared TankDeliveryStartTemp As String = "06"
      Public Shared TankDeliveryStopDateAndTime As String = "07"
      Public Shared TankDeliveryStopProdVol As String = "08"
      Public Shared TankDeliveryStopWaterVol As String = "09"
      Public Shared TankDeliveryStopTemp As String = "10"
      Public Shared TankDeliveredVol As String = "11"
      Public Shared DeliveryReportSeqNo As String = "12"
      Public Shared DeliveryReturnData As String = "13"
      Public Shared TankDeliveryStartProductDensity As String = "14"
      Public Shared TankDeliveryStopProductDensity As String = "15"
      Public Shared TankDeliveryStartProductMass As String = "16"
      Public Shared TankDeliveryStopProductMass As String = "17"
      Public Shared TankDeliveryStartProdTcVol As String = "18"
      Public Shared TankDeliveryStopProdTcVol As String = "19"
      Public Shared TankDeliveredTcVol As String = "20"
      Public Shared TankDeliveryAdjustedVolume As String = "21"
      Public Shared TankDeliveryAdjustedTCVolume As String = "22"
      Public Shared TankDeliverySaleVolDuringDelivery As String = "23"
      Public Shared TankDeliveryStartProductTCDensity As String = "24"
      Public Shared TankDeliveryStopProductTCDensity As String = "25"
      Public Shared TankDeliveryTemperature As String = "26"
      Public Shared TankDeliveryAdjustedVolumeSigned As String = "27"
      Public Shared TankDeliveryAdjustedTCVolumeSigned As String = "28"
    End Class

    Public Class TankDeliveryDatareqDataType
      Public TgId As String
      Public ZERO As Byte = 0
      Public PosId As String
      Public TankDeliveryDataItemId As List(Of String)
    End Class

    Public data As TankDeliveryDatareqDataType

    Public Sub New(TgId As Integer, PosId As Integer, TankDeliveryDataItemId As List(Of String))
      MyBase.New("TankDeliveryData_req", "00H")
      Me.data = New TankDeliveryDatareqDataType
      Me.data.TgId = TgId.ToString()
      Me.data.PosId = PosId.ToString()
      Me.data.TankDeliveryDataItemId = TankDeliveryDataItemId
    End Sub
  End Class

  Public Class TankDeliveryDataRespType
    Inherits DomsPosResponseType

    Public Class TankDeliveryDataItemsType
      Public TankDeliveredVol As String
      Public TankDeliveryStartDateAndTime As String
      Public TankDeliveryStopDateAndTime As String
      Public TankDeliverySeqNo As String
      Public DeliveryReportSeqNo As String
    End Class

    Public Class TankDeliveryDataRespDataType
      Public TgId As String
      Public PosId As String
      Public DeliveryReportSeqNo As String
      Public TankDeliveryDataItems As TankDeliveryDataItemsType
    End Class

    Public data As TankDeliveryDataRespDataType
  End Class

  Public Class SiteDeliveryStatusRespType
    Inherits DomsPosResponseType

    Public Class SiteDeliveryStatusRespDataType
      Public Class DeliveryStatusFlagsType
        Public SiteDeliveryStartingMarked As Integer
        Public SiteDeliveryInProgress As Integer
        Public SiteDeliveryFinishingMarked As Integer
        Public SiteDeliveryDataIsReady As Integer
        Public SiteTicketedDeliveryDataIsReady As Integer
        Public SiteTicketedDeliveryInProgress As Integer
      End Class

      Public DeliveryStatusFlags As BitFlagType(Of DeliveryStatusFlagsType)
      Public DeliveryReportSeqNo As String
      Public TgId As List(Of String)
    End Class

    Public data As SiteDeliveryStatusRespDataType
  End Class

  Public Class ClearTankDeliveryDataReqType
    Inherits DomsPosBaseType

    Public Class TankDeliveryType
      Public TgId As String
      Public TankDeliverySeqNo As String

      Public Sub New(TgId As Integer, TankDeliverySeqNo As String)
        Me.TgId = TgId.ToString()
        Me.TankDeliverySeqNo = TankDeliverySeqNo
      End Sub
    End Class

    Public Class ClearTankDeliveryDataReqDataType
      Public PosId As String
      Public DeliveryReportSeqNo As String
      Public TankDeliveries As List(Of TankDeliveryType)
    End Class

    Public data As ClearTankDeliveryDataReqDataType

    Public Sub New(PosId As Integer, DeliveryReportSeqNo As Integer, TankDeliveries As List(Of TankDeliveryType))
      MyBase.New("clear_TankDeliveryData_req", "00H")
      Me.data = New ClearTankDeliveryDataReqDataType
      Me.data.PosId = PosId.ToString()
      Me.data.DeliveryReportSeqNo = DeliveryReportSeqNo.ToString()
      Me.data.TankDeliveries = TankDeliveries
    End Sub

  End Class

#End Region

#End Region

End Module
