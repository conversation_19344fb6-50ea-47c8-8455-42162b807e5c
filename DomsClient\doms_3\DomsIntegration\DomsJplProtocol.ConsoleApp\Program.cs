﻿using System;
using System.Threading.Tasks;
using DomsJplProtocol.Network;
using DomsJplProtocol.Services;
using DomsJplProtocol.Utilities;

namespace DomsJplProtocol.ConsoleApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("DOMS JPL Protocol Console Test Application");
            Console.WriteLine("==========================================");
            
            Console.Write("Enter DOMS controller IP address: ");
            string ipAddress = Console.ReadLine();
            
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = "127.0.0.1";
                
            Console.WriteLine($"Connecting to {ipAddress}...");
            
            try
            {
                using (var client = PssConnectionFactory.CreateUnencryptedClient(ipAddress))
                {
                    client.ConnectionStateChanged += (sender, e) => 
                        Console.WriteLine($"Connection state changed: {e.OldState} -> {e.NewState}");
                    
                    client.UnsolicitedMessageReceived += (sender, e) => 
                        Console.WriteLine($"Received unsolicited message: {e.Message.Name}");
                    
                    client.ErrorOccurred += (sender, e) => 
                        Console.WriteLine($"Error: {e.Message}");
                    
                    await client.ConnectAsync();
                    Console.WriteLine("Connected successfully!");
                    
                    Console.WriteLine("Logging on to forecourt controller...");
                    var response = await client.LogonAsync(
                        "POS,APPL_ID=CONSOLE,RI,UNSO_FPSTA_3", 
                        "0000", 
                        "CONSOLE_TEST_1.0");
                        
                    if (response != null)
                    {
                        Console.WriteLine("Logged in successfully!");
                        
                        var fcService = new ForecourtControllerService(client);
                        var status = await fcService.GetStatusAsync();
                        Console.WriteLine($"Forecourt controller status received");
                        
                        Console.WriteLine("Press Enter to get forecourt date/time...");
                        Console.ReadLine();
                        
                        var dateTime = await fcService.GetDateTimeAsync();
                        Console.WriteLine($"Forecourt controller date/time: {dateTime.Data.FcDateAndTime}");
                        
                        Console.WriteLine("Press Enter to enable status updates...");
                        Console.ReadLine();
                        
                        await fcService.EnableStatusUpdates();
                        Console.WriteLine("Status updates enabled");
                        
                        if (client.IsLoggedOn)
                        {
                            Console.WriteLine("Successfully communicating with DOMS controller");
                            Console.WriteLine("Press Enter to disconnect...");
                            Console.ReadLine();
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed to log in");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            
            Console.WriteLine("Press Enter to exit...");
            Console.ReadLine();
        }
    }
}
