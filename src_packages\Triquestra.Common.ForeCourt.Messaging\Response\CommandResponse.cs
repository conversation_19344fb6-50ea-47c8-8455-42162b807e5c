﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class CommandResponse:ForecourtControlMessage
    {
        /// <summary>
        /// The result of the command
        /// </summary>
        public ForecourtCommandResults CommandResult { get; set; }
        /// <summary>
        /// Type of the response message
        /// </summary>
        public ForecourtCommandMessageTypes MessageType { get; set; }

        public CommandResponse(int sequenceNo, int sourceId, int targetId, ForecourtCommandResults commandResult, ForecourtCommandMessageTypes msgType) :
            base(sequenceNo, sourceId, targetId, ForecourtMessageClasses.COMMAND_RESPONSE)
        {
            CommandResult = commandResult;
            MessageType = msgType;
        }

        public static CommandResponse Deserialise(XContainer messageNode, int seqNo, int sourceId, int targetId)
        {
            var respNode = messageNode.Element("CommandResp");
            if (respNode == null)
                throw new XmlSchemaException("Command Response does not have <CommandResp> node");
            if (respNode.Attribute("Result") == null)
                throw new XmlSchemaException("Command Response does not have <Result> node");
            if (respNode.Attribute("Code") == null)
                throw new XmlSchemaException("Command Response does not have <Code> node");

            ForecourtCommandResults result;

            try
            {
                result = (ForecourtCommandResults)Enum.Parse(typeof(ForecourtCommandResults), respNode.Attribute("Result").Value, true);
            }
            catch(ArgumentException)
            {
                throw new XmlSchemaException("Cannot parse Result node in Command Response.");
            }

            ForecourtCommandMessageTypes responseType;

            try
            {
                responseType = (ForecourtCommandMessageTypes)Enum.Parse(typeof(ForecourtCommandMessageTypes), respNode.Attribute("Code").Value, true);
            }
            catch (ArgumentException)
            {
                throw new XmlSchemaException("Cannot parse Code node in Command Response.");
            }

            switch (responseType)
            {
                case ForecourtCommandMessageTypes.SYSTEM_TIME:
                    return ResponseTimeCommand.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.DISPENSER_LIST:
                    return ResponseDispenserList.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.DISPENSER_CONFIG:
                    return ResponseDispenserSettings.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.CONTROLLER_CONFIG:
                    return ResponseControllerConfig.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.DELIVERY_DATA:
                    return DeliveryDataResponse.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.DISPENSER_DATA:
                    return DispenserDataResponse.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.DELIVERY_LOCK:
                    return DeliveryLockResponse.Parse(respNode, seqNo, sourceId, targetId, result);

                case ForecourtCommandMessageTypes.RESERVE:
                    return new ReserveResponse(seqNo, sourceId, targetId, result);

                default:
                    return new CommandResponse(seqNo, sourceId, targetId, result,responseType);  
            }
        }
    }
}
