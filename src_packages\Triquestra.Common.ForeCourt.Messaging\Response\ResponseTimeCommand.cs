﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Response
{
    public class ResponseTimeCommand:CommandResponse
    {
        public DateTime Timestamp { get; set; }

        public ResponseTimeCommand(int sequenceNo, int sourceId, int targetId,  ForecourtCommandResults commandResult, DateTime timestamp) : 
            base(sequenceNo, sourceId, targetId,  commandResult, ForecourtCommandMessageTypes.SYSTEM_TIME)
        {
            Timestamp = timestamp;
        }

        public static ResponseTimeCommand Parse(XElement respNode, int seqNo, int sourceId, int targetId, ForecourtCommandResults result)
        {
            var timeNode = respNode.Element("SystemTime");
            if (timeNode == null)
                throw new XmlSchemaException("Command Response does not have <SystemTime> node");
            if (timeNode.Attribute("Timestamp") == null)
                throw new XmlSchemaException("SystemTime does not have <Timestamp> node");
            DateTime timestamp;
            if (!DateTime.TryParse(timeNode.Attribute("Timestamp").Value, out timestamp))
            {
                throw new XmlSchemaException("Cannot parse Timestamp node in SystemTime.");
            }
            return new ResponseTimeCommand(seqNo, sourceId, targetId, result, timestamp);            
        }
    }
}
