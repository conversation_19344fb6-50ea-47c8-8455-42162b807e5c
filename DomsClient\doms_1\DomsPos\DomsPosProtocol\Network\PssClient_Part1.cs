﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Constants;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Models;

namespace DomsPosProtocol.Network
{
    // Main PSS Client class
    public partial class PssClient : IDisposable
    {
        private readonly PssConnectionConfig _config;
        private TcpClient _tcpClient;
        private Stream _stream;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private readonly Timer _heartbeatTimer;
        private readonly ConcurrentDictionary<string, TaskCompletionSource<JplResponse>> _pendingRequests;
        private ConnectionState _connectionState;
        private DateTime _lastMessageReceived;
        private readonly object _stateLock = new object();
        private readonly JsonSerializerOptions _jsonOptions;

        public event EventHandler<ConnectionStateChangedEventArgs> ConnectionStateChanged;
        public event EventHandler<MessageReceivedEventArgs> MessageReceived;
        public event EventHandler<MessageReceivedEventArgs> UnsolicitedMessageReceived;
        public event EventHandler<ErrorEventArgs> ErrorOccurred;

        public ConnectionState State
        {
            get
            {
                lock (_stateLock)
                {
                    return _connectionState;
                }
            }
            private set
            {
                lock (_stateLock)
                {
                    var oldState = _connectionState;
                    _connectionState = value;
                    ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs
                    {
                        OldState = oldState,
                        NewState = value
                    });
                }
            }
        }

        public bool IsConnected => State == ConnectionState.Connected || State == ConnectionState.LoggedOn;
        public bool IsLoggedOn => State == ConnectionState.LoggedOn;

        public PssClient(PssConnectionConfig config)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _cancellationTokenSource = new CancellationTokenSource();
            _pendingRequests = new ConcurrentDictionary<string, TaskCompletionSource<JplResponse>>();
            _connectionState = ConnectionState.Disconnected;
            _lastMessageReceived = DateTime.UtcNow;

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };

            _heartbeatTimer = new Timer(OnHeartbeatTimer, null, Timeout.Infinite, Timeout.Infinite);
        }
    }
}
