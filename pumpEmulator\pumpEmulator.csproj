﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5C7DEC3C-3181-4D0C-B4E9-5E12FCCD8DDE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>pumpEmulator</RootNamespace>
    <AssemblyName>pumpEmulator</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <SccProjectName>Svn</SccProjectName>
    <SccLocalPath>Svn</SccLocalPath>
    <SccAuxPath>Svn</SccAuxPath>
    <SccProvider>SubversionScc</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\debug\</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>full</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DataAccess, Version=5.0.0.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DataAccess.5.0.0.3\lib\net48\DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="GUI, Version=15.0.0.8, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\GUI.15.0.0.8\lib\net48\GUI.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.3\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.XML" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Utils, Version=5.0.0.3, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Utils.5.0.0.3\lib\net48\Utils.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\ConfigFormPropertiesResult.cs" />
    <Compile Include="Base\ConfigGetItemDataResult.cs" />
    <Compile Include="Base\DllExportAttribute.cs" />
    <Compile Include="Base\Helpers\EventLogger.cs" />
    <Compile Include="Base\Helpers\PseudoXmlHelper.cs" />
    <Compile Include="Base\Helpers\StringBuilderHelper.cs" />
    <Compile Include="Base\Main.cs" />
    <Compile Include="Base\Native.cs" />
    <Compile Include="Base\PumpActions.cs" />
    <Compile Include="Base\PumpConfigs.cs" />
    <Compile Include="Base\PumpEsmActionRequest.cs" />
    <Compile Include="Base\PumpEsmActionResult.cs" />
    <Compile Include="Base\PumpEsmBase.cs" />
    <Compile Include="Base\PumpEsmConfigRequest.cs" />
    <Compile Include="Base\PumpEsmNotifyRequest.cs" />
    <Compile Include="Base\PumpEsmRequest.cs" />
    <Compile Include="Base\PumpNotifications.cs" />
    <Compile Include="Base\WindowWrapper.cs" />
    <Compile Include="Helpers\GuiHelper.cs" />
    <Compile Include="Messaging\Types.cs" />
    <Compile Include="PumpInfoPosFooter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PumpInfoPosFooter.Designer.cs">
      <DependentUpon>PumpInfoPosFooter.cs</DependentUpon>
    </Compile>
    <Compile Include="Types\IPumpDataProvider.cs" />
    <Compile Include="Types\PetrolSaleItem.cs" />
    <Compile Include="Types\PumpDataProvider.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PumpEmulatorEsm.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\FuelDataAccess\FuelDataAccess.csproj">
      <Project>{af35b5ea-1dab-47ba-bd7f-a2d79fa4a089}</Project>
      <Name>FuelDataAccess</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="PumpInfoPosFooter.resx">
      <DependentUpon>PumpInfoPosFooter.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <Import Project="../packages/UnmanagedExports.1.2.7/tools/RGiesecke.DllExport.targets" Condition="Exists('../packages/UnmanagedExports.1.2.7/tools/RGiesecke.DllExport.targets')" />
</Project>