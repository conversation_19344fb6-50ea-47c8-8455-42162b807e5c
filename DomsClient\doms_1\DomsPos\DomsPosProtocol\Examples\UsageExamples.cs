﻿using System;
using System.Threading;
using System.Threading.Tasks;
using DomsPosProtocol.Messages.ForecourtController;
using DomsPosProtocol.Messages.Services;
using DomsPosProtocol.Network;
using DomsPosProtocol.Network.Services;

namespace DomsPosProtocol.Examples
{
    public class PssClientUsageExample
    {
        public async Task ExampleUsage()
        {
            var config = new PssConnectionConfig
            {
                HostName = "*************",
                Port = 8888,
                UseTls = false,
                ConnectionTimeoutMs = 30000,
                HeartbeatIntervalMs = 20000
            };

            using (var client = new PssClient(config))
            {
                client.ConnectionStateChanged += OnConnectionStateChanged;
                client.UnsolicitedMessageReceived += OnUnsolicitedMessage;
                client.ErrorOccurred += OnError;

                if (await client.ConnectAsync())
                {
                    Console.WriteLine("Connected successfully");

                    var loginResponse = await client.LogonAsync(
                        "POS,APPL_ID=POS1,RI,UNSO_FPSTA_3", 
                        "1234", 
                        "POS_VERSION_1.0");

                    if (loginResponse != null)
                    {
                        Console.WriteLine("Logged in successfully");

                        var fcService = new ForecourtControllerService(client);
                        var status = await fcService.GetStatusAsync();
                        Console.WriteLine("Status received");

                        Console.WriteLine("Press any key to disconnect...");
                        Console.ReadKey();
                    }
                }

                await client.DisconnectAsync();
            }
        }

        private void OnConnectionStateChanged(object sender, ConnectionStateChangedEventArgs e)
        {
            Console.WriteLine($"Connection state changed: {e.OldState} -> {e.NewState}");
        }

        private void OnUnsolicitedMessage(object sender, MessageReceivedEventArgs e)
        {
            Console.WriteLine($"Unsolicited message received: {e.Message.Name}");
        }

        private void OnError(object sender, ErrorEventArgs e)
        {
            Console.WriteLine($"Error occurred: {e.Message}");
            if (e.Exception != null)
            {
                Console.WriteLine($"Exception: {e.Exception}");
            }
        }
    }
}
