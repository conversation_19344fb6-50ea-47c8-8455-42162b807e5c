﻿using System;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class ForecourtEventMessage:ForecourtControlMessage
    {
        public EventMessageTypes EventType { get; set; }

        public ForecourtEventMessage(int sequenceNo, int sourceId, int targetId, EventMessageTypes eventType) : 
            base(sequenceNo, sourceId, targetId, ForecourtMessageClasses.EVENT)
        {
            EventType = eventType;
        }

        public static ForecourtEventMessage Deserialise(XContainer messageNode, int seqNo, int sourceId, int targetId)
        {

            var eventNode = messageNode.Element("Event");
            if (eventNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <Event> node");
            }
            var et =
                (EventMessageTypes)Enum.Parse(typeof(EventMessageTypes), eventNode.Attribute("Code").Value);
            switch (et)
            {
                case EventMessageTypes.HEARTBEAT:
                    return new EventHeartbeatMessage(seqNo, sourceId, targetId);

                case EventMessageTypes.NOZZLE_STATE_CHANGE:
                    return EventNozzleStateChangeMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DELIVERY_STATE_CHANGE:
                    return EventDeliveryStateChangeMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DISPENSER_STATE_CHANGE:
                    return EventDispenserStateChangeMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DISPENSER_MODE_CHANGE:
                    return EventDispenserModeChangedMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DELIVERY_PROGRESS:
                    return EventDeliveryProgressMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DELIVERY_LOCKED:
                    return EventDeliveryLockedMessage.Parse(eventNode, seqNo, sourceId, targetId);

                case EventMessageTypes.DELIVERY_UNLOCKED:
                    return EventDeliveryUnlockedMessage.Parse(eventNode, seqNo, sourceId, targetId);
                case EventMessageTypes.DELIVERY_COMPLETE:
                    return EventDeliveryCompletedMessage.Parse(eventNode, seqNo, sourceId, targetId);
                case EventMessageTypes.DELIVERY_STARTED:
                    return EventDeliveryStartedMessage.Parse(eventNode, seqNo, sourceId, targetId);
                case EventMessageTypes.DELIVERY_CLEAR:
                    return EventDeliveryClearedMessage.Parse(eventNode, seqNo, sourceId, targetId);
                case EventMessageTypes.DELIVERY_DELETED:
                    return EventDeliveryDeletedMessage.Parse(eventNode, seqNo, sourceId, targetId);
                case EventMessageTypes.ELECTRONIC_TOTALS:
                    return EventPumpMeterMessage.Parse(eventNode, seqNo, sourceId, targetId);
            }
            return new ForecourtEventMessage(seqNo, sourceId, targetId, et);
        }
    }
}
