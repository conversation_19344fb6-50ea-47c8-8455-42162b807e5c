﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class frmMain
  Inherits System.Windows.Forms.Form

  'Form overrides dispose to clean up the component list.
  <System.Diagnostics.DebuggerNonUserCode()>
  Protected Overrides Sub Dispose(ByVal disposing As Boolean)
    Try
      If disposing AndAlso components IsNot Nothing Then
        components.Dispose()
      End If
    Finally
      MyBase.Dispose(disposing)
    End Try
  End Sub

  'Required by the Windows Form Designer
  Private components As System.ComponentModel.IContainer

  'NOTE: The following procedure is required by the Windows Form Designer
  'It can be modified using the Windows Form Designer.  
  'Do not modify it using the code editor.
  <System.Diagnostics.DebuggerStepThrough()>
  Private Sub InitializeComponent()
    Me.components = New System.ComponentModel.Container()
    Dim Fp As System.Windows.Forms.ColumnHeader
    Dim Grade As System.Windows.Forms.ColumnHeader
    Dim Vol As System.Windows.Forms.ColumnHeader
    Dim UnitPrice As System.Windows.Forms.ColumnHeader
    Dim Amount As System.Windows.Forms.ColumnHeader
    Dim TreeNode1 As System.Windows.Forms.TreeNode = New System.Windows.Forms.TreeNode("Fuelling Points", 1, 1)
    Dim TreeNode2 As System.Windows.Forms.TreeNode = New System.Windows.Forms.TreeNode("Tank Gauges", 2, 2)
    Dim TreeNode3 As System.Windows.Forms.TreeNode = New System.Windows.Forms.TreeNode("Forecourt", New System.Windows.Forms.TreeNode() {TreeNode1, TreeNode2})
    Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmMain))
    Me.btnFCLogon = New System.Windows.Forms.Button()
    Me.btnchangePrices = New System.Windows.Forms.Button()
    Me.btnLockTrans = New System.Windows.Forms.Button()
    Me.btnUnlock = New System.Windows.Forms.Button()
    Me.btnSaleOK = New System.Windows.Forms.Button()
    Me.btnReadTgData = New System.Windows.Forms.Button()
    Me.btnAuthorize = New System.Windows.Forms.Button()
    Me.btnDeauthorize = New System.Windows.Forms.Button()
    Me.btnOptions = New System.Windows.Forms.Button()
    Me.btnClearError = New System.Windows.Forms.Button()
    Me.SplitContainer1 = New System.Windows.Forms.SplitContainer()
    Me.tvForecourt = New System.Windows.Forms.TreeView()
    Me.ImageList1 = New System.Windows.Forms.ImageList(Me.components)
    Me.lvReceipt = New System.Windows.Forms.ListView()
    Me.btnReadTotals = New System.Windows.Forms.Button()
    Me.btnReadDeliveries = New System.Windows.Forms.Button()
    Me.btnViewJsonMessageDump = New System.Windows.Forms.Button()
    Me.btnOpenConsole = New System.Windows.Forms.Button()
    Me.FuellingDataTimer = New System.Windows.Forms.Timer(Me.components)
    Fp = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
    Grade = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
    Vol = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
    UnitPrice = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
    Amount = CType(New System.Windows.Forms.ColumnHeader(), System.Windows.Forms.ColumnHeader)
    CType(Me.SplitContainer1, System.ComponentModel.ISupportInitialize).BeginInit()
    Me.SplitContainer1.Panel1.SuspendLayout()
    Me.SplitContainer1.Panel2.SuspendLayout()
    Me.SplitContainer1.SuspendLayout()
    Me.SuspendLayout()
    '
    'Fp
    '
    Fp.Text = "Fp"
    Fp.Width = 46
    '
    'Grade
    '
    Grade.Text = "Grade"
    Grade.Width = 70
    '
    'Vol
    '
    Vol.Text = "Volume"
    Vol.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Vol.Width = 80
    '
    'UnitPrice
    '
    UnitPrice.Text = "Unit Price"
    UnitPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    UnitPrice.Width = 80
    '
    'Amount
    '
    Amount.Text = "Amount"
    Amount.TextAlign = System.Windows.Forms.HorizontalAlignment.Right
    Amount.Width = 80
    '
    'btnFCLogon
    '
    Me.btnFCLogon.Enabled = False
    Me.btnFCLogon.Location = New System.Drawing.Point(12, 12)
    Me.btnFCLogon.Name = "btnFCLogon"
    Me.btnFCLogon.Size = New System.Drawing.Size(75, 23)
    Me.btnFCLogon.TabIndex = 0
    Me.btnFCLogon.Text = "FcLogon"
    Me.btnFCLogon.UseVisualStyleBackColor = True
    '
    'btnchangePrices
    '
    Me.btnchangePrices.Enabled = False
    Me.btnchangePrices.Location = New System.Drawing.Point(338, 12)
    Me.btnchangePrices.Name = "btnchangePrices"
    Me.btnchangePrices.Size = New System.Drawing.Size(140, 23)
    Me.btnchangePrices.TabIndex = 8
    Me.btnchangePrices.Text = "Change Prices"
    Me.btnchangePrices.UseVisualStyleBackColor = True
    '
    'btnLockTrans
    '
    Me.btnLockTrans.Enabled = False
    Me.btnLockTrans.Location = New System.Drawing.Point(174, 12)
    Me.btnLockTrans.Name = "btnLockTrans"
    Me.btnLockTrans.Size = New System.Drawing.Size(75, 23)
    Me.btnLockTrans.TabIndex = 4
    Me.btnLockTrans.Text = "Lock"
    Me.btnLockTrans.UseVisualStyleBackColor = True
    '
    'btnUnlock
    '
    Me.btnUnlock.Enabled = False
    Me.btnUnlock.Location = New System.Drawing.Point(174, 41)
    Me.btnUnlock.Name = "btnUnlock"
    Me.btnUnlock.Size = New System.Drawing.Size(75, 23)
    Me.btnUnlock.TabIndex = 5
    Me.btnUnlock.Text = "Unlock"
    Me.btnUnlock.UseVisualStyleBackColor = True
    '
    'btnSaleOK
    '
    Me.btnSaleOK.Enabled = False
    Me.btnSaleOK.Location = New System.Drawing.Point(257, 12)
    Me.btnSaleOK.Name = "btnSaleOK"
    Me.btnSaleOK.Size = New System.Drawing.Size(75, 23)
    Me.btnSaleOK.TabIndex = 6
    Me.btnSaleOK.Text = "Sale OK"
    Me.btnSaleOK.UseVisualStyleBackColor = True
    '
    'btnReadTgData
    '
    Me.btnReadTgData.Enabled = False
    Me.btnReadTgData.Location = New System.Drawing.Point(484, 12)
    Me.btnReadTgData.Name = "btnReadTgData"
    Me.btnReadTgData.Size = New System.Drawing.Size(124, 23)
    Me.btnReadTgData.TabIndex = 9
    Me.btnReadTgData.Text = "Read ATG Data"
    Me.btnReadTgData.UseVisualStyleBackColor = True
    '
    'btnAuthorize
    '
    Me.btnAuthorize.Enabled = False
    Me.btnAuthorize.Location = New System.Drawing.Point(93, 12)
    Me.btnAuthorize.Name = "btnAuthorize"
    Me.btnAuthorize.Size = New System.Drawing.Size(75, 23)
    Me.btnAuthorize.TabIndex = 2
    Me.btnAuthorize.Text = "Authorize"
    Me.btnAuthorize.UseVisualStyleBackColor = True
    '
    'btnDeauthorize
    '
    Me.btnDeauthorize.Enabled = False
    Me.btnDeauthorize.Location = New System.Drawing.Point(93, 41)
    Me.btnDeauthorize.Name = "btnDeauthorize"
    Me.btnDeauthorize.Size = New System.Drawing.Size(75, 23)
    Me.btnDeauthorize.TabIndex = 3
    Me.btnDeauthorize.Text = "Deauthorize"
    Me.btnDeauthorize.UseVisualStyleBackColor = True
    '
    'btnOptions
    '
    Me.btnOptions.Location = New System.Drawing.Point(614, 12)
    Me.btnOptions.Name = "btnOptions"
    Me.btnOptions.Size = New System.Drawing.Size(75, 23)
    Me.btnOptions.TabIndex = 11
    Me.btnOptions.Text = "Options"
    Me.btnOptions.UseVisualStyleBackColor = True
    '
    'btnClearError
    '
    Me.btnClearError.Enabled = False
    Me.btnClearError.Location = New System.Drawing.Point(12, 41)
    Me.btnClearError.Name = "btnClearError"
    Me.btnClearError.Size = New System.Drawing.Size(75, 23)
    Me.btnClearError.TabIndex = 1
    Me.btnClearError.Text = "Clear Error"
    Me.btnClearError.UseVisualStyleBackColor = True
    '
    'SplitContainer1
    '
    Me.SplitContainer1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
    Me.SplitContainer1.Location = New System.Drawing.Point(12, 70)
    Me.SplitContainer1.Name = "SplitContainer1"
    '
    'SplitContainer1.Panel1
    '
    Me.SplitContainer1.Panel1.Controls.Add(Me.tvForecourt)
    '
    'SplitContainer1.Panel2
    '
    Me.SplitContainer1.Panel2.Controls.Add(Me.lvReceipt)
    Me.SplitContainer1.Size = New System.Drawing.Size(823, 512)
    Me.SplitContainer1.SplitterDistance = 390
    Me.SplitContainer1.TabIndex = 17
    '
    'tvForecourt
    '
    Me.tvForecourt.Dock = System.Windows.Forms.DockStyle.Fill
    Me.tvForecourt.HideSelection = False
    Me.tvForecourt.ImageIndex = 0
    Me.tvForecourt.ImageList = Me.ImageList1
    Me.tvForecourt.Location = New System.Drawing.Point(0, 0)
    Me.tvForecourt.Name = "tvForecourt"
    TreeNode1.ImageIndex = 1
    TreeNode1.Name = "Fps"
    TreeNode1.SelectedImageIndex = 1
    TreeNode1.Tag = ""
    TreeNode1.Text = "Fuelling Points"
    TreeNode2.ImageIndex = 2
    TreeNode2.Name = "Tgs"
    TreeNode2.SelectedImageIndex = 2
    TreeNode2.Text = "Tank Gauges"
    TreeNode3.Name = "FC"
    TreeNode3.Text = "Forecourt"
    Me.tvForecourt.Nodes.AddRange(New System.Windows.Forms.TreeNode() {TreeNode3})
    Me.tvForecourt.SelectedImageIndex = 0
    Me.tvForecourt.Size = New System.Drawing.Size(390, 512)
    Me.tvForecourt.TabIndex = 0
    '
    'ImageList1
    '
    Me.ImageList1.ImageStream = CType(resources.GetObject("ImageList1.ImageStream"), System.Windows.Forms.ImageListStreamer)
    Me.ImageList1.TransparentColor = System.Drawing.Color.Transparent
    Me.ImageList1.Images.SetKeyName(0, "fc.ico")
    Me.ImageList1.Images.SetKeyName(1, "gaspump.ico")
    Me.ImageList1.Images.SetKeyName(2, "tg.ico")
    Me.ImageList1.Images.SetKeyName(3, "online.ico")
    Me.ImageList1.Images.SetKeyName(4, "offline.ico")
    Me.ImageList1.Images.SetKeyName(5, "coins.bmp")
    Me.ImageList1.Images.SetKeyName(6, "tgdata.ico")
    '
    'lvReceipt
    '
    Me.lvReceipt.Columns.AddRange(New System.Windows.Forms.ColumnHeader() {Fp, Grade, Vol, UnitPrice, Amount})
    Me.lvReceipt.Dock = System.Windows.Forms.DockStyle.Fill
    Me.lvReceipt.GridLines = True
    Me.lvReceipt.Location = New System.Drawing.Point(0, 0)
    Me.lvReceipt.Name = "lvReceipt"
    Me.lvReceipt.Size = New System.Drawing.Size(429, 512)
    Me.lvReceipt.TabIndex = 0
    Me.lvReceipt.UseCompatibleStateImageBehavior = False
    Me.lvReceipt.View = System.Windows.Forms.View.Details
    '
    'btnReadTotals
    '
    Me.btnReadTotals.Enabled = False
    Me.btnReadTotals.Location = New System.Drawing.Point(257, 41)
    Me.btnReadTotals.Name = "btnReadTotals"
    Me.btnReadTotals.Size = New System.Drawing.Size(75, 23)
    Me.btnReadTotals.TabIndex = 7
    Me.btnReadTotals.Text = "&Read Totals"
    Me.btnReadTotals.UseVisualStyleBackColor = True
    '
    'btnReadDeliveries
    '
    Me.btnReadDeliveries.Enabled = False
    Me.btnReadDeliveries.Location = New System.Drawing.Point(484, 41)
    Me.btnReadDeliveries.Name = "btnReadDeliveries"
    Me.btnReadDeliveries.Size = New System.Drawing.Size(124, 23)
    Me.btnReadDeliveries.TabIndex = 10
    Me.btnReadDeliveries.Text = "Read ATG Deliveries"
    Me.btnReadDeliveries.UseVisualStyleBackColor = True
    '
    'btnViewJsonMessageDump
    '
    Me.btnViewJsonMessageDump.Location = New System.Drawing.Point(695, 12)
    Me.btnViewJsonMessageDump.Name = "btnViewJsonMessageDump"
    Me.btnViewJsonMessageDump.Size = New System.Drawing.Size(142, 23)
    Me.btnViewJsonMessageDump.TabIndex = 12
    Me.btnViewJsonMessageDump.Text = "View JPL Messages"
    Me.btnViewJsonMessageDump.UseVisualStyleBackColor = True
    '
    'btnOpenConsole
    '
    Me.btnOpenConsole.Location = New System.Drawing.Point(695, 41)
    Me.btnOpenConsole.Name = "btnOpenConsole"
    Me.btnOpenConsole.Size = New System.Drawing.Size(142, 23)
    Me.btnOpenConsole.TabIndex = 13
    Me.btnOpenConsole.Text = "Open JPL Console"
    Me.btnOpenConsole.UseVisualStyleBackColor = True
    '
    'FuellingDataTimer
    '
    Me.FuellingDataTimer.Enabled = True
    Me.FuellingDataTimer.Interval = 1000
    '
    'frmMain
    '
    Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
    Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
    Me.ClientSize = New System.Drawing.Size(847, 594)
    Me.Controls.Add(Me.btnOpenConsole)
    Me.Controls.Add(Me.btnViewJsonMessageDump)
    Me.Controls.Add(Me.btnReadDeliveries)
    Me.Controls.Add(Me.btnReadTotals)
    Me.Controls.Add(Me.SplitContainer1)
    Me.Controls.Add(Me.btnClearError)
    Me.Controls.Add(Me.btnOptions)
    Me.Controls.Add(Me.btnDeauthorize)
    Me.Controls.Add(Me.btnAuthorize)
    Me.Controls.Add(Me.btnReadTgData)
    Me.Controls.Add(Me.btnSaleOK)
    Me.Controls.Add(Me.btnUnlock)
    Me.Controls.Add(Me.btnLockTrans)
    Me.Controls.Add(Me.btnchangePrices)
    Me.Controls.Add(Me.btnFCLogon)
    Me.MinimumSize = New System.Drawing.Size(863, 330)
    Me.Name = "frmMain"
    Me.Text = "JPL Demo POS"
    Me.SplitContainer1.Panel1.ResumeLayout(False)
    Me.SplitContainer1.Panel2.ResumeLayout(False)
    CType(Me.SplitContainer1, System.ComponentModel.ISupportInitialize).EndInit()
    Me.SplitContainer1.ResumeLayout(False)
    Me.ResumeLayout(False)

  End Sub
  Friend WithEvents btnFCLogon As System.Windows.Forms.Button
  Friend WithEvents btnchangePrices As System.Windows.Forms.Button
  Friend WithEvents btnLockTrans As System.Windows.Forms.Button
  Friend WithEvents btnUnlock As System.Windows.Forms.Button
  Friend WithEvents btnSaleOK As System.Windows.Forms.Button
  Friend WithEvents btnReadTgData As System.Windows.Forms.Button
  Friend WithEvents btnAuthorize As System.Windows.Forms.Button
  Friend WithEvents btnDeauthorize As System.Windows.Forms.Button
  Friend WithEvents btnOptions As System.Windows.Forms.Button
  Friend WithEvents btnClearError As System.Windows.Forms.Button
  Friend WithEvents SplitContainer1 As System.Windows.Forms.SplitContainer
  Friend WithEvents tvForecourt As System.Windows.Forms.TreeView
  Friend WithEvents lvReceipt As System.Windows.Forms.ListView
  Friend WithEvents btnReadTotals As System.Windows.Forms.Button
  Friend WithEvents btnReadDeliveries As System.Windows.Forms.Button
  Friend WithEvents ImageList1 As System.Windows.Forms.ImageList
  Friend WithEvents btnViewJsonMessageDump As Button
  Friend WithEvents btnOpenConsole As Button
  Friend WithEvents FuellingDataTimer As Timer
End Class
