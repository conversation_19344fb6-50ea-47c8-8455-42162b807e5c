{"SlnFileName": "PumpRegulus.sln", "Configuration": "Release", "Verbosity": "Normal", "Major": "56", "Minor": "", "Patch": "0", "Build": "0", "BuildOffset": "", "PackageSuffix": "", "DevReleaseSvnUrl": "/tstNon-standard Infinity Releases/Pump Regulus/", "ReleaseSvnUrl": "/QA Non-standard Infinity Releases/Pump Regulus/", "ReleaseSvnName": "Pump Regulus", "OnTimeSolutionName": "Pump Regulus", "OnTimeReleasePath": "29936", "SetupProjects": [], "ReleaseFiles": [{"FilePath": "\\pumpRegulus\\bin\\Release\\*.dll", "TargetPath": "\\Infinity\\Components\\pumpRegulus"}, {"FilePath": "\\EsmBridge.RunOnce.bat", "TargetPath": "\\Infinity\\Components\\pumpRegulus"}], "PackFiles": [{"MainDll": "Regulus.Engine.dll", "MainPath": "\\Regulus.Engine\\bin\\Release\\", "TargetPath": "\\Infinity\\Components\\pumpRegulus", "ExcludeList": "Regulus.Interface.dll"}]}