using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
//using DomsJplProtocol.Core;
//using DomsJplProtocol.Messages.DispenseControl;
//using DomsJplProtocol.Messages.General;
//using DomsJplProtocol.Services;
//using DomsJplProtocol.Utilities;
using Triquestra.Common.PumpEsm.Messaging;
using Triquestra.Common.PumpEsm.Messaging.Event;
using Triquestra.Common.PumpEsm.Messaging.Request;
using Triquestra.Common.PumpEsm.Messaging.Response;

namespace Triquestra.Common.PumpEsm.RegulusInterface
{
    public class JplPumpController : IPumpController
    {
        private readonly NLog.Logger _log = NLog.LogManager.GetCurrentClassLogger();
        private readonly PssClient _client;
        private readonly DispenseControlService _dispenseControlService;
        private readonly ForecourtControllerService _forecourtControllerService;
        private readonly int _workstationId;
        private readonly List<IDispenserSettings> _dispensers;
        private bool _isConnected;
        private int _sequenceNumber = 1;

        public JplPumpController(PssClient client, int workstationId)
        {
            _client = client ?? throw new ArgumentNullException(nameof(client));
            _dispenseControlService = new DispenseControlService(_client);
            _forecourtControllerService = new ForecourtControllerService(_client);
            _workstationId = workstationId;
            _dispensers = new List<IDispenserSettings>();
            
            // Subscribe to client events
            _client.ConnectionStateChanged += OnClientConnectionStateChanged;
            _client.UnsolicitedMessageReceived += OnClientUnsolicitedMessageReceived;
            
            // Register message types
            MessageRegistry.RegisterAllMessageTypes();
        }

        // IPumpController implementation
        public IEnumerable<IDispenserSettings> Dispensers => _dispensers;
        public int WorkstationId => _workstationId;
        public string Version { get; private set; } = "1.0.0";
        public bool IsConnected => _isConnected;

        // Connection methods
        public void Connect()
        {
            _log.Info("Connecting to JPL service...");
            Task.Run(async () => await _client.ConnectAsync()).Wait();
            _isConnected = true;
            OnConnected(EventArgs.Empty);
            
            // Request initial dispenser list
            GetDispenserList();
        }

        public void Disconnect()
        {
            _log.Info("Disconnecting from JPL service...");
            Task.Run(async () => await _client.DisconnectAsync()).Wait();
            _isConnected = false;
            OnDisconnected(EventArgs.Empty);
        }

        // Event handlers for client events
        private void OnClientConnectionStateChanged(object sender, ConnectionStateChangedEventArgs e)
        {
            _log.Info($"JPL connection state changed: {e.NewState}");
            _isConnected = e.NewState == ConnectionState.Connected;
            if (_isConnected)
                OnConnected(EventArgs.Empty);
            else
                OnDisconnected(EventArgs.Empty);
        }

        private void OnClientUnsolicitedMessageReceived(object sender, UnsolicitedMessageEventArgs e)
        {
            _log.Debug($"Received unsolicited message: {e.Message.Name}");
            
            // Map JPL messages to appropriate events
            switch (e.Message.Name)
            {
                case "Heartbeat":
                    OnHeartbeat(EventArgs.Empty);
                    break;
                    
                case "FpStatusUpdate":
                    if (e.Message is FpStatusUpdateMessage fpStatusUpdate)
                    {
                        HandleFpStatusUpdate(fpStatusUpdate);
                    }
                    break;
                    
                case "FpDeliveryStarted":
                    if (e.Message is FpDeliveryStartedMessage deliveryStarted)
                    {
                        HandleDeliveryStarted(deliveryStarted);
                    }
                    break;
                    
                case "FpDeliveryProgress":
                    if (e.Message is FpDeliveryProgressMessage deliveryProgress)
                    {
                        HandleDeliveryProgress(deliveryProgress);
                    }
                    break;
                    
                case "FpDeliveryComplete":
                    if (e.Message is FpDeliveryCompleteMessage deliveryComplete)
                    {
                        HandleDeliveryComplete(deliveryComplete);
                    }
                    break;
                    
                case "FpNozzleStateChange":
                    if (e.Message is FpNozzleStateChangeMessage nozzleStateChange)
                    {
                        HandleNozzleStateChange(nozzleStateChange);
                    }
                    break;
                    
                // Add more message handlers as needed
            }
        }

        #region Message Handlers

        private void HandleFpStatusUpdate(FpStatusUpdateMessage message)
        {
            // Create dispenser state change event
            var dispenserId = int.Parse(message.FpId);
            var state = MapJplStateToDispenserState(message.State);
            var authMode = MapJplModeToAuthMode(message.Mode);
            
            var stateChangeMsg = new EventDispenserStateChangeMessage(
                GetNextSequenceNumber(),
                dispenserId,
                _workstationId,
                state,
                authMode);
                
            OnDispenserStateChanged(stateChangeMsg);
        }

        private void HandleDeliveryStarted(FpDeliveryStartedMessage message)
        {
            var dispenserId = int.Parse(message.FpId);
            var delivery = CreateDeliveryFromJplMessage(message);
            
            var deliveryStartedMsg = new EventDeliveryStartedMessage(
                GetNextSequenceNumber(),
                dispenserId,
                _workstationId,
                delivery);
                
            OnDeliveryStarted(deliveryStartedMsg);
        }

        private void HandleDeliveryProgress(FpDeliveryProgressMessage message)
        {
            var dispenserId = int.Parse(message.FpId);
            var delivery = CreateDeliveryFromJplMessage(message);
            
            var deliveryProgressMsg = new EventDeliveryProgressMessage(
                GetNextSequenceNumber(),
                dispenserId,
                _workstationId,
                delivery);
                
            OnDeliveryInProgress(deliveryProgressMsg);
        }

        private void HandleDeliveryComplete(FpDeliveryCompleteMessage message)
        {
            var dispenserId = int.Parse(message.FpId);
            var delivery = CreateDeliveryFromJplMessage(message);
            
            var deliveryCompletedMsg = new EventDeliveryCompletedMessage(
                GetNextSequenceNumber(),
                dispenserId,
                _workstationId,
                delivery);
                
            OnDeliveryCompleted(deliveryCompletedMsg);
        }

        private void HandleNozzleStateChange(FpNozzleStateChangeMessage message)
        {
            var dispenserId = int.Parse(message.FpId);
            var nozzleId = int.Parse(message.NozzleId);
            var state = MapJplNozzleStateToNozzleState(message.State);
            
            var nozzleStateChangeMsg = new EventNozzleStateChangeMessage(
                GetNextSequenceNumber(),
                dispenserId,
                _workstationId,
                nozzleId,
                state);
                
            OnNozzleStateChanged(nozzleStateChangeMsg);
        }

        #endregion

        #region Mapping Methods

        private DispenserStates MapJplStateToDispenserState(string jplState)
        {
            switch (jplState.ToUpper())
            {
                case "IDLE": return DispenserStates.IDLE;
                case "CALLING": return DispenserStates.CALLING;
                case "AUTHORIZED": return DispenserStates.AUTHORIZED;
                case "FUELLING": return DispenserStates.DELIVERING;
                case "FINISHED": return DispenserStates.FINALIZED;
                case "ERROR": return DispenserStates.SYSTEM_ERROR;
                case "SUSPENDED": return DispenserStates.SUSPENDED;
                case "OFFLINE": return DispenserStates.NOT_RESPONDING;
                default: return DispenserStates.IDLE;
            }
        }

        private AuthModes MapJplModeToAuthMode(string jplMode)
        {
            switch (jplMode?.ToUpper())
            {
                case "PREPAY": return AuthModes.PREPAY;
                case "PREAUTH": return AuthModes.PREAUTH;
                case "POSTPAY": return AuthModes.POSTPAY;
                default: return AuthModes.None;
            }
        }

        private NozzleStates MapJplNozzleStateToNozzleState(string jplState)
        {
            switch (jplState.ToUpper())
            {
                case "IN": return NozzleStates.IN;
                case "OUT": return NozzleStates.OUT;
                case "FUELLING": return NozzleStates.DELIVERING;
                case "IDLE": return NozzleStates.NOT_DELIVERING;
                default: return NozzleStates.NOT_AUTHORIZED;
            }
        }

        private IDelivery CreateDeliveryFromJplMessage(JplMessage message)
        {
            // Create a delivery object from JPL message
            // This is a simplified implementation - you'll need to adapt based on your actual message structure
            var delivery = new Delivery
            {
                DispenserId = int.Parse(message.GetProperty<string>("FpId")),
                DeliveryId = int.Parse(message.GetProperty<string>("DeliveryId", "0")),
                State = DeliveryStates.DELIVERING
            };
            
            // Set other properties based on message type
            if (message is FpDeliveryCompleteMessage complete)
            {
                delivery.State = DeliveryStates.COMPLETED;
                delivery.Volume = decimal.Parse(complete.Volume);
                delivery.Amount = decimal.Parse(complete.Amount);
                delivery.UnitPrice = decimal.Parse(complete.UnitPrice);
            }
            else if (message is FpDeliveryProgressMessage progress)
            {
                delivery.Volume = decimal.Parse(progress.Volume);
                delivery.Amount = decimal.Parse(progress.Amount);
                delivery.UnitPrice = decimal.Parse(progress.UnitPrice);
            }
            
            return delivery;
        }

        #endregion

        #region IPumpController Methods

        public void Authorize(int targetId)
        {
            _log.Info($"Authorizing dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpAuthorizeRequest>();
                    request.FpId = targetId.ToString();
                    var response = await _dispenseControlService.AuthorizeAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully authorized dispenser {targetId}");
                    }
                    else
                    {
                        _log.Warn($"Failed to authorize dispenser {targetId}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error authorizing dispenser {targetId}");
                }
            });
        }

        public void Authorize(int targetId, decimal limit, IForecourtBlend blend)
        {
            _log.Info($"Authorizing dispenser {targetId} with limit {limit}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpAuthorizeRequest>();
                    request.FpId = targetId.ToString();
                    request.LimitType = "Value";
                    request.LimitAmount = limit.ToString();
                    request.Grade = blend?.Id.ToString() ?? "0";
                    
                    var response = await _dispenseControlService.AuthorizeAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully authorized dispenser {targetId} with limit {limit}");
                    }
                    else
                    {
                        _log.Warn($"Failed to authorize dispenser {targetId} with limit {limit}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error authorizing dispenser {targetId} with limit {limit}");
                }
            });
        }

        public void AbortDelivery(int targetId)
        {
            _log.Info($"Aborting delivery on dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpAbortRequest>();
                    request.FpId = targetId.ToString();
                    
                    var response = await _dispenseControlService.AbortAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully aborted delivery on dispenser {targetId}");
                    }
                    else
                    {
                        _log.Warn($"Failed to abort delivery on dispenser {targetId}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error aborting delivery on dispenser {targetId}");
                }
            });
        }

        public void ClearDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Clearing delivery {deliveryId} on dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpClearRequest>();
                    request.FpId = targetId.ToString();
                    request.DeliveryId = deliveryId.ToString();
                    
                    var response = await _dispenseControlService.ClearAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully cleared delivery {deliveryId} on dispenser {targetId}");
                        
                        // Create and raise event
                        var clearResponse = new ClearDeliveryResponse(
                            GetNextSequenceNumber(),
                            targetId,
                            _workstationId,
                            ForecourtCommandResults.SUCCESS,
                            deliveryId);
                            
                        OnDeliveryCleared(clearResponse);
                    }
                    else
                    {
                        _log.Warn($"Failed to clear delivery {deliveryId} on dispenser {targetId}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error clearing delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void GetDispenserData(int targetId)
        {
            _log.Info($"Getting dispenser data for dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpStatusRequest>();
                    request.FpId = targetId.ToString();
                    
                    var response = await _dispenseControlService.GetStatusAsync(request);
                    
                    // Create dispenser data response
                    var dispenserData = new DispenserData
                    {
                        DispenserId = targetId,
                        State = MapJplStateToDispenserState(response.State),
                        AuthMode = MapJplModeToAuthMode(response.Mode)
                    };
                    
                    var dataResponse = new DispenserDataResponse(
                        GetNextSequenceNumber(),
                        targetId,
                        _workstationId,
                        ForecourtCommandResults.SUCCESS,
                        dispenserData);
                        
                    OnDispenserDataReceived(dataResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error getting dispenser data for dispenser {targetId}");
                }
            });
        }

        public void GetDispenserList()
        {
            _log.Info("Getting dispenser list");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpListRequest>();
                    var response = await _dispenseControlService.GetFpListAsync(request);
                    
                    // Create dispenser list
                    var dispensers = new List<IDispenserSettings>();
                    foreach (var fpInfo in response.FpList)
                    {
                        var dispenser = new DispenserSettings
                        {
                            DispenserId = int.Parse(fpInfo.FpId),
                            DispenserName = fpInfo.Name,
                            IsEnabled = fpInfo.Status == "Enabled"
                        };
                        dispensers.Add(dispenser);
                    }
                    
                    // Update local dispenser list
                    _dispensers.Clear();
                    _dispensers.AddRange(dispensers);
                    
                    // Create response
                    var listResponse = new DispenserListResponse(
                        GetNextSequenceNumber(),
                        0,
                        _workstationId,
                        ForecourtCommandResults.SUCCESS,
                        dispensers);
                        
                    OnDispenserListReceived(listResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, "Error getting dispenser list");
                }
            });
        }

        public void GetDeliveryData(int targetId, int deliveryId)
        {
            _log.Info($"Getting delivery data for delivery {deliveryId} on dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpDeliveryDataRequest>();
                    request.FpId = targetId.ToString();
                    request.DeliveryId = deliveryId.ToString();
                    
                    var response = await _dispenseControlService.GetDeliveryDataAsync(request);
                    
                    // Create delivery
                    var delivery = new Delivery
                    {
                        DeliveryId = deliveryId,
                        DispenserId = targetId,
                        Volume = decimal.Parse(response.Volume),
                        Amount = decimal.Parse(response.Amount),
                        UnitPrice = decimal.Parse(response.UnitPrice),
                        State = DeliveryStates.COMPLETED
                    };
                    
                    // Create response
                    var dataResponse = new DeliveryDataResponse(
                        GetNextSequenceNumber(),
                        targetId,
                        _workstationId,
                        ForecourtCommandResults.SUCCESS,
                        delivery);
                        
                    OnDeliveryDataReceived(dataResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error getting delivery data for delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void LockDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Locking delivery {deliveryId} on dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpLockDeliveryRequest>();
                    request.FpId = targetId.ToString();
                    request.DeliveryId = deliveryId.ToString();
                    
                    var response = await _dispenseControlService.LockDeliveryAsync(request);
                    
                    // Create response
                    var lockResult = response.Result == "Success" 
                        ? ForecourtCommandResults.SUCCESS 
                        : ForecourtCommandResults.Failure;
                        
                    var lockResponse = new DeliveryLockResponse(
                        GetNextSequenceNumber(),
                        targetId,
                        _workstationId,
                        lockResult,
                        deliveryId);
                        
                    OnDeliveryLockReceived(lockResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error locking delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void UnlockDelivery(int targetId, int deliveryId)
        {
            _log.Info($"Unlocking delivery {deliveryId} on dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpUnlockDeliveryRequest>();
                    request.FpId = targetId.ToString();
                    request.DeliveryId = deliveryId.ToString();
                    
                    var response = await _dispenseControlService.UnlockDeliveryAsync(request);
                    
                    if (response.Result == "Success")
                    {
                        // Create event message
                        var unlockMessage = new EventDeliveryUnlockedMessage(
                            GetNextSequenceNumber(),
                            targetId,
                            _workstationId,
                            deliveryId);
                            
                        OnDeliveryUnlocked(unlockMessage);
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error unlocking delivery {deliveryId} on dispenser {targetId}");
                }
            });
        }

        public void ReserveDispenser(int targetId)
        {
            _log.Info($"Reserving dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpReserveRequest>();
                    request.FpId = targetId.ToString();
                    request.WorkstationId = _workstationId.ToString();
                    
                    var response = await _dispenseControlService.ReserveAsync(request);
                    
                    // Create response
                    var reserveResult = response.Result == "Success" 
                        ? ForecourtCommandResults.SUCCESS 
                        : ForecourtCommandResults.Failure;
                        
                    var reserveResponse = new ReserveDispenserResponse(
                        GetNextSequenceNumber(),
                        targetId,
                        _workstationId,
                        reserveResult);
                        
                    OnDispenserReserved(reserveResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error reserving dispenser {targetId}");
                }
            });
        }

        public void ReleaseDispenser(int targetId)
        {
            _log.Info($"Releasing dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpReleaseRequest>();
                    request.FpId = targetId.ToString();
                    request.WorkstationId = _workstationId.ToString();
                    
                    var response = await _dispenseControlService.ReleaseAsync(request);
                    
                    // Create response
                    var releaseResult = response.Result == "Success" 
                        ? ForecourtCommandResults.SUCCESS 
                        : ForecourtCommandResults.Failure;
                        
                    var releaseResponse = new ReleaseDispenserResponse(
                        GetNextSequenceNumber(),
                        targetId,
                        _workstationId,
                        releaseResult);
                        
                    OnDispenserReleased(releaseResponse);
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error releasing dispenser {targetId}");
                }
            });
        }

        public void SetDispenserPreset(int targetId, decimal presetAmount, PresetTypes presetType, IForecourtBlend blend = null)
        {
            _log.Info($"Setting preset for dispenser {targetId}: {presetAmount} ({presetType})");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpPresetRequest>();
                    request.FpId = targetId.ToString();
                    request.Amount = presetAmount.ToString();
                    request.PresetType = MapPresetTypeToJpl(presetType);
                    if (blend != null)
                        request.Grade = blend.Id.ToString();
                    
                    var response = await _dispenseControlService.SetPresetAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully set preset for dispenser {targetId}");
                    }
                    else
                    {
                        _log.Warn($"Failed to set preset for dispenser {targetId}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error setting preset for dispenser {targetId}");
                }
            });
        }

        public void StopDispenser(int targetId)
        {
            _log.Info($"Stopping dispenser {targetId}");
            Task.Run(async () => 
            {
                try
                {
                    var request = JplMessageFactory.CreateRequest<FpStopRequest>();
                    request.FpId = targetId.ToString();
                    
                    var response = await _dispenseControlService.StopAsync(request);
                    
                    // Handle response
                    if (response.Result == "Success")
                    {
                        _log.Info($"Successfully stopped dispenser {targetId}");
                    }
                    else
                    {
                        _log.Warn($"Failed to stop dispenser {targetId}: {response.Result}");
                    }
                }
                catch (Exception ex)
                {
                    _log.Error(ex, $"Error stopping dispenser {targetId}");
                }
            });
        }

        public void Dispose()
        {
            _log.Info("Disposing JplPumpController");
            
            // Unsubscribe from client events to prevent memory leaks
            if (_client != null)
            {
                _client.ConnectionStateChanged -= OnClientConnectionStateChanged;
                _client.UnsolicitedMessageReceived -= OnClientUnsolicitedMessageReceived;
                
                // Disconnect if connected
                if (_isConnected)
                {
                    Disconnect();
                }
            }
        }

        #region Helper Methods

        private string MapPresetTypeToJpl(PresetTypes presetType)
        {
            switch (presetType)
            {
                case PresetTypes.Volume:
                    return "Volume";
                case PresetTypes.Amount:
                    return "Value";
                default:
                    return "Value";
            }
        }

        private int GetNextSequenceNumber()
        {
            return _sequenceNumber++;
        }

        #endregion

        #region Event Declarations

        public event EventHandler<EventArgs> Connected;
        public event EventHandler<EventArgs> Disconnected;
        public event EventHandler<EventArgs> Heartbeat;
        public event EventHandler<EventMessageArgs> DispenserStateChanged;
        public event EventHandler<EventMessageArgs> DispenserStatusChanged;
        public event EventHandler<EventMessageArgs> NozzleStateChanged;
        public event EventHandler<EventMessageArgs> DeliveryStarted;
        public event EventHandler<EventMessageArgs> DeliveryInProgress;
        public event EventHandler<EventMessageArgs> DeliveryCompleted;
        public event EventHandler<EventMessageArgs> DeliveryUnlocked;
        public event EventHandler<ResponseMessageArgs> DeliveryCleared;
        public event EventHandler<ResponseMessageArgs> DispenserReleased;
        public event EventHandler<ResponseMessageArgs> DispenserReserved;
        public event EventHandler<ResponseMessageArgs> DispenserDataReceived;
        public event EventHandler<ResponseMessageArgs> DispenserListReceived;
        public event EventHandler<ResponseMessageArgs> DeliveryDataReceived;
        public event EventHandler<ResponseMessageArgs> DeliveryLockReceived;

        #endregion

        #region Event Invocation Methods

        protected virtual void OnConnected(EventArgs e)
        {
            Connected?.Invoke(this, e);
        }

        protected virtual void OnDisconnected(EventArgs e)
        {
            Disconnected?.Invoke(this, e);
        }

        protected virtual void OnHeartbeat(EventArgs e)
        {
            Heartbeat?.Invoke(this, e);
        }

        protected virtual void OnDispenserStateChanged(EventMessageArgs e)
        {
            DispenserStateChanged?.Invoke(this, e);
        }

        protected virtual void OnDispenserStateChanged(EventDispenserStateChangeMessage message)
        {
            OnDispenserStateChanged(new EventMessageArgs(message));
        }

        protected virtual void OnDispenserStatusChanged(EventMessageArgs e)
        {
            DispenserStatusChanged?.Invoke(this, e);
        }

        protected virtual void OnNozzleStateChanged(EventMessageArgs e)
        {
            NozzleStateChanged?.Invoke(this, e);
        }

        protected virtual void OnNozzleStateChanged(EventNozzleStateChangeMessage message)
        {
            OnNozzleStateChanged(new EventMessageArgs(message));
        }

        protected virtual void OnDeliveryStarted(EventMessageArgs e)
        {
            DeliveryStarted?.Invoke(this, e);
        }

        protected virtual void OnDeliveryStarted(EventDeliveryStartedMessage message)
        {
            OnDeliveryStarted(new EventMessageArgs(message));
        }

        protected virtual void OnDeliveryInProgress(EventMessageArgs e)
        {
            DeliveryInProgress?.Invoke(this, e);
        }

        protected virtual void OnDeliveryInProgress(EventDeliveryProgressMessage message)
        {
            OnDeliveryInProgress(new EventMessageArgs(message));
        }

        protected virtual void OnDeliveryCompleted(EventMessageArgs e)
        {
            DeliveryCompleted?.Invoke(this, e);
        }

        protected virtual void OnDeliveryCompleted(EventDeliveryCompletedMessage message)
        {
            OnDeliveryCompleted(new EventMessageArgs(message));
        }

        protected virtual void OnDeliveryUnlocked(EventMessageArgs e)
        {
            DeliveryUnlocked?.Invoke(this, e);
        }

        protected virtual void OnDeliveryUnlocked(EventDeliveryUnlockedMessage message)
        {
            OnDeliveryUnlocked(new EventMessageArgs(message));
        }

        protected virtual void OnDeliveryCleared(ResponseMessageArgs e)
        {
            DeliveryCleared?.Invoke(this, e);
        }

        protected virtual void OnDeliveryCleared(ClearDeliveryResponse response)
        {
            OnDeliveryCleared(new ResponseMessageArgs(response));
        }

        protected virtual void OnDispenserReleased(ResponseMessageArgs e)
        {
            DispenserReleased?.Invoke(this, e);
        }

        protected virtual void OnDispenserReleased(ReleaseDispenserResponse response)
        {
            OnDispenserReleased(new ResponseMessageArgs(response));
        }

        protected virtual void OnDispenserReserved(ResponseMessageArgs e)
        {
            DispenserReserved?.Invoke(this, e);
        }

        protected virtual void OnDispenserReserved(ReserveDispenserResponse response)
        {
            OnDispenserReserved(new ResponseMessageArgs(response));
        }

        protected virtual void OnDispenserDataReceived(ResponseMessageArgs e)
        {
            DispenserDataReceived?.Invoke(this, e);
        }

        protected virtual void OnDispenserDataReceived(DispenserDataResponse response)
        {
            OnDispenserDataReceived(new ResponseMessageArgs(response));
        }

        protected virtual void OnDispenserListReceived(ResponseMessageArgs e)
        {
            DispenserListReceived?.Invoke(this, e);
        }

        protected virtual void OnDispenserListReceived(DispenserListResponse response)
        {
            OnDispenserListReceived(new ResponseMessageArgs(response));
        }

        protected virtual void OnDeliveryDataReceived(ResponseMessageArgs e)
        {
            DeliveryDataReceived?.Invoke(this, e);
        }

        protected virtual void OnDeliveryDataReceived(DeliveryDataResponse response)
        {
            OnDeliveryDataReceived(new ResponseMessageArgs(response));
        }

        protected virtual void OnDeliveryLockReceived(ResponseMessageArgs e)
        {
            DeliveryLockReceived?.Invoke(this, e);
        }

        protected virtual void OnDeliveryLockReceived(DeliveryLockResponse response)
        {
            OnDeliveryLockReceived(new ResponseMessageArgs(response));
        }

        #endregion
    }
}