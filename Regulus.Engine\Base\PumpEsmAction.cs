﻿using pumpRegulus.Interface;
using System;
using System.IO;
using System.Text;
using Triquestra.Common.PumpEsm.Base.Helpers;

namespace Triquestra.Common.PumpEsm.Base
{
    public class PumpEsmAction : IPumpRegulus
    {
        private const int PARAMETER_BUFFER_LENGTH = 1024 * 3 + 128;
        static NLog.Logger _logger = NLog.LogManager.GetCurrentClassLogger();
        PumpEsmBase _pumpEsm = null;
        
        public PumpEsmAction()
        {
            _logger.Info("Regulus.Engine has been loaded.");
        }

        public void Bootstrap()
        {
            _logger.Trace("Entering PumpEsmBase.Bootstrap");
            // do this after runonce, to avoid loading assemblies that runonce wants to delete.
            FileInfo assemblyFolder = new FileInfo(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);

            string filename = Path.Combine(assemblyFolder.DirectoryName, "posrun.ini");
            _logger.Debug("Reading " + filename);
            Main.Station = Native.GetPrivateProfileInt("StartUp", "StationID", -1, filename);
            _logger.Debug($"Station number: { Main.Station }");

            CreatePumpEsmInstance();
            //PumpEsmLookup(assemblyFolder);
            _logger.Trace("Leaving PumpEsmBase.Bootstrap");
        }

        public void ProcessRequest(string request, out bool res, out string reply)
        {
            _logger.Trace("Entering PumpEsmBase.ProcessRequest");
            res = false;
            reply = string.Empty;
#if DEBUG
            _logger.Info("Request: \"{0}\"", request);
#else
            _logger.Debug("Request: \"{0}\"", request);
#endif
            StringBuilder sXmlFromPos = new StringBuilder(request);

            //#region B9245: xml special characters from pos are not encoded
            //// Convert given string to appropriate XML
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "storename");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "dirhost");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "account");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "account2");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address1");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address2");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "address3");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "city");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "state");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "postcode");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "phone1");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "taxnumber");
            //sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "companynumber");
            //#endregion
            sXmlFromPos = BodgeyFixTagContent(sXmlFromPos, "connstr");
            //Add opening and closing tags
            sXmlFromPos.Insert(0, "<pumpesmrequest>");
            sXmlFromPos.Append("</pumpesmrequest>");

            var dstXml = sXmlFromPos.ToString();
            string lActionNode = PseudoXmlHelper.GetXMLField(dstXml, "action");

            if (null != lActionNode)
            {
                try
                {
                    DoESMAction(dstXml, out res, out reply);

                    if (lActionNode == "deinit")
                    {
                        // finalisation
                        _pumpEsm = null;
                        lActionNode = null;
                        reply = string.Empty;
                    }

                    if (reply.Length > PARAMETER_BUFFER_LENGTH)
                    {
                        throw new ArgumentOutOfRangeException(string.Format(@"The ESM response length {0} is out of range. The response is '{1}'", reply.Length, reply));
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error("Error processing request. Exception: {0}", ex.ToString());
                    throw;
                }
            }
            else
            {
                reply = string.Empty;
            }

            _logger.Debug("PumpESM Response: \"{0}\"", reply);
            _logger.Trace("Leaving PumpEsmBase.ProcessRequest");
        }

#if false
        // left here for a reference purpose
        private void PumpEsmLookup(FileInfo assemblyFolder)
        {
            DirectoryInfo di = assemblyFolder.Directory;

            FileInfo[] files = di.GetFiles("pump*.dll", SearchOption.AllDirectories); // Changed to search directories.  CR12597

            string esmFileName = null;
            HashSet<string> listToSkip = new HashSet<string>(DLLNAMESTOSKIP, StringComparer.OrdinalIgnoreCase);

            //grab the first file, which is not in DLLNAMESTOSKIP   
            foreach (FileInfo file in files)
            {
                if (!listToSkip.Contains(file.Name))
                {
                    esmFileName = file.FullName;
                    break;
                }
            }

            if (!string.IsNullOrEmpty(esmFileName))
            {
                Assembly esmAssembly = Assembly.LoadFrom(esmFileName);

                // find an PumpEsmBase and save it
                CreatePumpEsmInstance(esmAssembly);
            }
            else
            {
                _logger.Debug("No pumpesm services found!");
            }
        }

        private void CreatePumpEsmInstance(Assembly esmAssembly)
        {
            Type[] types = esmAssembly.GetTypes();
            foreach (Type t in types)
            {
                if (t.IsClass && t.IsSubclassOf(typeof(PumpEsmBase)) && !t.IsAbstract)
                {
                    PumpEsm = esmAssembly.CreateInstance(t.FullName) as PumpEsmBase;
                    _logger.Debug(t.FullName + " found and " + ((null == PumpEsm) ? "NOT" : string.Empty) + " loaded");
                    break;
                }
            }
        }
#else
        private void CreatePumpEsmInstance()
        {
            _pumpEsm = new PumpRegulusEsm();
        }
#endif
        /// <summary>
        /// Execute PumpEsm action
        /// </summary>
        /// <param name="request">XML string</param>
        /// <returns>XML string</returns>
        public void DoESMAction(string request, out bool res, out string retval)
        {
            retval = string.Empty;
            res = false;
            try
            {
                if (_pumpEsm != null)
                {
                    _logger.Trace("DoESMAction: {0}", request);
                    PumpEsmActionResult result = _pumpEsm.PerformESMAction(request);
                    if (result != null)
                    {
                        res = result.Result;
                        retval = result.ResultText;
                    }

                }
                else
                {
                    _logger.Error(@"Attempting to call pumpesm assembly, but it hasn't been loaded");
                }

            }
            catch (Exception ex)
            {
                retval = string.Empty;
                res = false;
                _logger.Error(ex, ex.Message);
            }
            _logger.Trace("Aquired reply: {0}-{1}", res, retval);
        }

        private StringBuilder BodgeyFixTagContent(StringBuilder aString, string TagName)
        {
            // B9245: "fix" the content of a given tag, using string replace.
            // Core doesn't xml-encode entities in the xml it passes (in fact, it decodes
            // entities! If you put &amp; in your store name, you'll get & passed in the xml).
            // Because it doesn't encode entities, you could end up with something like:
            // <storename>Super <3 Heart</storename>
            // which we cannot parse as XML, because of the '<'
            // So we fix it using string search/replace.
            // This function finds the first instance of the given tag, and "fixes" it.
            // It does nothing if the tag is not found.
            int i1;
            int i2;
            string openTag, closeTag;
            StringBuilder Result = aString;

            openTag = "<" + TagName + ">";
            closeTag = "</" + TagName + ">";
            i1 = Helpers.StringBuilderHelper.IndexOf(aString, openTag);
            //i1 = aString.IndexOf(openTag);

            if (i1 < 0) return Result;

            i1 += openTag.Length;
            //  i2 = aString.IndexOf(closeTag,i1);
            i2 = Helpers.StringBuilderHelper.IndexOf(aString, closeTag, i1);


            if (i2 < 0) return Result; //however, it seems to be incorrect...

            int oldLen = i2 - i1;
            StringBuilder content = new StringBuilder(aString.ToString(i1, i2 - i1));

            content = content.Replace("&", "&amp;").Replace("<", "&lt;")
                .Replace(">", "&gt;").Replace("'", "&apos;")
                .Replace("\"", "&quot;");

            if (content.Length != oldLen)
            {
                Result.Remove(i1, oldLen);
                Result.Insert(i1, content);
            }
            return Result;
        }
    }
}
