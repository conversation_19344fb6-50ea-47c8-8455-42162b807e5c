﻿using pumpRegulus.Interface;
using RGiesecke.DllExport;
using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;

namespace pumpRegulus
{
    public static class Main
    {
        private const string PUMP_REGULUS_ENGINE = "Regulus.Engine.dll";
        private static IPumpRegulus _pumpRegulus;
        private static bool _init = false;

        [DllExport("InfDllActionStd", CallingConvention.StdCall)]
        public unsafe static bool InfDllActionStdCall(char* buffer)
        {
            try
            {
                return InfDllActionInternal(buffer, IntPtr.Zero, IntPtr.Zero);
            }
            catch
            {
                return false;
            }
        }

        private unsafe static bool InfDllActionInternal(char* buffer, IntPtr conn, IntPtr app)
        {
            LoadAssembly();

            if (_pumpRegulus == null)
            {
                return false;
            }

            if (!_init)
            {
                _pumpRegulus.Bootstrap();
                _init = true;
            }

            bool res = false;
            string reply = string.Empty;
            string request = new string(buffer);

            try
            {
                _pumpRegulus.ProcessRequest(request, out res, out reply);
            }
            catch
            {
                // todo: is this an appropriate return string? perhaps should be <reply action=\"ignore\"></reply>
                reply = string.Empty;
                res = false;
            }

            Marshal.Copy(reply.ToCharArray(), 0, (IntPtr)buffer, reply.Length);
            buffer[reply.Length] = '\0';
            return res;
        }

        private static void LoadAssembly()
        {
            if (_pumpRegulus == null)
            {
                var basePath = AppDomain.CurrentDomain.BaseDirectory;
                var dllFilePath = Path.Combine(basePath, $"pumpRegulus\\{ PUMP_REGULUS_ENGINE }");

                if (File.Exists(dllFilePath))
                {
                    var assembly = Assembly.LoadFrom(dllFilePath);

                    var dllType = assembly.GetTypes().FirstOrDefault(
                        t => (t.GetInterfaces().Contains(typeof(IPumpRegulus)) || t.IsSubclassOf(typeof(IPumpRegulus)))
                        && !t.IsAbstract
                        && t.IsClass
                        && t.IsPublic
                        );

                    if (dllType != null)
                    {
                        _pumpRegulus = (IPumpRegulus)assembly.CreateInstance(dllType.FullName, false, BindingFlags.CreateInstance, null, null, null, null);
                    }
                }
            }
        }
    }
}
