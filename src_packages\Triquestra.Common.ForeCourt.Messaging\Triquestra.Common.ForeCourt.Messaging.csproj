﻿<Project Sdk="Microsoft.NET.Sdk">
 <PropertyGroup>
    <TargetFrameworks>net461;net48</TargetFrameworks>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Authors>Triquestra</Authors>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <title>Triquestra.Common.PumpEsm.Messaging</title>
    <description>Triquestra.Common.PumpEsm.Messaging</description>
    <RootNamespace>Triquestra.Common.PumpEsm.Messaging</RootNamespace>
    <AssemblyName>Triquestra.Common.ForeCourt.Messaging</AssemblyName>
   <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
  </PropertyGroup>

   <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
    <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>  
  
  <ItemGroup>
    <Compile Include="DispenserSettings.cs" />
    <Compile Include="Event\EventDeliveryClearedMessage.cs" />
    <Compile Include="Event\EventDeliveryCompletedMessage.cs" />
    <Compile Include="Event\EventDeliveryDeletedMessage.cs" />
    <Compile Include="Event\EventDeliveryLockedMessage.cs" />
    <Compile Include="Event\EventDeliveryProgressMessage.cs" />
    <Compile Include="Event\EventDeliveryStartedMessage.cs" />
    <Compile Include="Event\EventDeliveryStateChangeMessage.cs" />
    <Compile Include="Event\EventDeliveryUnlockedMessage.cs" />
    <Compile Include="Event\EventDispenserModeChangedMessage.cs" />
    <Compile Include="Event\EventDispenserStateChangeMessage.cs" />
    <Compile Include="Event\EventPumpMeterMessage.cs" />
    <Compile Include="Event\ForecourtEventMessage.cs" />
    <Compile Include="Event\EventHeartbeatMessage.cs" />
    <Compile Include="Event\EventNozzleStateChangeMessage.cs" />
    <Compile Include="ForecourtBlend.cs" />
    <Compile Include="ForecourtMessage.cs" />
    <Compile Include="IAuthModeProfile.cs" />
    <Compile Include="IDelivery.cs" />
    <Compile Include="IDispenserData.cs" />
    <Compile Include="IDispenserSettings.cs" />
    <Compile Include="IForecourtBlend.cs" />
    <Compile Include="IForecourtMessage.cs" />
    <Compile Include="INozzle.cs" />
    <Compile Include="IPrepay.cs" />
    <Compile Include="PriceChangeRequestModel.cs" />
    <Compile Include="Request\AuthorizeRequest.cs" />
    <Compile Include="Request\ClearDeliveryRequest.cs" />
    <Compile Include="Request\DeliveryDataRequest.cs" />
    <Compile Include="Request\ForecourtCommandRequest.cs" />
    <Compile Include="ForecourtControlMessage.cs" />
    <Compile Include="IForecourtControlMessage.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Request\ForecourtConfigRequest.cs" />
    <Compile Include="Request\ForecourtPriceChangeRequest.cs" />
    <Compile Include="Request\ForecourtSetProfileRequest.cs" />
    <Compile Include="Request\LockDeliveryRequest.cs" />
    <Compile Include="Request\ReserveRequest.cs" />
    <Compile Include="Request\UnlockDeliveryRequest.cs" />
    <Compile Include="Response\CommandResponse.cs" />
    <Compile Include="Response\ConfigResponse.cs" />
    <Compile Include="Response\DeliveryDataResponse.cs" />
    <Compile Include="Response\DeliveryLockResponse.cs" />
    <Compile Include="Response\DispenserDataResponse.cs" />
    <Compile Include="Response\ReserveResponse.cs" />
    <Compile Include="Response\ResponseControllerConfig.cs" />
    <Compile Include="Response\ResponseDispenserList.cs" />
    <Compile Include="Response\ResponseDispenserSettings.cs" />
    <Compile Include="Response\ResponseProfilesConfig.cs" />
    <Compile Include="Response\ResponseTimeCommand.cs" />
    <Compile Include="SystemMessages\ForecourtSystemRequest.cs" />
    <Compile Include="SystemMessages\ForecourtSystemResponse.cs" />
    <Compile Include="SystemMessages\ForecourtSystemStatusResponse.cs" />
  </ItemGroup>
</Project>
