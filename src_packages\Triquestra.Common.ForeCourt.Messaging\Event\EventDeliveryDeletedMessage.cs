﻿using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{
    public class EventDeliveryDeletedMessage : ForecourtEventMessage
    {
        public int DeliveryId { get; set; }
        public int DispenserId { get { return SourceId; } set { SourceId = value; } }
        public string ExceptionText { get; set; }

        public EventDeliveryDeletedMessage(int sequenceNo, int sourceId, int targetId, int deliveryId, string exception)
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_DELETED)
        {
            DeliveryId = deliveryId;
            ExceptionText = exception;
        }

        public static EventDeliveryDeletedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var delNode = eventNode.Element("DeliveryDeleted");

            if (delNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryDeleted> node");
            }

            var deliveryId = int.Parse(delNode.Attribute("DeliveryID").Value);
            var ex = delNode.Attribute("Exception").Value;

            return new EventDeliveryDeletedMessage(seqNo, sourceId, targetId, deliveryId, ex);
        }
    }
}
