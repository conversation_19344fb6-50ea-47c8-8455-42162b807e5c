﻿using System;

namespace DomsPosProtocol.Constants
{
    public static class MessageNames
    {
        // General FC Messages
        public const string FC_LOGON_REQ = "FcLogon_req";
        public const string FC_LOGON_RESP = "FcLogon_resp";
        public const string FC_STATUS_REQ = "FcStatus_req";
        public const string FC_STATUS_RESP = "FcStatus_resp";
        public const string FC_DATE_TIME_REQ = "FcDateAndTime_req";
        public const string FC_DATE_TIME_RESP = "FcDateAndTime_resp";
        public const string CHANGE_FC_DATE_TIME_REQ = "change_FcDateAndTime_req";
        public const string CHANGE_FC_DATE_TIME_RESP = "change_FcDateAndTime_resp";
        
        // Status Messages
        public const string FC_INSTALL_STATUS_REQ = "FcInstallStatus_req";
        public const string FC_INSTALL_STATUS_RESP = "FcInstallStatus_resp";
        public const string FC_PRICE_SET_STATUS_REQ = "FcPriceSetStatus_req";
        public const string FC_PRICE_SET_STATUS_RESP = "FcPriceSetStatus_resp";
        
        // Service Messages
        public const string FC_SERVICE_MSG_REQ = "FcServiceMsg_req";
        public const string FC_SERVICE_MSG_RESP = "FcServiceMsg_resp";
        public const string CLEAR_FC_SERVICE_MSG_REQ = "clear_FcServiceMsg_req";
        public const string CLEAR_FC_SERVICE_MSG_RESP = "clear_FcServiceMsg_resp";
        
        // Back Office Records
        public const string BACK_OFFICE_RECORD_REQ = "BackOfficeRecord_req";
        public const string BACK_OFFICE_RECORD_RESP = "BackOfficeRecord_resp";
        public const string STORE_BACK_OFFICE_RECORD_REQ = "store_BackOfficeRecord_req";
        public const string STORE_BACK_OFFICE_RECORD_RESP = "store_BackOfficeRecord_resp";
        public const string CLEAR_BACK_OFFICE_RECORD_REQ = "clear_BackOfficeRecord_req";
        public const string CLEAR_BACK_OFFICE_RECORD_RESP = "clear_BackOfficeRecord_resp";
        
        // Installation Data
        public const string CLEAR_INSTALL_DATA_REQ = "clear_InstallData_req";
        public const string CLEAR_INSTALL_DATA_RESP = "clear_InstallData_resp";
        
        // Client Data
        public const string CLIENT_DATA_REQ = "ClientData_req";
        public const string CLIENT_DATA_RESP = "ClientData_resp";
        public const string STORE_CLIENT_DATA_REQ = "store_ClientData_req";
        public const string STORE_CLIENT_DATA_RESP = "store_ClientData_resp";
        
        // Connection Status
        public const string POS_CONNECTION_STATUS_REQ = "PosConnectionStatus_req";
        public const string POS_CONNECTION_STATUS_RESP = "PosConnectionStatus_resp";
        public const string PSS_PERIPHERALS_STATUS_REQ = "PssPeripheralsStatus_req";
        public const string PSS_PERIPHERALS_STATUS_RESP = "PssPeripheralsStatus_resp";
        
        // Utility
        public const string CHANGE_FC_STATUS_UPDATE_MODE_REQ = "change_FcStatusUpdateMode_req";
        public const string CHANGE_FC_STATUS_UPDATE_MODE_RESP = "change_FcStatusUpdateMode_resp";
        public const string ECHO_COMMAND_REQ = "echo_req";
        public const string ECHO_COMMAND_RESP = "echo_resp";
        public const string HEARTBEAT = "heartbeat";
        public const string MULTI_MESSAGE_RESP = "MultiMessage_resp";
        public const string REJECT_MESSAGE_RESP = "RejectMessage_resp";
        public const string JPL = "jpl";
    }

    public static class SubCodes
    {
        public const string SUBC_00H = "00H";
        public const string SUBC_01H = "01H";
        public const string SUBC_02H = "02H";
        public const string SUBC_03H = "03H";
    }

    public static class TcpPorts
    {
        public const int UNENCRYPTED_PORT = 8888;
        public const int ENCRYPTED_TLS_PORT = 8889;
    }

    public static class ProtocolDelimiters
    {
        public const byte STX = 2;
        public const byte ETX = 3;
    }
}
