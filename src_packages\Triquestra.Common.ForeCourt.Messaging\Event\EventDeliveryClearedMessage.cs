﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.Schema;

namespace Triquestra.Common.PumpEsm.Messaging.Event
{    
    public class EventDeliveryClearedMessage : ForecourtEventMessage
    {
        public int DeliveryId { get; set; }
        public string ExceptionText { get; set; }
        public int DispenserId { get { return SourceId; } set { SourceId = value; } }

        public EventDeliveryClearedMessage(int sequenceNo, int sourceId, int targetId, int deliveryId, string ex)
            : base(sequenceNo, sourceId, targetId, EventMessageTypes.DELIVERY_CLEAR)
        {
            DeliveryId = deliveryId;
            ExceptionText = ex;
        }

        public static EventDeliveryClearedMessage Parse(XElement eventNode, int seqNo, int sourceId, int targetId)
        {
            var delNode = eventNode.Element("DeliveryClear");

            if (delNode == null)
            {
                throw new XmlSchemaException("eventNode does not have <DeliveryClear> node");
            }

            var deliveryId = int.Parse(delNode.Attribute("DeliveryID").Value);
            var ex = delNode.Attribute("Exception").Value;

            return new EventDeliveryClearedMessage(seqNo, sourceId, targetId, deliveryId,  ex);
        }
    }
}
