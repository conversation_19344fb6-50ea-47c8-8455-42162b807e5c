﻿using System;
using System.Security.Cryptography.X509Certificates;
using DomsJplProtocol.Core;

namespace DomsJplProtocol.Network
{
    public class PssConnectionFactory
    {
        public static PssClient CreateClient(PssConnectionConfig config)
        {
            return new PssClient(config);
        }

        public static PssClient CreateUnencryptedClient(string hostname, int port = JplConstants.NetworkConstants.UNENCRYPTED_PORT)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = false
            };
            return new PssClient(config);
        }

        public static PssClient CreateEncryptedClient(string hostname, int port = JplConstants.NetworkConstants.ENCRYPTED_TLS_PORT, 
            X509Certificate2 clientCertificate = null)
        {
            var config = new PssConnectionConfig
            {
                HostName = hostname,
                Port = port,
                UseTls = true,
                ClientCertificate = clientCertificate
            };
            return new PssClient(config);
        }
    }
}
